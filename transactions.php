<?php
require_once 'database.php';

// Initialize messages and session
session_start();
$successMessage = $errorMessage = "";

// Handle initial transaction creation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_transaction'])) {
    try {
        // Get current year
        $year = date('Y');
        
        // Insert into transactions table with just the basic info
        $stmt = $conn->prepare("INSERT INTO transactions (transaction_date, employee_id) VALUES (?, ?)");
        $stmt->execute([
            $_POST['transaction_date'],
            $_POST['employee_id']
        ]);
        
        $transaction_id = $conn->lastInsertId();
        
        // Generate transaction reference (YEAR-ID format)
        $transaction_ref = sprintf("%s-%06d", $year, $transaction_id);
        
        // Update the transaction with the generated reference
        $stmt = $conn->prepare("UPDATE transactions SET transaction_ref = ? WHERE transaction_id = ?");
        $stmt->execute([$transaction_ref, $transaction_id]);
        
        $successMessage = "Journal entry created successfully!";
        
    } catch(PDOException $e) {
        $errorMessage = "Error: " . $e->getMessage();
    }
}

// Handle adding transaction details
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_detail'])) {
    try {
        // Insert into transaction_details table
        $stmt = $conn->prepare("INSERT INTO transaction_details (transaction_id, account_code, transaction_description, amount) VALUES (?, ?, ?, ?)");
        $stmt->execute([
            $_POST['transaction_id'],
            $_POST['account_code'],
            $_POST['transaction_description'],
            $_POST['amount']
        ]);
        
        $successMessage = "Journal line item added successfully!";
    } catch(PDOException $e) {
        $errorMessage = "Error: " . $e->getMessage();
    }
}

// Handle updating transaction details
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_detail'])) {
    try {
        // Update transaction_details table
        $stmt = $conn->prepare("UPDATE transaction_details 
                              SET account_code = ?, 
                                  transaction_description = ?, 
                                  amount = ? 
                              WHERE detail_id = ?");
        $stmt->execute([
            $_POST['account_code'],
            $_POST['transaction_description'],
            $_POST['amount'],
            $_POST['detail_id']
        ]);
        
        $successMessage = "Journal line item updated successfully!";
    } catch(PDOException $e) {
        $errorMessage = "Error: " . $e->getMessage();
    }
}

// Fetch all transactions
$transactions = $conn->query("SELECT t.*, e.emp_name 
    FROM transactions t
    JOIN employees e ON t.employee_id = e.id
    ORDER BY t.transaction_id DESC")->fetchAll();

// Fetch employees for dropdown
$employees = $conn->query("SELECT id, emp_name FROM employees")->fetchAll();

// Fetch accounts for dropdown
$accounts = $conn->query("SELECT account_code, account_description FROM accounts")->fetchAll();

// Handle fetching transaction details
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['get_details'])) {
    try {
        $stmt = $conn->prepare("
            SELECT td.*, a.account_description 
            FROM transaction_details td
            JOIN accounts a ON td.account_code = a.account_code
            WHERE td.transaction_id = ?
        ");
        $stmt->execute([$_GET['transaction_id']]);
        $details = $stmt->fetchAll();
        
        // Return JSON response
        header('Content-Type: application/json');
        echo json_encode($details);
        exit;
    } catch(PDOException $e) {
        header('HTTP/1.1 500 Internal Server Error');
        echo json_encode(['error' => $e->getMessage()]);
        exit;
    }
}
// Assign transaction ID for later use
$current_transaction_id = isset($_GET['transaction_id']) ? $_GET['transaction_id'] : 0;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>General Ledger - BIRI District Hospital</title>
    <link rel="icon" type="image/x-icon" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;500;600&family=Source+Sans+Pro:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #1a237e;
            --secondary-color: #283593;
            --accent-color: #3949ab;
            --success-color: #2e7d32;
            --warning-color: #f57f17;
            --danger-color: #c62828;
            --background-color: #f5f7fa;
            --card-color: #ffffff;
            --text-primary: #263238;
            --text-secondary: #546e7a;
        }

        body {
            background-color: var(--background-color);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            color: var(--text-primary);
            font-size: 0.875rem;
            line-height: 1.5;
        }
        
        .container {
            max-width: 1400px;
            padding: 1.5rem;
        }
        
        h2 {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1.25rem;
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 0.75rem;
            font-size: 1.5rem;
        }
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            background: var(--card-color);
            margin-bottom: 1.25rem;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .form-label {
            font-weight: 500;
            color: var(--text-primary);
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.03em;
        }
        
        .form-control, .form-select {
            border: 1px solid #e0e4e8;
            padding: 0.625rem;
            font-size: 0.875rem;
            font-family: 'Inter', sans-serif;
            border-radius: 6px;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(57, 73, 171, 0.1);
        }
        
        .table {
            margin: 0;
            font-size: 0.8125rem;
        }
        
        .table th {
            background-color: var(--secondary-color);
            color: white;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.03em;
            padding: 0.875rem;
        }
        
        .table td {
            vertical-align: middle;
            padding: 0.875rem;
            border-bottom: 1px solid #e0e4e8;
            color: var(--text-primary);
        }
        
        .badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
            font-weight: 500;
        }
        
        .btn {
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.03em;
            padding: 0.625rem 1.25rem;
            border-radius: 6px;
            font-size: 0.8125rem;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background-color: var(--accent-color);
            border: none;
        }
        
        .btn-primary:hover {
            background-color: #303f9f;
            transform: translateY(-1px);
        }
        
        .modal-content {
            border-radius: 12px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        .modal-header {
            background-color: var(--secondary-color);
            color: white;
            border-radius: 12px 12px 0 0;
            padding: 1rem 1.5rem;
        }
        
        .modal-title {
            font-weight: 500;
            font-size: 1rem;
            letter-spacing: 0.02em;
        }
        
        .input-group-text {
            background-color: var(--secondary-color);
            color: white;
            border: none;
            font-size: 0.875rem;
        }
        
        /* Accounting-specific styles */
        .transaction-code {
            font-family: 'Inter', monospace;
            color: var(--accent-color);
            font-weight: 500;
            font-size: 0.875rem;
        }
        
        .amount-field {
            text-align: right;
            font-family: 'Inter', monospace;
            font-size: 0.875rem;
        }
        
        .account-code {
            font-family: 'Inter', monospace;
            color: var(--text-secondary);
            font-size: 0.8125rem;
        }
        
        .ledger-header {
            background-color: #f8f9fa;
            border-bottom: 2px solid var(--accent-color);
            padding: 0.875rem;
            margin-bottom: 1rem;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2><i class="fas fa-book me-2"></i>General Ledger</h2>

        <!-- Create Transaction Form -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="transaction_date" class="form-label">Transaction Date</label>
                            <input type="date" class="form-control" id="transaction_date" name="transaction_date" required>
                        </div>
                        <div class="col-md-6">
                            <label for="employee_id" class="form-label">Employee</label>
                            <select class="form-select" id="employee_id" name="employee_id" required>
                                <option value="">Select Employee</option>
                                <?php foreach($employees as $employee): ?>
                                    <option value="<?php echo $employee['id']; ?>">
                                        <?php echo htmlspecialchars($employee['emp_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-12">
                            <button type="submit" name="create_transaction" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Create Journal Entry
                            </button>
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-home me-2"></i>Back to Homepage
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Search Bar -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" id="journalSearch" 
                           onkeyup="searchJournalEntries()" 
                           placeholder="Search journal entries...">
                </div>
            </div>
        </div>

        <!-- Transactions Table -->
        <div class="card">
            <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                <table class="table table-hover mb-0">
                    <thead style="position: sticky; top: 0; background: white; z-index: 1;">
                        <tr>
                            <th><i class="fas fa-file-invoice me-2"></i>Reference</th>
                            <th><i class="fas fa-calendar-alt me-2"></i>Date</th>
                            <th><i class="fas fa-user-tie me-2"></i>Employee</th>
                            <th><i class="fas fa-cogs me-2"></i>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($transactions as $transaction): ?>
                            <tr>
                                <td class="transaction-code"><i class="fas fa-hashtag me-2"></i><?php echo htmlspecialchars($transaction['transaction_ref']); ?></td>
                                <td><i class="fas fa-clock me-2"></i><?php echo date('M d, Y', strtotime($transaction['transaction_date'])); ?></td>
                                <td><i class="fas fa-user me-2"></i><?php echo htmlspecialchars($transaction['emp_name']); ?></td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-primary" 
                                            data-bs-toggle="modal" 
                                            data-bs-target="#addDetailModal"
                                            data-transaction-id="<?php echo $transaction['transaction_id']; ?>"
                                            data-transaction-ref="<?php echo $transaction['transaction_ref']; ?>">
                                        <i class="fas fa-plus-circle me-1"></i> Add Detail
                                    </button>
                                    <button type="button" class="btn btn-sm btn-info" 
                                            onclick="viewTransactionDetails(<?php echo $transaction['transaction_id']; ?>)"
                                            data-bs-toggle="modal" 
                                            data-bs-target="#viewDetailsModal">
                                        <i class="fas fa-search-dollar me-1"></i> View
                                    </button>
                                    <a href="update_transdetails.php?id=<?php echo explode('-', $transaction['transaction_ref'])[1]; ?>" 
                                       class="btn btn-sm btn-success">
                                        <i class="fas fa-pen-to-square me-1"></i> Update
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Add Detail Modal -->
        <div class="modal fade" id="addDetailModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-plus-circle me-2"></i>
                            Add Journal Entry Detail - <span id="transaction_ref" class="transaction-code"></span>
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form method="POST" class="needs-validation" novalidate>
                            <input type="hidden" id="transaction_id" name="transaction_id">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">Account</label>
                                    <select class="form-select" name="account_code" required>
                                        <option value="">Select Account</option>
                                        <?php foreach($accounts as $account): ?>
                                            <option value="<?php echo $account['account_code']; ?>">
                                                <?php echo $account['account_code'] . ' - ' . $account['account_description']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Amount</label>
                                    <div class="input-group">
                                        <span class="input-group-text">₱</span>
                                        <input type="number" step="0.01" class="form-control amount-field" 
                                               name="amount" required>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <label class="form-label">Description</label>
                                    <textarea class="form-control" name="transaction_description" 
                                              rows="3" required></textarea>
                                </div>
                            </div>
                            <div class="mt-4">
                                <button type="submit" name="add_detail" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Detail
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- View Details Modal -->
        <div class="modal fade" id="viewDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-list me-2"></i>
                            Journal Entry Details
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Account</th>
                                        <th>Description</th>
                                        <th class="text-end">Amount</th>
                                    </tr>
                                </thead>
                                <tbody id="detailsTableBody"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form validation
        (function () {
            'use strict'
            var forms = document.querySelectorAll('.needs-validation')
            Array.prototype.slice.call(forms).forEach(function (form) {
                form.addEventListener('submit', function (event) {
                    if (!form.checkValidity()) {
                        event.preventDefault()
                        event.stopPropagation()
                    }
                    form.classList.add('was-validated')
                }, false)
            })
        })()

        // Handle modal data
        var addDetailModal = document.getElementById('addDetailModal')
        addDetailModal.addEventListener('show.bs.modal', function (event) {
            var button = event.relatedTarget
            var transactionId = button.getAttribute('data-transaction-id')
            var transactionRef = button.getAttribute('data-transaction-ref')
            
            var modalTransactionId = addDetailModal.querySelector('#transaction_id')
            var modalTransactionRef = addDetailModal.querySelector('#transaction_ref')
            
            modalTransactionId.value = transactionId
            modalTransactionRef.textContent = transactionRef
        })

        // Function to view transaction details
        function viewTransactionDetails(transactionId) {
            fetch(`transactions.php?get_details=1&transaction_id=${transactionId}`)
                .then(response => response.json())
                .then(details => {
                    const tbody = document.getElementById('detailsTableBody')
                    tbody.innerHTML = ''
                    let total = 0
                    
                    details.forEach(detail => {
                        total += parseFloat(detail.amount)
                        tbody.innerHTML += `
                            <tr>
                                <td>${detail.account_description}</td>
                                <td>${detail.transaction_description}</td>
                                <td class="text-end">₱${parseFloat(detail.amount).toFixed(2)}</td>
                            </tr>
                        `
                    })
                    
                    // Add total row
                    tbody.innerHTML += `
                        <tr class="table-light">
                            <td colspan="2" class="text-end fw-bold">Total:</td>
                            <td class="text-end fw-bold">₱${total.toFixed(2)}</td>
                        </tr>
                    `
                })
                .catch(error => console.error('Error:', error))
        }
    </script>
    <script>
        function searchJournalEntries() {
            // Get input value and convert to lowercase
            let input = document.getElementById("journalSearch");
            let filter = input.value.toLowerCase();
            
            // Get all transaction rows
            let rows = document.querySelectorAll(".table tbody tr");
            
            // Loop through rows and hide those that don't match the search query
            rows.forEach(row => {
                let refCode = row.querySelector("td:nth-child(1)").textContent.toLowerCase();
                let date = row.querySelector("td:nth-child(2)").textContent.toLowerCase();
                let employee = row.querySelector("td:nth-child(3)").textContent.toLowerCase();
                
                if (refCode.includes(filter) || date.includes(filter) || employee.includes(filter)) {
                    row.style.display = "";
                } else {
                    row.style.display = "none";
                }
            });
        }
    </script>
</body>
</html>
