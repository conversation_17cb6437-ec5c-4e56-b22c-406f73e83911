<?php

require_once 'database.php';

try {
    // Get current year
    $currentYear = date('Y');
    
    // Get total utilization for current year
    $stmt = $conn->prepare("
        SELECT COALESCE(SUM(td.amount), 0) as total 
        FROM transaction_details td
        INNER JOIN transactions t ON td.transaction_id = t.transaction_id 
        WHERE YEAR(t.transaction_date) = ?
    ");
    $stmt->execute([$currentYear]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode(['totalUtilization' => floatval($result['total'])]);
} catch(PDOException $e) {
    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode(['error' => $e->getMessage()]);
}

?>










