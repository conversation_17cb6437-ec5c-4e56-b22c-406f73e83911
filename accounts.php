<?php
require_once 'database.php';

$successMessage = "";
$errorMessage = "";

// Add Account
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['add_account'])) {
    try {
        $stmt = $conn->prepare("INSERT INTO accounts (account_code, account_description, account_year, account_amount) VALUES (?, ?, ?, ?)");
        $stmt->execute([
            $_POST['account_code'],
            $_POST['account_description'],
            $_POST['account_year'],
            $_POST['account_amount']
        ]);
        $successMessage = "Account added successfully!";
    } catch(PDOException $e) {
        $errorMessage = "Error adding account: " . $e->getMessage();
    }
}

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update_account'])) {
    try {
        $stmt = $conn->prepare("UPDATE accounts SET account_code=?, account_description=?, account_year=?, account_amount=? WHERE account_code=?");
        $stmt->execute([
            $_POST['account_code'],
            $_POST['account_description'],
            $_POST['account_year'],
            $_POST['account_amount'],
            $_POST['id']
        ]);
        $successMessage = "Account updated successfully!";
    } catch(PDOException $e) {
        $errorMessage = "Error: " . $e->getMessage();
    }
}

// Search Account
$searchQuery = isset($_POST['search']) ? $_POST['search'] : "";

try {
    $stmt = $conn->prepare("SELECT * FROM accounts WHERE account_code LIKE ? OR account_description LIKE ?");
    $searchParam = "%$searchQuery%";
    $stmt->execute([$searchParam, $searchParam]);
    $result = $stmt;
} catch(PDOException $e) {
    $errorMessage = "Error: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Account Management - BIRI District Hospital</title>
    <link rel="icon" type="image/x-icon" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00b4d8;
            --secondary-color: #0077b6;
            --accent-color: #48cae4;
            --success-color: #20c997;
            --warning-color: #ffd60a;
            --danger-color: #ff6b6b;
            --background-color: #f0f9ff;
            --card-color: #ffffff;
            --text-primary: #023e8a;
            --text-secondary: #0096c7;
        }

        body {
            background-color: var(--background-color);
            font-family: 'Poppins', sans-serif;
            color: var(--text-primary);
            background-image: linear-gradient(45deg, #f0f9ff 25%, #e9f2f9 25%, #e9f2f9 50%, #f0f9ff 50%, #f0f9ff 75%, #e9f2f9 75%, #e9f2f9 100%);
            background-size: 56.57px 56.57px;
        }
        
        .container {
            max-width: 1400px;
            padding: 2rem;
        }
        
        h2 {
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .card {
            border: none;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 180, 216, 0.15);
            padding: 1.5rem;
            background: var(--card-color);
            margin-bottom: 1.5rem;
            backdrop-filter: blur(10px);
            border-top: 3px solid var(--primary-color);
        }
        
        .content-wrapper {
            display: flex;
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .form-section {
            flex: 0 0 35%;
        }
        
        .table-section {
            flex: 0 0 65%;
        }
        
        .form-label {
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
        }
        
        .form-control, .form-select {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 0.75rem;
            transition: all 0.3s ease;
            background-color: rgba(255, 255, 255, 0.9);
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 180, 216, 0.2);
        }
        
        .btn {
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .btn-success {
            background-color: var(--success-color);
            border: none;
        }
        
        .btn-success:hover {
            background-color: #1db385;
        }
        
        .table {
            margin: 0;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .table th {
            background-color: var(--primary-color);
            font-weight: 600;
            color: white;
            border: none;
            padding: 1rem;
        }
        
        .table td {
            vertical-align: middle;
            color: var(--text-primary);
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .btn-warning {
            background-color: var(--warning-color);
            border: none;
            color: var(--text-primary);
        }
        
        .btn-warning:hover {
            background-color: #fcc419;
            color: var(--text-primary);
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        /* Modern scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--background-color);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }
        /* Digital elements */
        .digital-border {
            position: relative;
            overflow: hidden;
        }

        .digital-border::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            animation: borderFlow 2s linear infinite;
        }

        @keyframes borderFlow {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Pulse effect for icons */
        .fa-solid {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="digital-border"><i class="fa-solid fa-money-bill-trend-up me-2"></i>Account Management</h2>

        <?php if ($successMessage): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($successMessage); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
        <?php if ($errorMessage): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($errorMessage); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <!-- Search Form -->
        <div class="card digital-border">
            <form method="POST" class="mb-0">
                <div class="input-group">
                    <span class="input-group-text border-0 bg-light"><i class="fa-solid fa-magnifying-glass"></i></span>
                    <input type="text" name="search" class="form-control border-0 bg-light" placeholder="Search accounts..." value="<?php echo htmlspecialchars($searchQuery); ?>" />
                    <button type="submit" class="btn btn-primary px-4">Search</button>
                </div>
            </form>
        </div>

        <div class="content-wrapper">
            <!-- Add/Edit Account Form -->
            <div class="form-section">
                <div class="card digital-border">
                    <form method="POST" id="accountForm" class="needs-validation" novalidate>
                        <input type="hidden" name="id" id="accountId" />
                        <div class="row g-4">
                            <div class="col-12">
                                <label for="account_code" class="form-label">Account Code</label>
                                <input type="text" name="account_code" class="form-control" id="account_code" required />
                                <div class="invalid-feedback">Please enter the account code.</div>
                            </div>
                            <div class="col-12">
                                <label for="account_description" class="form-label">Description</label>
                                <textarea name="account_description" class="form-control" id="account_description" rows="3" required></textarea>
                                <div class="invalid-feedback">Please enter the account description.</div>
                            </div>
                            <div class="col-12">
                                <label for="account_year" class="form-label">Year</label>
                                <input type="number" name="account_year" class="form-control" id="account_year" required />
                                <div class="invalid-feedback">Please enter the account year.</div>
                            </div>
                            <div class="col-12">
                                <label for="account_amount" class="form-label">Amount</label>
                                <input type="number" step="0.01" name="account_amount" class="form-control" id="account_amount" required />
                                <div class="invalid-feedback">Please enter the account amount.</div>
                            </div>
                        </div>
                        <div class="mt-4 d-flex gap-3">
                            <button type="submit" name="add_account" id="submitButton" class="btn btn-success flex-grow-1">
                                <i class="fa-solid fa-plus me-2"></i>Add Account
                            </button>
                            <a href="index.php" class="btn btn-primary">
                                <i class="fa-solid fa-home me-2"></i>Homepage
                            </a>
                            <button type="button" id="cancelButton" class="btn btn-secondary d-none">
                                <i class="fa-solid fa-times me-2"></i>Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Account List -->
            <div class="table-section">
                <div class="card digital-border table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th><i class="fa-solid fa-hashtag me-2"></i>Code</th>
                                <th><i class="fa-solid fa-file-lines me-2"></i>Description</th>
                                <th><i class="fa-solid fa-calendar me-2"></i>Year</th>
                                <th><i class="fa-solid fa-money-bill me-2"></i>Amount</th>
                                <th><i class="fa-solid fa-gear me-2"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($row = $result->fetch(PDO::FETCH_ASSOC)) { ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($row['account_code']); ?></td>
                                    <td><?php echo htmlspecialchars($row['account_description']); ?></td>
                                    <td><?php echo htmlspecialchars($row['account_year']); ?></td>
                                    <td><?php echo number_format($row['account_amount'], 2); ?></td>
                                    <td>
                                        <button onclick='editAccount(<?php echo json_encode($row, JSON_HEX_APOS | JSON_HEX_QUOT); ?>)' class="btn btn-warning btn-sm">
                                            <i class="fa-solid fa-pen me-1"></i>Edit
                                        </button>
                                    </td>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Form validation
        (function () {
            'use strict'
            var forms = document.querySelectorAll('.needs-validation')
            Array.prototype.slice.call(forms).forEach(function (form) {
                form.addEventListener('submit', function (event) {
                    if (!form.checkValidity()) {
                        event.preventDefault()
                        event.stopPropagation()
                    }
                    form.classList.add('was-validated')
                }, false)
            })
        })()

        // Edit account function
        function editAccount(account) {
            document.getElementById('accountId').value = account.id;
            document.getElementById('account_code').value = account.account_code;
            document.getElementById('account_description').value = account.account_description;
            document.getElementById('account_year').value = account.account_year;
            document.getElementById('account_amount').value = account.account_amount;

            // Change form button to Update
            const submitButton = document.getElementById('submitButton');
            submitButton.innerHTML = '<i class="fa-solid fa-pen me-2"></i>Update Account';
            submitButton.name = 'update_account';

            // Show cancel button
            const cancelButton = document.getElementById('cancelButton');
            cancelButton.classList.remove('d-none');

            // Scroll to form
            document.querySelector('.form-section').scrollIntoView({ behavior: 'smooth' });
        }

        // Cancel button handler
        document.getElementById('cancelButton').addEventListener('click', function() {
            document.getElementById('accountForm').reset();
            document.getElementById('accountId').value = '';
            
            // Reset form button to Add
            const submitButton = document.getElementById('submitButton');
            submitButton.innerHTML = '<i class="fa-solid fa-plus me-2"></i>Add Account';
            submitButton.name = 'add_account';

            // Hide cancel button
            this.classList.add('d-none');

            // Remove validation classes
            document.getElementById('accountForm').classList.remove('was-validated');
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
