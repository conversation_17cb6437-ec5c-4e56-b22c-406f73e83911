<?php
require_once 'database.php';

// Get current year
$currentYear = date('Y');

// Fetch transaction summary data
$transactionSummary = $conn->query("
    SELECT 
        t.transaction_id,
        t.transaction_ref,
        t.transaction_date,
        e.emp_name,
        d.department_name,
        COUNT(td.detail_id) as items_count,
        SUM(td.amount) as total_amount
    FROM transactions t
    JOIN employees e ON t.employee_id = e.id
    JOIN departments d ON e.department_id = d.id
    JOIN transaction_details td ON t.transaction_id = td.transaction_id
    GROUP BY t.transaction_id, t.transaction_ref, t.transaction_date, e.emp_name, d.department_name
    ORDER BY t.transaction_date DESC")->fetchAll();

// Calculate transaction statistics
$totalTransactions = count($transactionSummary);
$totalAmount = 0;
$departmentTotals = [];
$employeeTotals = [];

foreach ($transactionSummary as $trans) {
    $totalAmount += $trans['total_amount'];
    
    // Department statistics
    if (!isset($departmentTotals[$trans['department_name']])) {
        $departmentTotals[$trans['department_name']] = 0;
    }
    $departmentTotals[$trans['department_name']] += $trans['total_amount'];
    
    // Employee statistics
    if (!isset($employeeTotals[$trans['emp_name']])) {
        $employeeTotals[$trans['emp_name']] = 0;
    }
    $employeeTotals[$trans['emp_name']] += $trans['total_amount'];
}

arsort($departmentTotals);
arsort($employeeTotals);

// Fetch transaction details for modal display
$transactionDetails = [];
foreach ($transactionSummary as $trans) {
    $transRef = $trans['transaction_ref'];
    $details = $conn->query("
        SELECT 
            td.account_code,
            a.account_description,
            td.transaction_description,
            td.amount
        FROM transaction_details td
        JOIN accounts a ON td.account_code = a.account_code
        WHERE td.transaction_id = '{$trans['transaction_id']}'
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    $transactionDetails[$transRef] = $details;
}

// Store transaction details in PHP session for modal access
session_start();
$_SESSION['transaction_details'] = $transactionDetails;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transaction Reports - BIRI District Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #2c3e50;
            --secondary-blue: #34495e;
            --accent-blue: #3498db;
            --success-green: #27ae60;
            --light-gray: #f5f6fa;
            --white: #ffffff;
        }
        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--light-gray);
            color: var(--primary-blue);
            line-height: 1.6;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            background-color: var(--white);
            margin-bottom: 1.5rem;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .metric-card {
            background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
            padding: 2rem;
            border-radius: 15px;
            color: var(--white);
            height: 100%;
        }
        .metric-title {
            font-size: 1.1rem;
            text-transform: uppercase;
            letter-spacing: 1.5px;
            margin-bottom: 1.2rem;
            opacity: 0.9;
            font-weight: 600;
        }
        .metric-value {
            font-size: 2.8rem;
            font-weight: 700;
            margin-bottom: 0.8rem;
            line-height: 1.2;
        }
        .metric-subtitle {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        .table {
            border-collapse: separate;
            border-spacing: 0 12px;
            margin-top: -12px;
            width: 100%;
        }
        .table thead th {
            border: none;
            color: var(--primary-blue);
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.9rem;
            padding: 1.2rem;
            background-color: var(--light-gray);
        }
        .table tbody tr {
            background: var(--white);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
            transition: transform 0.2s ease;
            border-radius: 8px;
        }
        .table tbody tr:hover {
            transform: scale(1.01);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .table tbody td {
            padding: 1.2rem;
            border: none;
            vertical-align: middle;
        }
        .btn {
            padding: 0.8rem 1.8rem;
            font-weight: 500;
            border-radius: 10px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
        }
        .btn-primary {
            background-color: var(--accent-blue);
            border: none;
            box-shadow: 0 4px 6px rgba(52, 152, 219, 0.2);
        }
        .btn-primary:hover {
            background-color: var(--primary-blue);
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(44, 62, 80, 0.2);
        }
        .btn-success {
            background-color: var(--success-green);
            border: none;
            box-shadow: 0 4px 6px rgba(39, 174, 96, 0.2);
        }
        .btn-success:hover {
            background-color: #219a52;
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(39, 174, 96, 0.2);
        }
        .card-title {
            color: var(--primary-blue);
            font-weight: 600;
            margin-bottom: 1.8rem;
            font-size: 1.4rem;
        }
        .text-success {
            color: var(--success-green) !important;
        }
        .modal-content {
            border-radius: 15px;
            border: none;
        }
        .modal-header {
            background-color: var(--primary-blue);
            color: var(--white);
            border-radius: 15px 15px 0 0;
            padding: 1.5rem;
        }
        .modal-body {
            padding: 2rem;
        }
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <h2 class="mb-4 d-flex align-items-center">
            <i class="fas fa-chart-line me-3 text-primary"></i>
            <span>Transaction Analytics Dashboard</span>
        </h2>

        <!-- Transaction Overview -->
        <div class="row g-4 mb-5">
            <div class="col-md-4">
                <div class="metric-card">
                    <h6 class="metric-title">Total Transactions</h6>
                    <div class="metric-value"><?php echo $totalTransactions; ?></div>
                    <div class="metric-subtitle">Processed this period</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="metric-card">
                    <h6 class="metric-title">Total Expenditures</h6>
                    <div class="metric-value">₱<?php echo number_format($totalAmount, 2); ?></div>
                    <div class="metric-subtitle">Gross transaction value</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="metric-card">
                    <h6 class="metric-title">Average Expenditure</h6>
                    <div class="metric-value">₱<?php echo $totalTransactions > 0 ? number_format($totalAmount / $totalTransactions, 2) : '0.00'; ?></div>
                    <div class="metric-subtitle">Per transaction</div>
                </div>
            </div>
        </div>

        <!-- Transaction List -->
        <div class="card mb-5">
            <div class="card-body p-4">
                <h4 class="card-title">
                    <i class="fas fa-history me-2"></i>
                    Transaction History
                </h4>
                <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                    <table class="table table-hover" style="table-layout: fixed; width: 100%;">
                        <thead style="position: sticky; top: 0; background: white; z-index: 1;">
                            <tr>
                                <th style="width: 15%">Reference ID</th>
                                <th style="width: 15%">Date</th>
                                <th style="width: 20%">Employee</th>
                                <th style="width: 20%">Department</th>
                                <th style="width: 10%">Items</th>
                                <th style="width: 20%">Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($transactionSummary as $trans): ?>
                                <tr>
                                    <td class="fw-bold text-truncate">
                                        <a href="#" class="text-decoration-none text-primary" 
                                           data-bs-toggle="modal" 
                                           data-bs-target="#transactionDetailsModal" 
                                           data-transaction-ref="<?php echo htmlspecialchars($trans['transaction_ref']); ?>"
                                           title="<?php echo htmlspecialchars($trans['transaction_ref']); ?>">
                                            <?php echo htmlspecialchars($trans['transaction_ref']); ?>
                                        </a>
                                    </td>
                                    <td><?php echo date('M d, Y', strtotime($trans['transaction_date'])); ?></td>
                                    <td class="text-truncate" title="<?php echo htmlspecialchars($trans['emp_name']); ?>">
                                        <?php echo htmlspecialchars($trans['emp_name']); ?>
                                    </td>
                                    <td class="text-truncate" title="<?php echo htmlspecialchars($trans['department_name']); ?>">
                                        <?php echo htmlspecialchars($trans['department_name']); ?>
                                    </td>
                                    <td class="text-center fw-bold"><?php echo $trans['items_count']; ?></td>
                                    <td class="fw-bold text-success">₱<?php echo number_format($trans['total_amount'], 2); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Transaction Details Modal -->
            <div class="modal fade" id="transactionDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-info-circle me-2"></i>
                                Transaction Details
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                                <table class="table" id="detailsTable">
                                    <thead style="position: sticky; top: 0; background: white; z-index: 1;">
                                        <tr>
                                            <th>Account Code</th>
                                            <th>Description</th>
                                            <th>Amount</th>
                                        </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const modal = document.getElementById('transactionDetailsModal');
                    modal.addEventListener('show.bs.modal', function(event) {
                        const button = event.relatedTarget;
                        const transactionRef = button.getAttribute('data-transaction-ref');
                        
                        fetch(`get_transaction_details.php?ref=${transactionRef}&details=1`)
                            .then(response => response.json())
                            .then(data => {
                                const tbody = document.querySelector('#detailsTable tbody');
                                tbody.innerHTML = '';
                                
                                data.forEach(detail => {
                                    tbody.innerHTML += `
                                        <tr>
                                            <td class="fw-semibold">${detail.account_code}</td>
                                            <td>${detail.transaction_description}</td>
                                            <td class="fw-bold text-success">₱${parseFloat(detail.amount).toFixed(2)}</td>
                                        </tr>
                                    `;
                                });
                            });
                    });
                });
            </script>
        </div>

        <!-- Analysis Section -->
        <div class="row g-4">
            <!-- All Departments -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body p-4">
                        <h4 class="card-title mb-4">
                            <i class="fas fa-building me-2 text-primary"></i>
                            All Departments
                        </h4>
                        <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                            <table class="table table-hover">
                                <thead style="position: sticky; top: 0; background: white; z-index: 1;">
                                    <tr>
                                        <th>Department Name</th>
                                        <th>Total Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    // Query to get all departments
                                    $deptQuery = $conn->query("SELECT * FROM departments ORDER BY department_name");
                                    while ($dept = $deptQuery->fetch()) {
                                        $deptAmount = isset($departmentTotals[$dept['department_name']]) 
                                            ? $departmentTotals[$dept['department_name']] 
                                            : 0;
                                    ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($dept['department_name']); ?></td>
                                            <td class="fw-bold">₱<?php echo number_format($deptAmount, 2); ?></td>
                                        </tr>
                                    <?php } ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- All Employees -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body p-4">
                        <h4 class="card-title mb-4">
                            <i class="fas fa-users me-2 text-success"></i>
                            All Employees
                        </h4>
                        <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                            <table class="table table-hover">
                                <thead style="position: sticky; top: 0; background: white; z-index: 1;">
                                    <tr>
                                        <th>Employee Name</th>
                                        <th>Department</th>
                                        <th>Total Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    // Query to get all employees with their departments
                                    $empQuery = $conn->query("
                                        SELECT e.emp_name, d.department_name 
                                        FROM employees e 
                                        JOIN departments d ON e.department_id = d.id 
                                        ORDER BY e.emp_name
                                    ");
                                    while ($emp = $empQuery->fetch()) {
                                        $empAmount = isset($employeeTotals[$emp['emp_name']]) 
                                            ? $employeeTotals[$emp['emp_name']] 
                                            : 0;
                                    ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($emp['emp_name']); ?></td>
                                            <td><?php echo htmlspecialchars($emp['department_name']); ?></td>
                                            <td class="fw-bold">₱<?php echo number_format($empAmount, 2); ?></td>
                                        </tr>
                                    <?php } ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            })

            // Employee search functionality
            document.getElementById('employeeSearch').addEventListener('keyup', function() {
                var input = this.value.toLowerCase();
                var rows = document.querySelectorAll('#employeeTable tbody tr');
                
                rows.forEach(function(row) {
                    var text = row.querySelector('td').textContent.toLowerCase();
                    row.style.display = text.includes(input) ? '' : 'none';
                });
            });

            // Department table sorting
            function sortDepartmentTable(column) {
                var table = document.getElementById('departmentTable');
                var rows = Array.from(table.querySelectorAll('tbody tr'));
                var index = column === 'name' ? 0 : 1;

                rows.sort(function(a, b) {
                    var aValue = a.cells[index].textContent;
                    var bValue = b.cells[index].textContent;
                    
                    if (column === 'amount') {
                        aValue = parseFloat(aValue.replace(/[₱,]/g, ''));
                        bValue = parseFloat(bValue.replace(/[₱,]/g, ''));
                    }
                    
                    return aValue > bValue ? 1 : -1;
                });

                var tbody = table.querySelector('tbody');
                tbody.innerHTML = '';
                rows.forEach(function(row) {
                    tbody.appendChild(row);
                });
            }
        </script>

        <div class="mt-5 d-flex gap-3">
            <a href="reports.php" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Reports
            </a>
            <a href="export_transaction_pdf.php" class="btn btn-success">
                <i class="fas fa-file-pdf me-2"></i>Export Report
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
