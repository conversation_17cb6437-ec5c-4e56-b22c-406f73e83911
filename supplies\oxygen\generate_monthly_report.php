<?php
/**
 * Oxygen Delivery Monthly Report Generator
 */
ob_start();
require_once '../../database.php';
require_once '../../fpdf/fpdf.php';

class OxygenDeliveryReport extends FPDF {
    private $month;
    private $year;
    private $totalAmount = 0;
    private const FONT_FAMILY = 'Times';
    private const STANDARD_FONT_SIZE = 9;
    private const HEADER_FONT_SIZE = 14;
    private const TITLE_FONT_SIZE = 12;
    
    public function __construct($month, $year) {
        parent::__construct('L', 'mm', 'A4');
        $this->month = $month;
        $this->year = $year;
        $this->SetAutoPageBreak(true, 15);
        $this->SetMargins(15, 15, 15); // Set left and right margins to 15mm
    }

    public function generateReport($deliveries) {
        $this->AddPage();
        $this->generateHeader();
        $this->generateTitle();
        $this->generateTableHeader();
        $this->generateDeliveryDetails($deliveries);
        $this->generateSummary();
        $this->generateSignatures();
    }

    private function generateHeader() {
        $pageWidth = $this->GetPageWidth();
        $centerX = $pageWidth / 2;
        
        $this->Image('../../images/pgns.png', $centerX - 65, 10, 20);
        $this->Image('../../images/bdh.png', $centerX + 45, 10, 20);
        
        $this->SetFont(self::FONT_FAMILY, '', 10);
        $this->Cell(0, 5, 'Republic of the Philippines', 0, 1, 'C');
        $this->Cell(0, 5, 'Province of Northern Samar', 0, 1, 'C');
        $this->Cell(0, 5, 'Provincial Health Office', 0, 1, 'C');
        $this->SetFont(self::FONT_FAMILY, 'B', self::HEADER_FONT_SIZE);
        $this->Cell(0, 6, 'BIRI DISTRICT HOSPITAL', 0, 1, 'C');
        $this->SetFont(self::FONT_FAMILY, 'I', self::STANDARD_FONT_SIZE);
        $this->Cell(0, 5, 'Biri Northern Samar', 0, 1, 'C');
    }

    private function generateTitle() {
        $this->Ln(5);
        $this->SetFont(self::FONT_FAMILY, 'B', self::TITLE_FONT_SIZE);
        $monthName = date('F', mktime(0, 0, 0, $this->month, 1));
        $this->Cell(0, 8, "MONTHLY OXYGEN DELIVERY REPORT - $monthName {$this->year}", 0, 1, 'C');
    }

    private function generateTableHeader() {
        $this->Ln(3);
        $this->SetFont(self::FONT_FAMILY, 'B', self::STANDARD_FONT_SIZE);
        $this->SetFillColor(220, 220, 220);
        
        $availableWidth = $this->GetPageWidth() - 30; // Total width minus margins
        $colWidths = [
            $availableWidth * 0.14, // Receipt No.
            $availableWidth * 0.10, // Date
            $availableWidth * 0.18, // Supplier
            $availableWidth * 0.22, // Tank Serial No.
            $availableWidth * 0.10, // Quantity
            $availableWidth * 0.13, // Price
            $availableWidth * 0.13  // Subtotal
        ];
        
        $this->Cell($colWidths[0], 7, 'Receipt No.', 1, 0, 'C', true);
        $this->Cell($colWidths[1], 7, 'Date', 1, 0, 'C', true);
        $this->Cell($colWidths[2], 7, 'Supplier', 1, 0, 'C', true);
        $this->Cell($colWidths[3], 7, 'Tank Serial No.', 1, 0, 'C', true);
        $this->Cell($colWidths[4], 7, 'Quantity', 1, 0, 'C', true);
        $this->Cell($colWidths[5], 7, 'Price', 1, 0, 'C', true);
        $this->Cell($colWidths[6], 7, 'Subtotal', 1, 1, 'C', true);
    }

    private function generateDeliveryDetails($deliveries) {
        $this->SetFont(self::FONT_FAMILY, '', self::STANDARD_FONT_SIZE);
        $availableWidth = $this->GetPageWidth() - 30;
        $colWidths = [
            $availableWidth * 0.14,
            $availableWidth * 0.10,
            $availableWidth * 0.18,
            $availableWidth * 0.22,
            $availableWidth * 0.10,
            $availableWidth * 0.13,
            $availableWidth * 0.13
        ];
        
        foreach ($deliveries as $delivery) {
            $tanks = $this->fetchTanksForDelivery($delivery['id']);
            $firstRow = true;
            $deliverySubtotal = 0;
            
            foreach ($tanks as $tank) {
                if ($this->GetY() > 180) {
                    $this->AddPage();
                    $this->generateTableHeader();
                }
                
                $subtotal = $tank['quantity'] * 450;
                $deliverySubtotal += $subtotal;
                
                if ($firstRow) {
                    $this->Cell($colWidths[0], 6, $delivery['receipt_no'], 1, 0, 'C');
                    $this->Cell($colWidths[1], 6, date('m/d/y', strtotime($delivery['delivery_date'])), 1, 0, 'C');
                    $this->Cell($colWidths[2], 6, $delivery['supplier'], 1, 0, 'L');
                } else {
                    $this->Cell($colWidths[0], 6, '', 1, 0, 'C');
                    $this->Cell($colWidths[1], 6, '', 1, 0, 'C');
                    $this->Cell($colWidths[2], 6, '', 1, 0, 'L');
                }
                
                $this->Cell($colWidths[3], 6, $tank['serial_number'], 1, 0, 'L');
                $this->Cell($colWidths[4], 6, $tank['quantity'], 1, 0, 'C');
                $this->Cell($colWidths[5], 6, '450.00', 1, 0, 'R');
                $this->Cell($colWidths[6], 6, number_format($subtotal, 2), 1, 1, 'R');
                
                $firstRow = false;
            }
            
            $this->SetFont(self::FONT_FAMILY, 'B', self::STANDARD_FONT_SIZE);
            $this->Cell($availableWidth * 0.74, 6, 'Subtotal for Receipt No. ' . $delivery['receipt_no'], 1, 0, 'R');
            $this->Cell($availableWidth * 0.26, 6, number_format($deliverySubtotal, 2), 1, 1, 'R');
            $this->SetFont(self::FONT_FAMILY, '', self::STANDARD_FONT_SIZE);
            
            $this->totalAmount += $deliverySubtotal;
        }
    }

    private function generateSummary() {
        $this->Ln(3);
        $this->SetFont(self::FONT_FAMILY, 'B', self::STANDARD_FONT_SIZE);
        $availableWidth = $this->GetPageWidth() - 30;
        $this->Cell($availableWidth * 0.74, 7, 'TOTAL AMOUNT', 1, 0, 'R');
        $this->Cell($availableWidth * 0.26, 7, number_format($this->totalAmount, 2), 1, 1, 'R');
    }

    private function generateSignatures() {
        $this->Ln(15);
        $this->SetFont(self::FONT_FAMILY, 'B', self::STANDARD_FONT_SIZE);
        $availableWidth = $this->GetPageWidth() - 30;
        $halfWidth = $availableWidth / 2;
        
        $this->Cell($halfWidth, 6, 'Prepared by:', 0, 0, 'L');
        $this->Cell($halfWidth, 6, 'Noted by:', 0, 1, 'L');
        $this->Ln(10);
        
        $this->Cell($halfWidth, 6, 'EDUARDO O. SABANGAN, JR', 0, 0, 'L');
        $this->Cell($halfWidth, 6, 'LUCILLE G. ROMINES, MD, FPCP', 0, 1, 'L');
        $this->SetFont(self::FONT_FAMILY, '', self::STANDARD_FONT_SIZE);
        $this->Cell($halfWidth, 6, 'Supply Officer', 0, 0, 'L');
        $this->Cell($halfWidth, 6, 'Chief of Hospital', 0, 1, 'L');
    }

    private function fetchTanksForDelivery($deliveryId) {
        global $conn;
        $stmt = $conn->prepare("SELECT * FROM oxygen_tanks WHERE delivery_id = ?");
        $stmt->execute([$deliveryId]);
        return $stmt->fetchAll();
    }
}

$month = isset($_GET['month']) ? (int)$_GET['month'] : date('m');
$year = isset($_GET['year']) ? (int)$_GET['year'] : date('Y');

$stmt = $conn->prepare("SELECT * FROM oxygen_deliveries 
                     WHERE MONTH(delivery_date) = ? 
                     AND YEAR(delivery_date) = ? 
                     ORDER BY delivery_date");
$stmt->execute([$month, $year]);
$deliveries = $stmt->fetchAll();

$report = new OxygenDeliveryReport($month, $year);
$report->generateReport($deliveries);

ob_end_clean();
$report->Output();
