<?php
require_once '../../database.php';

// Get delivery ID from URL
$delivery_id = isset($_GET['delivery_id']) ? (int)$_GET['delivery_id'] : 0;

// Fetch delivery details
$delivery = null;
if ($delivery_id > 0) {
    $stmt = $conn->prepare("SELECT * FROM oxygen_deliveries WHERE id = ?");
    $stmt->execute([$delivery_id]);
    $delivery = $stmt->fetch();
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['addTank'])) {
            $serialNumber = trim($_POST['serialNumber']);
            $quantity = (int)$_POST['quantity'];
            $amount = 1625.00; // Default amount for oxygen tanks
            
            // Validate inputs
            if (empty($serialNumber) || $quantity <= 0) {
                throw new Exception("Valid serial number and quantity are required");
            }
            
            // Check if serial number already exists for this delivery
            $checkStmt = $conn->prepare("SELECT COUNT(*) FROM oxygen_tanks WHERE delivery_id = ? AND serial_number = ?");
            $checkStmt->execute([$delivery_id, $serialNumber]);
            $exists = $checkStmt->fetchColumn();
            
            if ($exists > 0) {
                throw new Exception("This serial number has already been added to this delivery");
            }
            
            $stmt = $conn->prepare("INSERT INTO oxygen_tanks (delivery_id, serial_number, quantity, amount) VALUES (?, ?, ?, ?)");
            $stmt->execute([$delivery_id, $serialNumber, $quantity, $amount]);
        }
        
        if (isset($_POST['updateTank'])) {
            $tankId = (int)$_POST['tankId'];
            $quantity = (int)$_POST['quantity'];
            
            if ($quantity <= 0) {
                throw new Exception("Quantity must be greater than 0");
            }
            
            $stmt = $conn->prepare("UPDATE oxygen_tanks SET quantity = ? WHERE id = ? AND delivery_id = ?");
            $stmt->execute([$quantity, $tankId, $delivery_id]);
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Fetch tanks for this delivery
$tanks = [];
if ($delivery_id > 0) {
    $stmt = $conn->prepare("SELECT * FROM oxygen_tanks WHERE delivery_id = ?");
    $stmt->execute([$delivery_id]);
    $tanks = $stmt->fetchAll();
}


// Handle tank deletion
if (isset($_POST['deleteTank'])) {
    try {
        $tankId = (int)$_POST['deleteTank'];
        
        $stmt = $conn->prepare("DELETE FROM oxygen_tanks WHERE id = ? AND delivery_id = ?");
        $stmt->execute([$tankId, $delivery_id]);
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Oxygen Tank Management System for BIRI District Hospital">
    <title>Manage Oxygen Tanks - BIRI District Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <?php if ($delivery): ?>
        <!-- Delivery Details Card -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0"><i class="fas fa-info-circle me-2"></i>Delivery Details</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <p><strong>Receipt No:</strong> <?= htmlspecialchars($delivery['receipt_no']) ?></p>
                    </div>
                    <div class="col-md-4">
                        <p><strong>Date Delivered:</strong> <?= htmlspecialchars($delivery['delivery_date']) ?></p>
                    </div>
                    <div class="col-md-4">
                        <p><strong>Supplier:</strong> <?= htmlspecialchars($delivery['supplier']) ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add Tank Card -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <h3 class="mb-0"><i class="fas fa-plus me-2"></i>Add New Tank</h3>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">Serial Number</label>
                            <input type="text" class="form-control" name="serialNumber" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Quantity</label>
                            <input type="number" class="form-control" name="quantity" min="1" value="1" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Amount</label>
                            <input type="number" class="form-control" name="amount" min="0" step="0.01" value="1625.00" required>
                        </div>
                        <div class="col-12">
                            <button type="submit" name="addTank" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Add Tank
                            </button>
                            <a href="oxindex.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back
                            </a>
                            <a href="generate_delivery_pdf.php?delivery_id=<?= $delivery_id ?>&receipt_no=<?= $delivery['receipt_no'] ?>" class="btn btn-info" target="_blank">
                                <i class="fas fa-file-pdf me-2"></i>Export PDF
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Tanks List -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h3 class="mb-0"><i class="fas fa-list me-2"></i>Tanks List</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Serial Number</th>
                                <th>Quantity</th>
                                <th>Amount</th>
                                <th>Added On</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($tanks as $tank): ?>
                            <tr>
                                <td><?= htmlspecialchars($tank['serial_number']) ?></td>
                                <td>
                                    <form method="POST" class="d-flex align-items-center">
                                        <input type="hidden" name="tankId" value="<?= $tank['id'] ?>">
                                        <input type="number" class="form-control form-control-sm me-2" 
                                               name="quantity" value="<?= $tank['quantity'] ?>" 
                                               style="width: 80px" min="1">
                                        <button type="submit" name="updateTank" class="btn btn-sm btn-warning">
                                            <i class="fas fa-sync-alt"></i>
                                        </button>
                                    </form>
                                </td>
                                <td>₱<?= number_format($tank['amount'], 2) ?></td>
                                <td><?= htmlspecialchars($tank['created_at']) ?></td>
                                <td>
                                    <button class="btn btn-sm btn-danger" onclick="deleteTank(<?= $tank['id'] ?>)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>No delivery selected
        </div>
        <?php endif; ?>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function deleteTank(tankId) {
            if (confirm('Are you sure you want to delete this tank?')) {
                fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'deleteTank=' + tankId
                })
                .then(response => {
                    if(response.ok) {
                        // Find and remove the deleted row from the table
                        const row = document.querySelector(`button[onclick="deleteTank(${tankId})"]`).closest('tr');
                        row.remove();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Failed to delete tank. Please try again.');
                });
            }
        }
    </script>
</body>
</html>
