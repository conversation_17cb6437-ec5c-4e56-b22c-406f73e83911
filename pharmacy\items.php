<?php
require_once '../database.php';

// Create table if not exists
$sql = "CREATE TABLE IF NOT EXISTS items (
    itemid INT AUTO_INCREMENT PRIMARY KEY,
    category INT NOT NULL,
    FOREIGN KEY (category) REFERENCES pharmacategory(categoryid),
    item_type VARCHAR(25) NOT NULL,
    generaldescription TEXT NOT NULL,
    unitmeasure VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";
try {
    $conn->exec($sql);
} catch(PDOException $e) {
    die("Error creating table: " . $e->getMessage());
}

// Get categories for dropdown
$categoryQuery = "SELECT * FROM pharmacategory ORDER BY categorydesc";
$categoryStmt = $conn->query($categoryQuery);
$categories = $categoryStmt->fetchAll(PDO::FETCH_ASSOC);

// INSERT
if (isset($_POST['submit'])) {
    try {
        // Validate item_type value
        $validItemTypes = ['Medical Supply', 'Medicine'];
        $itemType = $_POST['item_type'];
        if (!in_array($itemType, $validItemTypes)) {
            throw new Exception("Invalid item type selected");
        }

        $sql = "INSERT INTO items (category, item_type, generaldescription, unitmeasure) 
                VALUES (?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $_POST['category'],
            $itemType,
            $_POST['generaldescription'],
            $_POST['unitmeasure']
        ]);
        header("Location: " . $_SERVER['PHP_SELF']);
        exit();
    } catch (Exception $e) {
        die("Error: " . $e->getMessage());
    }
}

// UPDATE
if (isset($_POST['update'])) {
    try {
        // Validate item_type value
        $validItemTypes = ['Medical Supply', 'Medicine'];
        $itemType = $_POST['item_type'];
        if (!in_array($itemType, $validItemTypes)) {
            throw new Exception("Invalid item type selected");
        }

        $sql = "UPDATE items SET 
                category = ?,
                item_type = ?,
                generaldescription = ?,
                unitmeasure = ?
                WHERE itemid = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $_POST['category'],
            $itemType,
            $_POST['generaldescription'],
            $_POST['unitmeasure'],
            $_POST['itemid']
        ]);
        header("Location: " . $_SERVER['PHP_SELF']);
        exit();
    } catch (Exception $e) {
        die("Error: " . $e->getMessage());
    }
}

// DELETE
if (isset($_POST['delete'])) {
    try {
        $sql = "DELETE FROM items WHERE itemid = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$_POST['itemid']]);
        header("Location: " . $_SERVER['PHP_SELF']);
        exit();
    } catch (PDOException $e) {
        die("Error deleting item: " . $e->getMessage());
    }
}

// SEARCH
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$sql = "SELECT i.*, p.categorydesc 
        FROM items i 
        JOIN pharmacategory p ON i.category = p.categoryid 
        WHERE i.generaldescription LIKE ? 
        OR p.categorydesc LIKE ? 
        ORDER BY i.created_at DESC";
$stmt = $conn->prepare($sql);
$searchTerm = "%$search%";
$stmt->execute([$searchTerm, $searchTerm]);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pharmacy Items</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 20px auto; }
        .expiring-soon { background-color: #fff3cd; }
        .expired { background-color: #f8d7da; }
    </style>
</head>
<body>
    <!-- Add/Edit Item Modal -->
    <div class="modal fade" id="itemModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="modalTitle">Add New Item</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <div class="row">
                            <input type="hidden" name="itemid" id="itemid">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Category</label>
                                <select name="category" id="category" class="form-select" required>
                                    <?php foreach($categories as $category): ?>
                                        <option value="<?php echo $category['categoryid']; ?>">
                                            <?php echo htmlspecialchars($category['categorydesc']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Item Type</label>
                                <select name="item_type" id="item_type" class="form-select" required>
                                    <option value="Medical Supply">Medical Supply</option>
                                    <option value="Medicine">Medicine</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Description</label>
                                <input type="text" class="form-control" name="generaldescription" id="generaldescription" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Unit Measure</label>
                                <input type="text" class="form-control" name="unitmeasure" id="unitmeasure" required>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary" name="submit" id="submitBtn">Add Item</button>
                        <button type="submit" class="btn btn-warning" name="update" id="updateBtn" style="display: none;">Update Item</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    Are you sure you want to delete this item?
                </div>
                <div class="modal-footer">
                    <form method="POST">
                        <input type="hidden" name="itemid" id="deleteItemId">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="delete" class="btn btn-danger">Delete</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="card shadow-lg rounded-4 border-0">
            <div class="card-header bg-gradient bg-primary bg-opacity-25 border-0 py-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h3 class="mb-0 text-primary">
                        <i class="fas fa-pills me-2"></i>Pharmacy Items
                    </h3>
                    <div>
                        <button type="button" class="btn btn-primary btn-lg rounded-3 shadow-sm" data-bs-toggle="modal" data-bs-target="#itemModal">
                            <i class="fas fa-plus me-2"></i>New Item
                        </button>
                        <a href="pharmacategory.php" class="btn btn-primary btn-lg rounded-3 shadow-sm ms-2">
                            <i class="fas fa-folder-plus me-2"></i>Add Category
                        </a>
                        <a href="../pharmacy/pharmacydashboard.php" class="btn btn-outline-primary btn-lg rounded-3 shadow-sm ms-2">
                            <i class="fas fa-home me-2"></i>Homepage
                        </a>
                    </div>
                </div>

                <!-- Filter Buttons -->
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary" onclick="filterItems('all')">All Items</button>
                    <button type="button" class="btn btn-outline-warning" onclick="filterItems('3months')">Expires in 3 Months</button>
                    <button type="button" class="btn btn-outline-warning" onclick="filterItems('2months')">Expires in 2 Months</button>
                    <button type="button" class="btn btn-outline-warning" onclick="filterItems('1month')">Expires in 1 Month</button>
                    <button type="button" class="btn btn-outline-danger" onclick="filterItems('expired')">Expired Items</button>
                </div>
            </div>

            <div class="card-body">
                <!-- Search Form -->
                <form method="GET" class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text border-0 bg-light">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control border-0 bg-light" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Search items...">
                        <button class="btn btn-primary" type="submit">Search</button>
                    </div>
                </form>

                <!-- Items Table -->
                <div class="table-responsive">
                    <table class="table table-hover align-middle" id="itemsTable">
                        <thead class="bg-light">
                            <tr>
                                <th><i class="fas fa-tag me-2"></i>Category</th>
                                <th><i class="fas fa-pills me-2"></i>Description</th>
                                <th><i class="fas fa-ruler me-2"></i>Unit</th>
                                <th><i class="fas fa-cog me-2"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($results as $row): ?>
                                <td><?php echo htmlspecialchars($row['categorydesc']); ?></td>
                                <td><?php echo htmlspecialchars($row['generaldescription']); ?></td>
                                <td><?php echo htmlspecialchars($row['unitmeasure']); ?></td>
                                <td>
                                    <button class="btn btn-warning btn-sm" onclick="editItem(<?php echo htmlspecialchars(json_encode($row)); ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger btn-sm" onclick="deleteItem(<?php echo $row['itemid']; ?>)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    const itemModal = new bootstrap.Modal(document.getElementById('itemModal'));
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));

    function editItem(item) {
        document.getElementById('modalTitle').textContent = 'Edit Item';
        document.getElementById('itemid').value = item.itemid;
        document.getElementById('category').value = item.category;
        document.getElementById('item_type').value = item.item_type;
        document.getElementById('generaldescription').value = item.generaldescription;
        document.getElementById('unitmeasure').value = item.unitmeasure;
        document.getElementById('unitcost').value = item.unitcost;
        document.getElementById('sellingprice').value = item.sellingprice;
        document.getElementById('barcode').value = item.barcode;
        document.getElementById('expirationdate').value = item.expirationdate;
        document.getElementById('submitBtn').style.display = 'none';
        document.getElementById('updateBtn').style.display = 'block';
        itemModal.show();
    }

    function deleteItem(itemId) {
        document.getElementById('deleteItemId').value = itemId;
        deleteModal.show();
    }

    function filterItems(filter) {
        const rows = document.querySelectorAll('#itemsTable tbody tr');
        rows.forEach(row => {
            const monthsUntilExpiration = parseInt(row.dataset.expiration);
            switch(filter) {
                case 'all':
                    row.style.display = '';
                    break;
                case '3months':
                    row.style.display = (monthsUntilExpiration <= 3 && monthsUntilExpiration > 2) ? '' : 'none';
                    break;
                case '2months':
                    row.style.display = (monthsUntilExpiration <= 2 && monthsUntilExpiration > 1) ? '' : 'none';
                    break;
                case '1month':
                    row.style.display = (monthsUntilExpiration <= 1 && monthsUntilExpiration > 0) ? '' : 'none';
                    break;
                case 'expired':
                    row.style.display = (monthsUntilExpiration <= 0) ? '' : 'none';
                    break;
            }
        });
    }

    // Reset modal when opening for new item
    document.querySelector('[data-bs-target="#itemModal"]').addEventListener('click', () => {
        document.getElementById('modalTitle').textContent = 'Add New Item';
        document.getElementById('itemid').value = '';
        document.getElementById('submitBtn').style.display = 'block';
        document.getElementById('updateBtn').style.display = 'none';
        document.querySelector('#itemModal form').reset();
    });
    </script>
</body>
</html>



