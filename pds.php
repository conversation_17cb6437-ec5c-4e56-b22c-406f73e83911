<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Personal Data Sheet (PDS) - BIRI District Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #16a34a;
            --secondary-color: #15803d;
            --accent-color: #22c55e;
            --background-color: #f8fafc;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--background-color);
            margin: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .page-header {
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .section {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .warning-box {
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
            color: #856404;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1.5rem;
        }

        .guide-text {
            color: #6b7280;
            font-style: italic;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .form-control {
            border-radius: 5px;
            border: 1px solid #d1d5db;
            padding: 0.5rem;
        }

        .grid-2, .grid-3 {
            display: grid;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .grid-2 { grid-template-columns: repeat(2, 1fr); }
        .grid-3 { grid-template-columns: repeat(3, 1fr); }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1.5rem;
        }

        th, td {
            border: 1px solid #e5e7eb;
            padding: 0.75rem;
        }

        th {
            background-color: #f3f4f6;
            font-weight: 600;
        }

        .btn-submit {
            background-color: var(--primary-color);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.3s;
        }

        .btn-submit:hover {
            background-color: var(--secondary-color);
        }

        @media (max-width: 768px) {
            .grid-2, .grid-3 {
                grid-template-columns: 1fr;
            }
            
            body {
                margin: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <h2><i class="fas fa-file-alt me-2"></i>Personal Data Sheet</h2>
            <p class="mb-0">BIRI District Hospital Employee Information Form</p>
        </div>
        <form id="pdsForm" class="needs-validation" novalidate>
            <!-- Personal Information Section -->
            <div class="section">
                <h3><i class="fas fa-user me-2"></i>Personal Information</h3>
                <div class="grid-3">
                    <div class="form-group">
                        <label class="form-label" for="surname">Surname</label>
                        <input type="text" class="form-control" id="surname" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="firstName">First Name</label>
                        <input type="text" class="form-control" id="firstName" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="middleName">Middle Name</label>
                        <input type="text" class="form-control" id="middleName">
                    </div>
                </div>

                <div class="grid-2">
                    <div class="form-group">
                        <label class="form-label" for="birthDate">Date of Birth</label>
                        <input type="date" class="form-control" id="birthDate" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="placeOfBirth">Place of Birth</label>
                        <input type="text" class="form-control" id="placeOfBirth" required>
                    </div>
                </div>
            </div>

            <!-- Contact Information Section -->
            <div class="section">
                <h3><i class="fas fa-address-book me-2"></i>Contact Information</h3>
                <div class="form-group">
                    <label class="form-label" for="residentialAddress">Residential Address</label>
                    <input type="text" class="form-control" id="residentialAddress" required>
                </div>
                <div class="grid-2">
                    <div class="form-group">
                        <label class="form-label" for="email">Email Address</label>
                        <input type="email" class="form-control" id="email" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="mobile">Mobile Number</label>
                        <input type="tel" class="form-control" id="mobile" required>
                    </div>
                </div>
            </div>

            <!-- Education Background Section -->
            <div class="section">
                <h3><i class="fas fa-graduation-cap me-2"></i>Educational Background</h3>
                <div id="educationFields">
                    <div class="education-entry">
                        <div class="grid-3">
                            <div class="form-group">
                                <label class="form-label">Level</label>
                                <select class="form-select" required>
                                    <option value="">Select Level</option>
                                    <option value="elementary">Elementary</option>
                                    <option value="secondary">Secondary</option>
                                    <option value="tertiary">Tertiary</option>
                                    <option value="graduate">Graduate Studies</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">School Name</label>
                                <input type="text" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Year Graduated</label>
                                <input type="number" class="form-control" required>
                            </div>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-secondary" onclick="addEducationField()">
                    <i class="fas fa-plus me-2"></i>Add Education
                </button>
            </div>

            <!-- Work Experience Section -->
            <div class="section">
                <h3><i class="fas fa-briefcase me-2"></i>Work Experience</h3>
                <div id="workFields">
                    <div class="work-entry">
                        <div class="grid-2">
                            <div class="form-group">
                                <label class="form-label">Position Title</label>
                                <input type="text" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Department/Agency</label>
                                <input type="text" class="form-control" required>
                            </div>
                        </div>
                        <div class="grid-2">
                            <div class="form-group">
                                <label class="form-label">Start Date</label>
                                <input type="date" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">End Date</label>
                                <input type="date" class="form-control">
                            </div>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-secondary" onclick="addWorkField()">
                    <i class="fas fa-plus me-2"></i>Add Work Experience
                </button>
            </div>

            <!-- Submit Button -->
            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-submit">
                    <i class="fas fa-save me-2"></i>Submit PDS
                </button>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form validation
        (function () {
            'use strict'
            var forms = document.querySelectorAll('.needs-validation')
            Array.prototype.slice.call(forms)
                .forEach(function (form) {
                    form.addEventListener('submit', function (event) {
                        if (!form.checkValidity()) {
                            event.preventDefault()
                            event.stopPropagation()
                        }
                        form.classList.add('was-validated')
                    }, false)
                })
        })()

        // Add education field
        function addEducationField() {
            const template = document.querySelector('.education-entry').cloneNode(true);
            template.querySelectorAll('input').forEach(input => input.value = '');
            template.querySelectorAll('select').forEach(select => select.selectedIndex = 0);
            document.getElementById('educationFields').appendChild(template);
        }

        // Add work experience field
        function addWorkField() {
            const template = document.querySelector('.work-entry').cloneNode(true);
            template.querySelectorAll('input').forEach(input => input.value = '');
            document.getElementById('workFields').appendChild(template);
        }
    </script>
</body>
</html>
