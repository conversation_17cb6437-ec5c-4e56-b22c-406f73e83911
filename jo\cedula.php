<?php
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json');

require_once '../database.php';

// Create cedula table if not exists
try {
    $sql = "CREATE TABLE IF NOT EXISTS cedula (
        id INT AUTO_INCREMENT PRIMARY KEY,
        profile_id INT,
        cedula_number VARCHAR(50) NOT NULL,
        date_issued DATE NOT NULL,
        place_issued VARCHAR(255) NOT NULL,
        FOREIGN KEY (profile_id) REFERENCES jo_profile(profile_id)
    )";
    $conn->exec($sql);
} catch(PDOException $e) {
    $response = ['status' => 'error', 'message' => 'Error creating table: ' . $e->getMessage()];
    echo json_encode($response);
    exit;
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (isset($data['save_cedula'])) {
        $profile_id = $data['profile_id'];
        $cedula_number = $data['cedula_number'];
        $date_issued = $data['date_issued'];
        $place_issued = $data['place_issued'];
        
        try {
            $sql = "INSERT INTO cedula (profile_id, cedula_number, date_issued, place_issued) 
                    VALUES (?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$profile_id, $cedula_number, $date_issued, $place_issued]);
            $response = ['status' => 'success', 'message' => 'Cedula information saved successfully'];
            echo json_encode($response);
        } catch(PDOException $e) {
            $response = ['status' => 'error', 'message' => $e->getMessage()];
            echo json_encode($response);
        }
    }
    
    if (isset($data['update_cedula'])) {
        $id = $data['cedula_id'];
        $cedula_number = $data['cedula_number'];
        $date_issued = $data['date_issued'];
        $place_issued = $data['place_issued'];
        
        try {
            $sql = "UPDATE cedula SET cedula_number=?, date_issued=?, place_issued=? WHERE id=?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$cedula_number, $date_issued, $place_issued, $id]);
            $response = ['status' => 'success', 'message' => 'Cedula information updated successfully'];
            echo json_encode($response);
        } catch(PDOException $e) {
            $response = ['status' => 'error', 'message' => $e->getMessage()];
            echo json_encode($response);
        }
    }
}

// Initialize search variable
$search = isset($_GET['search']) ? $_GET['search'] : '';

try {
    $sql = "SELECT c.*, p.jo_name 
            FROM cedula c 
            JOIN jo_profile p ON c.profile_id = p.profile_id";
    
    if ($search) {
        $sql .= " WHERE p.jo_name LIKE ? OR c.cedula_number LIKE ?";
        $searchParam = "%$search%";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$searchParam, $searchParam]);
    } else {
        $stmt = $conn->prepare($sql);
        $stmt->execute();
    }
    
    $cedula_records = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $response = ['status' => 'success', 'data' => $cedula_records];
    echo json_encode($response);
} catch(PDOException $e) {
    $response = ['status' => 'error', 'message' => $e->getMessage()];
    echo json_encode($response);
}

// Get list of JO profiles for dropdown
try {
    $stmt = $conn->query("SELECT profile_id, jo_name FROM jo_profile ORDER BY jo_name");
    $profiles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $response = ['status' => 'success', 'data' => $profiles];
    echo json_encode($response);
} catch(PDOException $e) {
    $response = ['status' => 'error', 'message' => $e->getMessage()];
    echo json_encode($response);
}
$conn = null;
?>
