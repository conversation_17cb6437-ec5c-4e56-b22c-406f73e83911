<?php
require_once('../fpdf/fpdf.php');
require_once '../database.php';

class PDF extends FPDF {
    function Header() {
        // This function intentionally left empty to handle headers manually
    }

    function PrintCertificate($y_offset = 0) {
        // Set 8.5x13 dimensions (215.9mm x 330.2mm)
        $pageWidth = 215.9;
        $pageHeight = 330.2;
        $this->Set<PERSON>argins(10, 10, 10); // Adjust margins for 8.5x13

        // Calculate center positions
        $centerX = $pageWidth / 2;

        // Modern header section with 8.5x13 spacing
        $this->SetFont('Arial', '', 9);
        $this->SetY(15 + $y_offset);
        
        // Add header logos with adjusted positions
        if (file_exists('../images/pgns.png') && file_exists('../images/bdh.png')) {
            $this->Image('../images/pgns.png', $centerX - 55, 15 + $y_offset, 25);
            $this->Image('../images/bdh.png', $centerX + 30, 15 + $y_offset, 25);
        }

        // Modern header section with 8.5x13 spacing
        $this->SetFont('Arial', '', 9);
        $this->SetY(15 + $y_offset);
        $this->Cell(0, 5, 'Republic of the Philippines', 0, 1, 'C');
        $this->Cell(0, 5, 'Province of Northern Samar', 0, 1, 'C');
        $this->Cell(0, 5, 'Provincial Health Office', 0, 1, 'C');
        
        // Hospital name with adjusted size
        $this->SetFont('Arial', 'B', 12);
        $this->Ln(2);
        $this->Cell(0, 6, 'BIRI DISTRICT HOSPITAL', 0, 1, 'C');
        
        $this->SetFont('Arial', '', 9);
        $this->Cell(0, 5, 'Biri Northern Samar', 0, 1, 'C');
        
        // Title with adjusted spacing
        $this->Ln(6);
        $this->SetFont('Arial', 'B', 11);
        $this->Cell(0, 6, 'CERTIFICATE OF APPEARANCE', 0, 1, 'C');
        $this->Ln(4);

        // Content with adjusted margins for 8.5x13
        $this->SetLeftMargin(30);
        $this->SetRightMargin(30);

        $this->SetFont('Arial', 'B', 9);
        $this->Cell(0, 6, 'TO WHOM IT MAY CONCERN:', 0, 1, 'L');

        $this->Ln(3);
        $this->SetFont('Arial', '', 9);
        $this->MultiCell(0, 6, 'This is to certify that ___________________________________ has appeared in this office from ________ to ________ for the purpose of _____________________________________________________________.', 0, 'J');

        $this->Ln(4);
        $this->MultiCell(0, 6, 'This certification is issued upon the request of the above-named person for whatever legal purpose it may serve.', 0, 'J');

        $this->Ln(4);
        $this->Cell(0, 6, 'Issued this _____ day of _____________ at Biri, Northern Samar.', 0, 1, 'L');

        // Signature section with adjusted spacing
        $this->Ln(20);
        $this->SetFont('Arial', 'B', 9);
        $this->Cell(0, 4, 'LUCILLE GALLEGO-ROMINES, MD, FPCP', 0, 1, 'L');
        $this->SetFont('Arial', '', 9);
        $this->Cell(0, 4, 'Chief of Hospital', 0, 1, 'L');
        
      
    }

    function AliasNbPages($alias = '{nb}') {
        $this->AliasNbPages = $alias;
    }
}

// Initialize PDF with 8.5x13 format (215.9mm x 330.2mm)
$pdf = new PDF('P', 'mm', array(215.9, 330.2));
$pdf->AddPage();

// Print two copies on one 8.5x13 page with adjusted spacing
$pdf->PrintCertificate(0);  // First copy at top
$pdf->PrintCertificate(165);  // Second copy adjusted for 8.5x13

// Generate PDF
$pdf->Output('I', 'certificate_of_appearance.pdf', true);
