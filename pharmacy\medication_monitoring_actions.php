<?php
require_once '../database.php';
header('Content-Type: application/json');

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['action'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
    exit;
}

$action = $input['action'];

try {
    switch ($action) {
        case 'acknowledge_alert':
            if (!isset($input['alert_id'])) {
                throw new Exception('Alert ID is required');
            }
            
            $stmt = $conn->prepare("UPDATE medication_monitoring_alerts 
                                   SET is_acknowledged = 1, acknowledged_at = NOW() 
                                   WHERE alert_id = ?");
            $stmt->execute([$input['alert_id']]);
            
            echo json_encode(['success' => true, 'message' => 'Alert acknowledged']);
            break;
            
        case 'acknowledge_all_alerts':
            $stmt = $conn->prepare("UPDATE medication_monitoring_alerts 
                                   SET is_acknowledged = 1, acknowledged_at = NOW() 
                                   WHERE is_acknowledged = 0 AND is_resolved = 0");
            $stmt->execute();
            
            $count = $stmt->rowCount();
            echo json_encode(['success' => true, 'message' => "$count alerts acknowledged"]);
            break;
            
        case 'resolve_alert':
            if (!isset($input['alert_id']) || !isset($input['resolution_notes'])) {
                throw new Exception('Alert ID and resolution notes are required');
            }
            
            $stmt = $conn->prepare("UPDATE medication_monitoring_alerts 
                                   SET is_resolved = 1, resolved_at = NOW(), resolution_notes = ? 
                                   WHERE alert_id = ?");
            $stmt->execute([$input['resolution_notes'], $input['alert_id']]);
            
            echo json_encode(['success' => true, 'message' => 'Alert resolved']);
            break;
            
        case 'create_monitoring':
            if (!isset($input['patientid']) || !isset($input['itemid']) || !isset($input['rule_id'])) {
                throw new Exception('Patient ID, Item ID, and Rule ID are required');
            }
            
            // Get rule details
            $rule_stmt = $conn->prepare("SELECT frequency_days FROM medication_monitoring_rules WHERE rule_id = ?");
            $rule_stmt->execute([$input['rule_id']]);
            $rule = $rule_stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$rule) {
                throw new Exception('Invalid rule ID');
            }
            
            // Calculate next monitoring date
            $next_date = date('Y-m-d', strtotime('+' . $rule['frequency_days'] . ' days'));
            
            $stmt = $conn->prepare("INSERT INTO patient_medication_monitoring 
                                   (patientid, itemid, rule_id, transaction_id, start_date, next_monitoring_date, 
                                    priority_level, prescribing_doctor, monitoring_notes) 
                                   VALUES (?, ?, ?, ?, CURDATE(), ?, ?, ?, ?)");
            
            $stmt->execute([
                $input['patientid'],
                $input['itemid'],
                $input['rule_id'],
                $input['transaction_id'] ?? null,
                $next_date,
                $input['priority_level'] ?? 'Medium',
                $input['prescribing_doctor'] ?? null,
                $input['monitoring_notes'] ?? ''
            ]);
            
            $monitoring_id = $conn->lastInsertId();
            
            // Create initial alert if needed
            $this->createMonitoringAlert($monitoring_id, 'Due', 'Medium', 
                'Monitoring scheduled for ' . date('M d, Y', strtotime($next_date)));
            
            echo json_encode(['success' => true, 'message' => 'Monitoring created', 'monitoring_id' => $monitoring_id]);
            break;
            
        case 'update_monitoring_status':
            if (!isset($input['monitoring_id']) || !isset($input['status'])) {
                throw new Exception('Monitoring ID and status are required');
            }
            
            $stmt = $conn->prepare("UPDATE patient_medication_monitoring 
                                   SET monitoring_status = ?, updated_at = NOW() 
                                   WHERE monitoring_id = ?");
            $stmt->execute([$input['status'], $input['monitoring_id']]);
            
            echo json_encode(['success' => true, 'message' => 'Monitoring status updated']);
            break;
            
        case 'record_monitoring_result':
            if (!isset($input['monitoring_id']) || !isset($input['monitoring_type'])) {
                throw new Exception('Monitoring ID and type are required');
            }
            
            // Insert monitoring history
            $stmt = $conn->prepare("INSERT INTO medication_monitoring_history 
                                   (monitoring_id, monitoring_date, monitoring_type, monitoring_results, 
                                    performed_by, notes, follow_up_required, follow_up_date) 
                                   VALUES (?, CURDATE(), ?, ?, ?, ?, ?, ?)");
            
            $stmt->execute([
                $input['monitoring_id'],
                $input['monitoring_type'],
                json_encode($input['monitoring_results'] ?? []),
                $input['performed_by'] ?? 'Pharmacist',
                $input['notes'] ?? '',
                $input['follow_up_required'] ?? false,
                $input['follow_up_date'] ?? null
            ]);
            
            // Update patient monitoring record
            $rule_stmt = $conn->prepare("SELECT mmr.frequency_days 
                                       FROM patient_medication_monitoring pmm
                                       JOIN medication_monitoring_rules mmr ON pmm.rule_id = mmr.rule_id
                                       WHERE pmm.monitoring_id = ?");
            $rule_stmt->execute([$input['monitoring_id']]);
            $rule = $rule_stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($rule) {
                $next_date = date('Y-m-d', strtotime('+' . $rule['frequency_days'] . ' days'));
                
                $update_stmt = $conn->prepare("UPDATE patient_medication_monitoring 
                                             SET last_monitoring_date = CURDATE(), 
                                                 next_monitoring_date = ?, 
                                                 monitoring_results = ?,
                                                 updated_at = NOW()
                                             WHERE monitoring_id = ?");
                
                $update_stmt->execute([
                    $next_date,
                    json_encode($input['monitoring_results'] ?? []),
                    $input['monitoring_id']
                ]);
            }
            
            echo json_encode(['success' => true, 'message' => 'Monitoring result recorded']);
            break;
            
        case 'check_drug_interactions':
            if (!isset($input['medications'])) {
                throw new Exception('Medications list is required');
            }
            
            // Simple interaction checking (can be expanded with a proper drug interaction database)
            $interactions = [];
            $medications = $input['medications'];
            
            // Example interaction checks
            if (in_array('Digoxin', $medications) && in_array('Amiodarone', $medications)) {
                $interactions[] = [
                    'severity' => 'High',
                    'drugs' => ['Digoxin', 'Amiodarone'],
                    'description' => 'Amiodarone increases digoxin levels. Monitor digoxin levels closely.'
                ];
            }
            
            if (in_array('Warfarin', $medications) && in_array('Phenytoin', $medications)) {
                $interactions[] = [
                    'severity' => 'Medium',
                    'drugs' => ['Warfarin', 'Phenytoin'],
                    'description' => 'Phenytoin may affect warfarin metabolism. Monitor INR closely.'
                ];
            }
            
            echo json_encode(['success' => true, 'interactions' => $interactions]);
            break;
            
        case 'generate_monitoring_alerts':
            // Check for overdue monitoring
            $overdue_stmt = $conn->prepare("SELECT monitoring_id, patientid, next_monitoring_date 
                                          FROM patient_medication_monitoring 
                                          WHERE monitoring_status = 'Active' 
                                          AND next_monitoring_date < CURDATE()
                                          AND monitoring_id NOT IN (
                                              SELECT monitoring_id FROM medication_monitoring_alerts 
                                              WHERE alert_type = 'Overdue' AND is_resolved = 0
                                          )");
            $overdue_stmt->execute();
            $overdue_monitoring = $overdue_stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $alerts_created = 0;
            foreach ($overdue_monitoring as $monitoring) {
                $days_overdue = (strtotime('now') - strtotime($monitoring['next_monitoring_date'])) / (60 * 60 * 24);
                
                $alert_stmt = $conn->prepare("INSERT INTO medication_monitoring_alerts 
                                            (monitoring_id, patientid, alert_type, alert_priority, alert_message) 
                                            VALUES (?, ?, 'Overdue', 'High', ?)");
                
                $message = "Monitoring is " . floor($days_overdue) . " days overdue";
                $alert_stmt->execute([$monitoring['monitoring_id'], $monitoring['patientid'], $message]);
                $alerts_created++;
            }
            
            echo json_encode(['success' => true, 'message' => "$alerts_created alerts generated"]);
            break;
            
        default:
            throw new Exception('Unknown action: ' . $action);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

// Helper function to create monitoring alerts
function createMonitoringAlert($monitoring_id, $alert_type, $priority, $message) {
    global $conn;
    
    // Get patient ID from monitoring record
    $stmt = $conn->prepare("SELECT patientid FROM patient_medication_monitoring WHERE monitoring_id = ?");
    $stmt->execute([$monitoring_id]);
    $patient = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($patient) {
        $alert_stmt = $conn->prepare("INSERT INTO medication_monitoring_alerts 
                                    (monitoring_id, patientid, alert_type, alert_priority, alert_message) 
                                    VALUES (?, ?, ?, ?, ?)");
        $alert_stmt->execute([$monitoring_id, $patient['patientid'], $alert_type, $priority, $message]);
    }
}
?>
