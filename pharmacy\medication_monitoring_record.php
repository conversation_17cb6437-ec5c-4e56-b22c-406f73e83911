<?php
require_once '../database.php';

// Check if monitoring ID is provided
if (!isset($_GET['id'])) {
    header('Location: medication_monitoring_dashboard.php');
    exit();
}

$monitoring_id = $_GET['id'];

// Get monitoring details
$monitoring_sql = "SELECT 
    pmm.*,
    p.patientname,
    p.birthdate,
    p.patient_type,
    i.generaldescription as medication_name,
    mmr.medication_name as rule_medication,
    mmr.monitoring_parameters,
    mmr.lab_tests_required,
    mmr.vital_signs_required,
    mmr.symptoms_to_monitor,
    mmr.therapeutic_range_min,
    mmr.therapeutic_range_max,
    mmr.toxic_level,
    mmc.category_name,
    mmc.risk_level,
    d.doctorname as prescribing_doctor_name
FROM patient_medication_monitoring pmm
JOIN patient p ON pmm.patientid = p.patientid
JOIN items i ON pmm.itemid = i.itemid
JOIN medication_monitoring_rules mmr ON pmm.rule_id = mmr.rule_id
JOIN medication_monitoring_categories mmc ON mmr.category_id = mmc.category_id
LEFT JOIN doctors d ON pmm.prescribing_doctor = d.doctorid
WHERE pmm.monitoring_id = ?";

$monitoring_stmt = $conn->prepare($monitoring_sql);
$monitoring_stmt->execute([$monitoring_id]);
$monitoring = $monitoring_stmt->fetch(PDO::FETCH_ASSOC);

if (!$monitoring) {
    header('Location: medication_monitoring_dashboard.php');
    exit();
}

// Calculate patient age
$age = 0;
if ($monitoring['birthdate']) {
    $birthDate = new DateTime($monitoring['birthdate']);
    $today = new DateTime();
    $age = $birthDate->diff($today)->y;
}

// Parse JSON fields
$monitoring_parameters = json_decode($monitoring['monitoring_parameters'], true) ?? [];
$lab_tests = json_decode($monitoring['lab_tests_required'], true) ?? [];
$vital_signs = json_decode($monitoring['vital_signs_required'], true) ?? [];
$symptoms = json_decode($monitoring['symptoms_to_monitor'], true) ?? [];

// Get monitoring history
$history_sql = "SELECT * FROM medication_monitoring_history 
               WHERE monitoring_id = ? 
               ORDER BY monitoring_date DESC";
$history_stmt = $conn->prepare($history_sql);
$history_stmt->execute([$monitoring_id]);
$history = $history_stmt->fetchAll(PDO::FETCH_ASSOC);

// Handle form submission
if ($_POST) {
    try {
        $monitoring_type = $_POST['monitoring_type'];
        $monitoring_results = [];
        $notes = $_POST['notes'] ?? '';
        $performed_by = $_POST['performed_by'] ?? 'Pharmacist';
        $follow_up_required = isset($_POST['follow_up_required']) ? 1 : 0;
        $follow_up_date = $_POST['follow_up_date'] ?? null;
        
        // Collect monitoring results based on type
        if ($monitoring_type === 'Lab') {
            foreach ($lab_tests as $test) {
                if (isset($_POST['lab_' . str_replace(' ', '_', $test)])) {
                    $monitoring_results[$test] = $_POST['lab_' . str_replace(' ', '_', $test)];
                }
            }
        } elseif ($monitoring_type === 'Vital_Signs') {
            foreach ($vital_signs as $vital) {
                if (isset($_POST['vital_' . str_replace(' ', '_', $vital)])) {
                    $monitoring_results[$vital] = $_POST['vital_' . str_replace(' ', '_', $vital)];
                }
            }
        } elseif ($monitoring_type === 'Symptom_Check') {
            foreach ($symptoms as $symptom) {
                if (isset($_POST['symptom_' . str_replace(' ', '_', $symptom)])) {
                    $monitoring_results[$symptom] = $_POST['symptom_' . str_replace(' ', '_', $symptom)];
                }
            }
        }
        
        // Insert monitoring history record
        $insert_sql = "INSERT INTO medication_monitoring_history 
                      (monitoring_id, monitoring_date, monitoring_type, monitoring_results, 
                       performed_by, notes, follow_up_required, follow_up_date) 
                      VALUES (?, CURDATE(), ?, ?, ?, ?, ?, ?)";
        
        $insert_stmt = $conn->prepare($insert_sql);
        $insert_stmt->execute([
            $monitoring_id,
            $monitoring_type,
            json_encode($monitoring_results),
            $performed_by,
            $notes,
            $follow_up_required,
            $follow_up_date
        ]);
        
        // Update patient monitoring record
        $next_monitoring_date = date('Y-m-d', strtotime('+' . $monitoring['frequency_days'] . ' days'));
        
        $update_sql = "UPDATE patient_medication_monitoring 
                      SET last_monitoring_date = CURDATE(), 
                          next_monitoring_date = ?, 
                          monitoring_results = ?,
                          updated_at = NOW()
                      WHERE monitoring_id = ?";
        
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->execute([
            $next_monitoring_date,
            json_encode($monitoring_results),
            $monitoring_id
        ]);
        
        // Check for critical values and create alerts if needed
        $alert_messages = [];
        
        // Check therapeutic ranges for lab values
        if ($monitoring_type === 'Lab' && !empty($monitoring_results)) {
            foreach ($monitoring_results as $test => $value) {
                if (is_numeric($value)) {
                    $numeric_value = floatval($value);
                    
                    // Check against therapeutic ranges
                    if ($monitoring['therapeutic_range_min'] && $numeric_value < $monitoring['therapeutic_range_min']) {
                        $alert_messages[] = "$test level ($value) is below therapeutic range";
                    } elseif ($monitoring['therapeutic_range_max'] && $numeric_value > $monitoring['therapeutic_range_max']) {
                        $alert_messages[] = "$test level ($value) is above therapeutic range";
                    }
                    
                    // Check for toxic levels
                    if ($monitoring['toxic_level'] && $numeric_value >= $monitoring['toxic_level']) {
                        $alert_messages[] = "CRITICAL: $test level ($value) is in toxic range";
                    }
                }
            }
        }
        
        // Create alerts for critical findings
        foreach ($alert_messages as $message) {
            $priority = strpos($message, 'CRITICAL') !== false ? 'Critical' : 'High';
            $alert_type = strpos($message, 'CRITICAL') !== false ? 'Critical' : 'Lab_Required';
            
            $alert_sql = "INSERT INTO medication_monitoring_alerts 
                         (monitoring_id, patientid, alert_type, alert_priority, alert_message) 
                         VALUES (?, ?, ?, ?, ?)";
            $alert_stmt = $conn->prepare($alert_sql);
            $alert_stmt->execute([$monitoring_id, $monitoring['patientid'], $alert_type, $priority, $message]);
        }
        
        $success_message = "Monitoring record saved successfully. Next monitoring due: " . date('M d, Y', strtotime($next_monitoring_date));
        if (!empty($alert_messages)) {
            $success_message .= " Alerts generated for critical findings.";
        }
        
    } catch (Exception $e) {
        $error_message = "Error saving monitoring record: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Record Medication Monitoring</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #ecf0f1;
            --white: #ffffff;
            --text-dark: #2c3e50;
            --text-muted: #7f8c8d;
            --border-color: #bdc3c7;
            --shadow-light: 0 2px 10px rgba(44, 62, 80, 0.1);
            --shadow-medium: 0 4px 20px rgba(44, 62, 80, 0.15);
        }

        body {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-dark);
        }

        .dashboard-card {
            border: none;
            border-radius: 12px;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
            background: var(--white);
            border-left: 4px solid var(--accent-color);
        }

        .patient-info {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .monitoring-form {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: var(--shadow-light);
        }

        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .btn-primary {
            background: var(--accent-color);
            border-color: var(--accent-color);
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: #2980b9;
            border-color: #2980b9;
            transform: translateY(-1px);
        }

        .badge {
            font-weight: 600;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 0.8rem;
        }

        .monitoring-section {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            background: #f8f9fa;
        }

        .history-item {
            border-left: 4px solid var(--accent-color);
            padding: 12px 16px;
            margin-bottom: 12px;
            background: white;
            border-radius: 0 8px 8px 0;
            box-shadow: var(--shadow-light);
        }

        .therapeutic-range {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 0.85rem;
            margin-top: 8px;
        }

        .toxic-warning {
            background: #ffeaa7;
            border: 1px solid #fdcb6e;
            color: #d63031;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 0.85rem;
            margin-top: 8px;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="dashboard-card card">
                    <div class="card-body py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-0">
                                    <i class="fas fa-clipboard-check me-3 text-primary"></i>
                                    Record Medication Monitoring
                                </h3>
                                <p class="text-muted mb-0 small">Document monitoring results and schedule next assessment</p>
                            </div>
                            <div class="d-flex gap-2">
                                <a href="medication_monitoring_history.php?id=<?php echo $monitoring_id; ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-history me-1"></i>View History
                                </a>
                                <a href="medication_monitoring_dashboard.php" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-arrow-left me-1"></i>Back
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Patient Information -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="patient-info">
                    <div class="row">
                        <div class="col-md-3">
                            <h5 class="mb-1"><?php echo htmlspecialchars($monitoring['patientname']); ?></h5>
                            <small class="opacity-75">
                                <?php echo htmlspecialchars($monitoring['patient_type']); ?>
                                <?php if ($age > 0): ?> • <?php echo $age; ?> years old<?php endif; ?>
                            </small>
                        </div>
                        <div class="col-md-3">
                            <strong>Medication:</strong><br>
                            <span><?php echo htmlspecialchars($monitoring['medication_name']); ?></span>
                        </div>
                        <div class="col-md-3">
                            <strong>Category:</strong><br>
                            <span class="badge bg-<?php echo $monitoring['risk_level'] === 'Critical' ? 'danger' : ($monitoring['risk_level'] === 'High' ? 'warning' : 'info'); ?>">
                                <?php echo htmlspecialchars($monitoring['category_name']); ?>
                            </span>
                        </div>
                        <div class="col-md-3">
                            <strong>Next Due:</strong><br>
                            <span><?php echo date('M d, Y', strtotime($monitoring['next_monitoring_date'])); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monitoring Form -->
        <div class="row">
            <div class="col-md-8">
                <div class="monitoring-form">
                    <h5 class="mb-4">
                        <i class="fas fa-stethoscope me-2 text-primary"></i>Record Monitoring Results
                    </h5>

                    <form method="POST">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Monitoring Type <span class="text-danger">*</span></label>
                                <select class="form-select" name="monitoring_type" id="monitoring_type" required onchange="showMonitoringFields()">
                                    <option value="">Select monitoring type...</option>
                                    <?php if (!empty($lab_tests)): ?>
                                        <option value="Lab">Laboratory Tests</option>
                                    <?php endif; ?>
                                    <?php if (!empty($vital_signs)): ?>
                                        <option value="Vital_Signs">Vital Signs</option>
                                    <?php endif; ?>
                                    <?php if (!empty($symptoms)): ?>
                                        <option value="Symptom_Check">Symptom Assessment</option>
                                    <?php endif; ?>
                                    <option value="Clinical_Assessment">Clinical Assessment</option>
                                    <option value="Patient_Report">Patient Report</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Performed By</label>
                                <input type="text" class="form-control" name="performed_by" value="Pharmacist" required>
                            </div>
                        </div>

                        <!-- Laboratory Tests Section -->
                        <?php if (!empty($lab_tests)): ?>
                            <div id="lab_section" class="monitoring-section" style="display: none;">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-flask me-2"></i>Laboratory Test Results
                                </h6>

                                <?php if ($monitoring['therapeutic_range_min'] || $monitoring['therapeutic_range_max']): ?>
                                    <div class="therapeutic-range mb-3">
                                        <strong>Therapeutic Range:</strong>
                                        <?php if ($monitoring['therapeutic_range_min']): ?>
                                            <?php echo $monitoring['therapeutic_range_min']; ?>
                                        <?php endif; ?>
                                        <?php if ($monitoring['therapeutic_range_min'] && $monitoring['therapeutic_range_max']): ?>
                                            -
                                        <?php endif; ?>
                                        <?php if ($monitoring['therapeutic_range_max']): ?>
                                            <?php echo $monitoring['therapeutic_range_max']; ?>
                                        <?php endif; ?>

                                        <?php if ($monitoring['toxic_level']): ?>
                                            <div class="toxic-warning mt-2">
                                                <i class="fas fa-exclamation-triangle me-1"></i>
                                                <strong>Toxic Level:</strong> ≥<?php echo $monitoring['toxic_level']; ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>

                                <div class="row">
                                    <?php foreach ($lab_tests as $test): ?>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label"><?php echo htmlspecialchars($test); ?></label>
                                            <input type="text" class="form-control" name="lab_<?php echo str_replace(' ', '_', $test); ?>"
                                                   placeholder="Enter result">
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Vital Signs Section -->
                        <?php if (!empty($vital_signs)): ?>
                            <div id="vital_section" class="monitoring-section" style="display: none;">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-heartbeat me-2"></i>Vital Signs
                                </h6>
                                <div class="row">
                                    <?php foreach ($vital_signs as $vital): ?>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label"><?php echo htmlspecialchars($vital); ?></label>
                                            <input type="text" class="form-control" name="vital_<?php echo str_replace(' ', '_', $vital); ?>"
                                                   placeholder="Enter measurement">
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Symptoms Section -->
                        <?php if (!empty($symptoms)): ?>
                            <div id="symptom_section" class="monitoring-section" style="display: none;">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-user-md me-2"></i>Symptom Assessment
                                </h6>
                                <div class="row">
                                    <?php foreach ($symptoms as $symptom): ?>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label"><?php echo htmlspecialchars($symptom); ?></label>
                                            <select class="form-select" name="symptom_<?php echo str_replace(' ', '_', $symptom); ?>">
                                                <option value="">Select status...</option>
                                                <option value="Not Present">Not Present</option>
                                                <option value="Mild">Mild</option>
                                                <option value="Moderate">Moderate</option>
                                                <option value="Severe">Severe</option>
                                            </select>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Notes Section -->
                        <div class="mb-3">
                            <label class="form-label">Clinical Notes</label>
                            <textarea class="form-control" name="notes" rows="4"
                                      placeholder="Enter clinical observations, patient concerns, or additional notes..."></textarea>
                        </div>

                        <!-- Follow-up Section -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="follow_up_required" id="follow_up_required"
                                           onchange="toggleFollowUpDate()">
                                    <label class="form-check-label" for="follow_up_required">
                                        Follow-up Required
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Follow-up Date</label>
                                <input type="date" class="form-control" name="follow_up_date" id="follow_up_date" disabled>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="medication_monitoring_dashboard.php" class="btn btn-outline-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Monitoring Record
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Monitoring History Sidebar -->
            <div class="col-md-4">
                <div class="dashboard-card card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-history me-2"></i>Recent Monitoring History
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($history)): ?>
                            <p class="text-muted small">No previous monitoring records.</p>
                        <?php else: ?>
                            <?php foreach (array_slice($history, 0, 5) as $record): ?>
                                <div class="history-item">
                                    <div class="d-flex justify-content-between align-items-start mb-1">
                                        <span class="badge bg-info"><?php echo str_replace('_', ' ', $record['monitoring_type']); ?></span>
                                        <small class="text-muted"><?php echo date('M d, Y', strtotime($record['monitoring_date'])); ?></small>
                                    </div>
                                    <small class="text-muted">By: <?php echo htmlspecialchars($record['performed_by']); ?></small>
                                    <?php if ($record['notes']): ?>
                                        <p class="small mb-0 mt-1"><?php echo htmlspecialchars(substr($record['notes'], 0, 100)); ?><?php echo strlen($record['notes']) > 100 ? '...' : ''; ?></p>
                                    <?php endif; ?>
                                    <?php if ($record['follow_up_required']): ?>
                                        <div class="mt-1">
                                            <span class="badge bg-warning">Follow-up Required</span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>

                            <?php if (count($history) > 5): ?>
                                <div class="text-center mt-3">
                                    <a href="medication_monitoring_history.php?id=<?php echo $monitoring_id; ?>" class="btn btn-outline-primary btn-sm">
                                        View All History
                                    </a>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function showMonitoringFields() {
            const monitoringType = document.getElementById('monitoring_type').value;

            // Hide all sections
            const sections = ['lab_section', 'vital_section', 'symptom_section'];
            sections.forEach(section => {
                const element = document.getElementById(section);
                if (element) {
                    element.style.display = 'none';
                }
            });

            // Show relevant section
            if (monitoringType === 'Lab') {
                const labSection = document.getElementById('lab_section');
                if (labSection) labSection.style.display = 'block';
            } else if (monitoringType === 'Vital_Signs') {
                const vitalSection = document.getElementById('vital_section');
                if (vitalSection) vitalSection.style.display = 'block';
            } else if (monitoringType === 'Symptom_Check') {
                const symptomSection = document.getElementById('symptom_section');
                if (symptomSection) symptomSection.style.display = 'block';
            }
        }

        function toggleFollowUpDate() {
            const checkbox = document.getElementById('follow_up_required');
            const dateInput = document.getElementById('follow_up_date');

            if (checkbox.checked) {
                dateInput.disabled = false;
                dateInput.required = true;
            } else {
                dateInput.disabled = true;
                dateInput.required = false;
                dateInput.value = '';
            }
        }
    </script>
</body>
</html>
