<?php
require_once 'database.php';

// Get current year, month, and set default values
$currentYear = date('Y');
$currentMonth = date('m');

// Get selected year or use current year as default
$selectedYear = isset($_GET['year']) ? $_GET['year'] : $currentYear;
$selectedMonth = isset($_GET['month']) ? $_GET['month'] : $currentMonth;

// Fetch financial summary data with monthly breakdown
$financialSummary = $conn->query("
    SELECT 
        a.account_code,
        a.account_description,
        a.account_amount as budget,
        COALESCE(SUM(td.amount), 0) as utilized,
        (a.account_amount - COALESCE(SUM(td.amount), 0)) as remaining,
        MONTH(t.transaction_date) as month,
        COALESCE(SUM(CASE WHEN MONTH(t.transaction_date) = MONTH(CURRENT_DATE) THEN td.amount ELSE 0 END), 0) as current_month_spent
    FROM accounts a
    LEFT JOIN transaction_details td ON a.account_code = td.account_code
    LEFT JOIN transactions t ON td.transaction_id = t.transaction_id
    WHERE a.account_year = $selectedYear AND MONTH(t.transaction_date) = $selectedMonth
    GROUP BY a.account_code, a.account_description, a.account_amount, MONTH(t.transaction_date)
    ORDER BY a.account_code, month")->fetchAll(PDO::FETCH_GROUP);

// Calculate enhanced totals
$totalBudget = 0;
$totalUtilized = 0;
$totalRemaining = 0;
$monthlySpending = array_fill(1, 12, 0);
$currentMonthSpent = 0;

foreach ($financialSummary as $accountData) {
    $totalBudget += $accountData[0]['budget'];
    $totalUtilized += array_sum(array_column($accountData, 'utilized'));
    foreach ($accountData as $record) {
        if (isset($record['month'])) {
            $monthlySpending[$record['month']] += $record['utilized'];
        }
        if (isset($record['current_month_spent'])) {
            $currentMonthSpent += $record['current_month_spent'];
        }
    }
}

$totalRemaining = $totalBudget - $totalUtilized;
$utilizationRate = ($totalBudget > 0) ? ($totalUtilized / $totalBudget * 100) : 0;
$monthlyBudget = $totalBudget / 12;
$monthlyUtilizationRate = ($monthlyBudget > 0) ? ($currentMonthSpent / $monthlyBudget * 100) : 0;

// Fetch department expenditure with year-to-date comparison
$departmentExpenditure = $conn->query("
    SELECT 
        d.department_name,
        COALESCE(SUM(td.amount), 0) as total_spent,
        COALESCE(SUM(CASE WHEN YEAR(t.transaction_date) = $currentYear - 1 THEN td.amount ELSE 0 END), 0) as last_year_spent,
        COUNT(DISTINCT t.transaction_id) as transaction_count
    FROM departments d
    LEFT JOIN employees e ON d.id = e.department_id
    LEFT JOIN transactions t ON e.id = t.employee_id
    LEFT JOIN transaction_details td ON t.transaction_id = td.transaction_id
    WHERE YEAR(t.transaction_date) >= $currentYear - 1
    GROUP BY d.id, d.department_name
    ORDER BY total_spent DESC")->fetchAll();

// Fetch monthly trends
$monthlyTrends = $conn->query("
    SELECT 
        MONTH(t.transaction_date) as month,
        COALESCE(SUM(td.amount), 0) as monthly_total
    FROM transactions t
    JOIN transaction_details td ON t.transaction_id = td.transaction_id
    WHERE YEAR(t.transaction_date) = $currentYear
    GROUP BY MONTH(t.transaction_date)
    ORDER BY month")->fetchAll();

// Fetch top spending categories
$topCategories = $conn->query("
    SELECT 
        a.account_description,
        COALESCE(SUM(td.amount), 0) as total_spent
    FROM accounts a
    LEFT JOIN transaction_details td ON a.account_code = td.account_code
    LEFT JOIN transactions t ON td.transaction_id = t.transaction_id
    WHERE YEAR(t.transaction_date) = $currentYear
    GROUP BY a.account_code, a.account_description
    ORDER BY total_spent DESC
    LIMIT 5")->fetchAll();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Reports - BIRI District Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
            margin-bottom: 1.5rem;
        }
        .financial-metric {
            padding: 1.5rem;
            border-radius: 10px;
            background: white;
            transition: transform 0.2s;
            border-left: 4px solid #3498db;
        }
        .financial-metric:hover {
            transform: translateY(-5px);
        }
        .metric-title {
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        .metric-value {
            font-size: 1.8rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        .metric-trend {
            font-size: 0.8rem;
            color: #27ae60;
        }
        .progress {
            height: 8px;
            border-radius: 4px;
            margin-top: 0.5rem;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 1rem;
        }
        .table th {
            font-weight: 600;
            color: #6c757d;
        }
        .trend-indicator {
            font-size: 0.8rem;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
        }
        .trend-up {
            background-color: #e8f5e9;
            color: #2e7d32;
        }
        .trend-down {
            background-color: #ffebee;
            color: #c62828;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-5">
            <div>
                <h2 class="mb-1">
                    <i class="fas fa-file-invoice-dollar me-2 text-primary"></i>
                    Financial Reports <?php echo $selectedYear; ?>
                </h2>
                <p class="text-muted mb-0">Monthly and quarterly financial analysis</p>
            </div>
            <div>
                <a href="reports.php" class="btn btn-outline-primary px-4">
                    <i class="fas fa-arrow-left me-2"></i>Back to Reports
                </a>
                <a href="export_financial_pdf.php?year=<?php echo $selectedYear; ?>&month=<?php echo $selectedMonth; ?>" class="btn btn-outline-success px-4 ms-2">
                    <i class="fas fa-file-pdf me-2"></i>Export to PDF
                </a>
            </div>
        </div>
        <div class="d-flex justify-content-between align-items-center mb-4">
            
            <!-- Date Selection Controls -->
            <div class="date-filter-container bg-white rounded-lg shadow-sm p-4">
                <div class="d-flex gap-5">
                    <!-- Year Selector -->
                    <div class="filter-group">
                        <h6 class="text-muted fw-medium mb-3">Select Year</h6>
                        <div class="btn-group" role="group">
                            <?php
                            for ($i = 0; $i < 5; $i++) {
                                $year = date('Y') - $i;
                                $activeClass = ($year == $selectedYear) ? 'active' : '';
                                echo "<a href='?year={$year}&month={$selectedMonth}' 
                                        class='btn filter-btn year-filter {$activeClass}'>
                                        <i class='fas fa-calendar-alt opacity-75 me-2'></i>
                                        {$year}
                                      </a>";
                            }
                            ?>
                        </div>
                    </div>

                    <!-- Month Selector -->
                    <div class="filter-group">
                        <h6 class="text-muted fw-medium mb-3">Select Month</h6>
                        <div class="month-grid">
                            <?php
                            $months = [
                                1 => 'Jan', 2 => 'Feb', 3 => 'Mar', 4 => 'Apr',
                                5 => 'May', 6 => 'Jun', 7 => 'Jul', 8 => 'Aug',
                                9 => 'Sep', 10 => 'Oct', 11 => 'Nov', 12 => 'Dec'
                            ];
                            foreach ($months as $num => $name) {
                                $activeClass = ($num == $selectedMonth) ? 'active' : '';
                                echo "<a href='?year={$selectedYear}&month={$num}' 
                                        class='btn filter-btn month-filter {$activeClass}'>
                                        {$name}
                                      </a>";
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>

            <style>
                .date-filter-container {
                    border: 1px solid rgba(0,0,0,0.08);
                }
                .filter-group {
                    min-width: 240px;
                }
                .filter-btn {
                    font-size: 0.9rem;
                    padding: 0.6rem 1rem;
                    border: 1px solid #e5e7eb;
                    background: #fff;
                    color: #4b5563;
                    transition: all 0.2s ease;
                }
                .filter-btn:hover {
                    background: #f3f4f6;
                    border-color: #3b82f6;
                    color: #3b82f6;
                }
                .filter-btn.active {
                    background: #3b82f6;
                    border-color: #3b82f6;
                    color: #fff;
                    font-weight: 500;
                }
                .year-filter {
                    min-width: 120px;
                }
                .month-grid {
                    display: grid;
                    grid-template-columns: repeat(4, 1fr);
                    gap: 0.5rem;
                }
                .month-filter {
                    text-align: center;
                    min-width: 60px;
                }
            </style>
        </div>

        <!-- Financial Overview -->
        <div class="card mb-4">
            <div class="card-body">
                <h4 class="card-title mb-4">Financial Overview</h4>
                <div class="row g-4">
                    <div class="col-md-3">
                        <div class="financial-metric">
                            <h6 class="metric-title">Total Budget</h6>
                            <div class="metric-value">₱<?php echo number_format($totalBudget, 2); ?></div>
                            <div class="metric-trend">
                                <i class="fas fa-chart-line me-1"></i>Annual Budget
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="financial-metric">
                            <h6 class="metric-title">Total Utilized</h6>
                            <div class="metric-value">₱<?php echo number_format($totalUtilized, 2); ?></div>
                            <div class="progress">
                                <div class="progress-bar bg-success" role="progressbar" 
                                     style="width: <?php echo $utilizationRate; ?>%" 
                                     aria-valuenow="<?php echo $utilizationRate; ?>" 
                                     aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="financial-metric">
                            <h6 class="metric-title">Remaining Budget</h6>
                            <div class="metric-value">₱<?php echo number_format($totalRemaining, 2); ?></div>
                            <div class="metric-trend">
                                <?php echo $totalBudget > 0 ? number_format(($totalRemaining / $totalBudget) * 100, 1) : 0; ?>% Available
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="financial-metric">
                            <h6 class="metric-title">Current Month Spending</h6>
                            <div class="metric-value">₱<?php echo number_format($currentMonthSpent, 2); ?></div>
                            <div class="metric-trend">
                                <?php echo $monthlyBudget > 0 ? number_format($monthlyUtilizationRate, 1) : 0; ?>% of Monthly Budget
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monthly Trends Chart -->
        <div class="card mb-4">
            <div class="card-body">
                <h4 class="card-title">Monthly Spending Trends</h4>
                <div class="chart-container">
                    <canvas id="monthlyTrendsChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Department Expenditure -->
        <div class="card mb-4">
            <div class="card-body">
                <h4 class="card-title">Department Expenditure</h4>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Department</th>
                                <th>Total Spent</th>
                                <th>YoY Change</th>
                                <th>Transactions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($departmentExpenditure as $dept): ?>
                                <tr>
                                    <td><?php echo $dept['department_name']; ?></td>
                                    <td>₱<?php echo number_format($dept['total_spent'], 2); ?></td>
                                    <td>
                                        <?php
                                        $yoyChange = $dept['last_year_spent'] > 0 
                                            ? (($dept['total_spent'] - $dept['last_year_spent']) / $dept['last_year_spent'] * 100)
                                            : 0;
                                        $trendClass = $yoyChange >= 0 ? 'trend-up' : 'trend-down';
                                        $trendIcon = $yoyChange >= 0 ? '↑' : '↓';
                                        ?>
                                        <span class="trend-indicator <?php echo $trendClass; ?>">
                                            <?php echo $trendIcon . ' ' . abs(number_format($yoyChange, 1)) . '%'; ?>
                                        </span>
                                    </td>
                                    <td><?php echo $dept['transaction_count']; ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Top Spending Categories -->
        <div class="card">
            <div class="card-body">
                <h4 class="card-title">Top Spending Categories</h4>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Total Spent</th>
                                <th>% of Budget</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($topCategories as $category): ?>
                                <tr>
                                    <td><?php echo $category['account_description']; ?></td>
                                    <td>₱<?php echo number_format($category['total_spent'], 2); ?></td>
                                    <td><?php echo $totalBudget > 0 ? number_format(($category['total_spent'] / $totalBudget) * 100, 1) : '0'; ?>%</td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Monthly Trends Chart
        const ctx = document.getElementById('monthlyTrendsChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: 'Monthly Spending',
                    data: <?php echo json_encode(array_values($monthlySpending)); ?>,
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '₱' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
