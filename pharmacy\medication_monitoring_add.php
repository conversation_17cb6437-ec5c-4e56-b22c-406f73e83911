<?php
require_once '../database.php';

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: medication_monitoring_dashboard.php');
    exit();
}

try {
    // Validate required fields
    $required_fields = ['patientid', 'itemid', 'rule_id', 'prescribing_doctor', 'start_date', 'priority_level'];
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            throw new Exception("Required field '$field' is missing.");
        }
    }

    // Sanitize and validate input data
    $patientid = (int)$_POST['patientid'];
    $itemid = (int)$_POST['itemid'];
    $rule_id = (int)$_POST['rule_id'];
    $prescribing_doctor = (int)$_POST['prescribing_doctor'];
    $start_date = $_POST['start_date'];
    $priority_level = $_POST['priority_level'];
    $transaction_id = !empty($_POST['transaction_id']) ? (int)$_POST['transaction_id'] : null;
    $monitoring_notes = trim($_POST['monitoring_notes'] ?? '');

    // Validate date format
    if (!DateTime::createFromFormat('Y-m-d', $start_date)) {
        throw new Exception("Invalid start date format.");
    }

    // Validate priority level
    $valid_priorities = ['Low', 'Medium', 'High', 'Critical'];
    if (!in_array($priority_level, $valid_priorities)) {
        throw new Exception("Invalid priority level.");
    }

    // Get monitoring rule details to calculate next monitoring date
    $rule_sql = "SELECT mmr.frequency_days, mmc.category_name, mmc.risk_level
                 FROM medication_monitoring_rules mmr
                 JOIN medication_monitoring_categories mmc ON mmr.category_id = mmc.category_id
                 WHERE mmr.rule_id = ?";
    $rule_stmt = $conn->prepare($rule_sql);
    $rule_stmt->execute([$rule_id]);
    $rule = $rule_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$rule) {
        throw new Exception("Invalid monitoring rule selected.");
    }

    // Calculate next monitoring date
    $next_monitoring_date = date('Y-m-d', strtotime($start_date . ' + ' . $rule['frequency_days'] . ' days'));

    // Check if monitoring already exists for this patient-medication combination
    $existing_sql = "SELECT monitoring_id FROM patient_medication_monitoring 
                     WHERE patientid = ? AND itemid = ? AND monitoring_status = 'Active'";
    $existing_stmt = $conn->prepare($existing_sql);
    $existing_stmt->execute([$patientid, $itemid]);
    
    if ($existing_stmt->fetch()) {
        throw new Exception("Active monitoring already exists for this patient-medication combination.");
    }

    // Begin transaction
    $conn->beginTransaction();

    // Insert monitoring record
    $monitoring_sql = "INSERT INTO patient_medication_monitoring 
                      (patientid, itemid, rule_id, transaction_id, start_date, next_monitoring_date, 
                       monitoring_status, priority_level, prescribing_doctor, monitoring_notes) 
                      VALUES (?, ?, ?, ?, ?, ?, 'Active', ?, ?, ?)";
    
    $monitoring_stmt = $conn->prepare($monitoring_sql);
    $monitoring_stmt->execute([
        $patientid,
        $itemid,
        $rule_id,
        $transaction_id,
        $start_date,
        $next_monitoring_date,
        $priority_level,
        $prescribing_doctor,
        $monitoring_notes
    ]);

    $monitoring_id = $conn->lastInsertId();

    // Create initial monitoring alert
    $alert_message = "New medication monitoring initiated for patient.";
    $alert_details = json_encode([
        'monitoring_id' => $monitoring_id,
        'medication_category' => $rule['category_name'],
        'risk_level' => $rule['risk_level'],
        'frequency_days' => $rule['frequency_days'],
        'next_due_date' => $next_monitoring_date
    ]);

    $alert_sql = "INSERT INTO medication_monitoring_alerts 
                  (monitoring_id, patientid, alert_type, alert_priority, alert_message, alert_details, 
                   is_acknowledged, auto_generated) 
                  VALUES (?, ?, 'Due', ?, ?, ?, 0, 1)";
    
    $alert_stmt = $conn->prepare($alert_sql);
    $alert_stmt->execute([
        $monitoring_id,
        $patientid,
        $priority_level,
        $alert_message,
        $alert_details
    ]);

    // Commit transaction
    $conn->commit();

    // Set success message
    $_SESSION['success_message'] = "Medication monitoring added successfully. Next monitoring due: " . date('M d, Y', strtotime($next_monitoring_date));
    
    // Redirect back to dashboard
    header('Location: medication_monitoring_dashboard.php?success=1');
    exit();

} catch (Exception $e) {
    // Rollback transaction if it was started
    if ($conn->inTransaction()) {
        $conn->rollback();
    }
    
    // Set error message
    $_SESSION['error_message'] = "Error adding medication monitoring: " . $e->getMessage();
    
    // Redirect back to dashboard
    header('Location: medication_monitoring_dashboard.php?error=1');
    exit();
}
?>
