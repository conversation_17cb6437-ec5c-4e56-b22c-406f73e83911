<?php
require_once 'database.php';

$successMessage = "";
$errorMessage = "";

// Add Employee
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['add_employee'])) {
    try {
        $stmt = $conn->prepare("INSERT INTO employees (emp_name, address, contact_number, position, department_id) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([
            $_POST['emp_name'],
            $_POST['address'],
            $_POST['contact_number'],
            $_POST['position'],
            $_POST['department_id']
        ]);
        $successMessage = "Employee added successfully!";
    } catch(PDOException $e) {
        $errorMessage = "Error adding employee: " . $e->getMessage();
    }
}

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update_employee'])) {
    try {
        $stmt = $conn->prepare("UPDATE employees SET emp_name=?, address=?, contact_number=?, position=?, department_id=? WHERE id=?");
        $stmt->execute([
            $_POST['emp_name'],
            $_POST['address'],
            $_POST['contact_number'],
            $_POST['position'],
            $_POST['department_id'],
            $_POST['id']
        ]);
        $successMessage = "Employee updated successfully!";
    } catch(PDOException $e) {
        $errorMessage = "Error: " . $e->getMessage();
    }
}

// Search Employee
$searchQuery = isset($_POST['search']) ? $_POST['search'] : "";

try {
    $stmt = $conn->prepare("SELECT e.*, d.department_name FROM employees e 
                           JOIN departments d ON e.department_id = d.id 
                           WHERE e.emp_name LIKE ? OR e.position LIKE ?");
    $searchParam = "%$searchQuery%";
    $stmt->execute([$searchParam, $searchParam]);
    $result = $stmt;
} catch(PDOException $e) {
    $errorMessage = "Error: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Employee Management - BIRI District Hospital</title>
    <link rel="icon" type="image/x-icon" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
        :root {
            --primary-color: #00b4d8;
            --secondary-color: #0077b6;
            --accent-color: #48cae4;
            --success-color: #20c997;
            --warning-color: #ffd60a;
            --danger-color: #ff6b6b;
            --background-color: #f0f9ff;
            --card-color: #ffffff;
            --text-primary: #023e8a;
            --text-secondary: #0096c7;
        }

        body {
            background-color: var(--background-color);
            font-family: 'Poppins', sans-serif;
            color: var(--text-primary);
            background-image: linear-gradient(45deg, #f0f9ff 25%, #e9f2f9 25%, #e9f2f9 50%, #f0f9ff 50%, #f0f9ff 75%, #e9f2f9 75%, #e9f2f9 100%);
            background-size: 56.57px 56.57px;
        }
        
        .container {
            max-width: 1400px;
            padding: 2rem;
        }
        
        h2 {
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .card {
            border: none;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 180, 216, 0.15);
            padding: 1.5rem;
            background: var(--card-color);
            margin-bottom: 1.5rem;
            backdrop-filter: blur(10px);
            border-top: 3px solid var(--primary-color);
        }
        
        .content-wrapper {
            display: flex;
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .form-section {
            flex: 0 0 35%;
        }
        
        .table-section {
            flex: 0 0 65%;
        }
        
        .form-label {
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
        }
        
        .form-control, .form-select {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 0.75rem;
            transition: all 0.3s ease;
            background-color: rgba(255, 255, 255, 0.9);
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 180, 216, 0.2);
        }
        
        .btn {
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .btn-success {
            background-color: var(--success-color);
            border: none;
        }
        
        .btn-success:hover {
            background-color: #1db385;
        }
        
        .table {
            margin: 0;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .table th {
            background-color: var(--primary-color);
            font-weight: 600;
            color: white;
            border: none;
            padding: 1rem;
        }
        
        .table td {
            vertical-align: middle;
            color: var(--text-primary);
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .btn-warning {
            background-color: var(--warning-color);
            border: none;
            color: var(--text-primary);
        }
        
        .btn-warning:hover {
            background-color: #fcc419;
            color: var(--text-primary);
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        /* Modern scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--background-color);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }
        /* Digital elements */
        .digital-border {
            position: relative;
            overflow: hidden;
        }

        .digital-border::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            animation: borderFlow 2s linear infinite;
        }

        @keyframes borderFlow {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Pulse effect for icons */
        .fa-solid {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="digital-border"><i class="fa-solid fa-users-gear me-2"></i>Employee Management System</h2>

        <?php if ($successMessage): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($successMessage); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
        <?php if ($errorMessage): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($errorMessage); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <!-- Search Form -->
        <div class="card digital-border">
            <form method="POST" class="mb-0">
                <div class="input-group">
                    <span class="input-group-text border-0 bg-light"><i class="fa-solid fa-magnifying-glass"></i></span>
                    <input type="text" name="search" class="form-control border-0 bg-light" placeholder="Search employees..." value="<?php echo htmlspecialchars($searchQuery); ?>" />
                    <button type="submit" class="btn btn-primary px-4">Search</button>
                </div>
            </form>
        </div>

        <div class="content-wrapper">
            <!-- Add/Edit Employee Form -->
            <div class="form-section">
                <div class="card digital-border">
                    <form method="POST" id="employeeForm" class="needs-validation" novalidate>
                        <input type="hidden" name="id" id="employeeId" />
                        <div class="row g-4">
                            <div class="col-12">
                                <label for="emp_name" class="form-label">
                                    <i class="fa-solid fa-user me-2"></i>Employee Name
                                </label>
                                <input type="text" name="emp_name" class="form-control" id="emp_name" required />
                                <div class="invalid-feedback">Please enter the employee name.</div>
                            </div>
                            <div class="col-12">
                                <label for="position" class="form-label">
                                    <i class="fa-solid fa-briefcase me-2"></i>Position
                                </label>
                                <input type="text" name="position" class="form-control" id="position" required />
                                <div class="invalid-feedback">Please enter the position.</div>
                            </div>
                            <div class="col-12">
                                <label for="contact_number" class="form-label">
                                    <i class="fa-solid fa-phone me-2"></i>Contact Number
                                </label>
                                <input type="text" name="contact_number" class="form-control" id="contact_number" required />
                                <div class="invalid-feedback">Please enter the contact number.</div>
                            </div>
                            <div class="col-12">
                                <label for="department_id" class="form-label">
                                    <i class="fa-solid fa-building me-2"></i>Department
                                </label>
                                <select name="department_id" class="form-select" id="department_id" required>
                                    <option value="">Select a department</option>
                                    <?php
                                    $deptQuery = "SELECT id, department_name FROM departments ORDER BY department_name";
                                    $deptStmt = $conn->prepare($deptQuery);
                                    $deptStmt->execute();
                                    while ($row = $deptStmt->fetch(PDO::FETCH_ASSOC)) {
                                        echo "<option value='" . htmlspecialchars($row['id']) . "'>" . htmlspecialchars($row['department_name']) . "</option>";
                                    }
                                    ?>
                                </select>
                                <div class="invalid-feedback">Please select a department.</div>
                            </div>
                            <div class="col-12">
                                <label for="address" class="form-label">
                                    <i class="fa-solid fa-location-dot me-2"></i>Address
                                </label>
                                <textarea name="address" class="form-control" id="address" rows="3" required></textarea>
                                <div class="invalid-feedback">Please enter the address.</div>
                            </div>
                        </div>
                        <div class="mt-4 d-flex gap-3">
                            <button type="submit" name="add_employee" id="submitButton" class="btn btn-success flex-grow-1">
                                <i class="fa-solid fa-plus me-2"></i>Add Employee
                            </button>
                            <a href="index.php" class="btn btn-primary">
                                <i class="fa-solid fa-home me-2"></i>Homepage
                            </a>
                            <button type="button" id="cancelButton" class="btn btn-secondary d-none">
                                <i class="fa-solid fa-times me-2"></i>Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Employee List -->
            <div class="table-section">
                <div class="card digital-border table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th><i class="fa-solid fa-id-card me-2"></i>ID</th>
                                <th><i class="fa-solid fa-user me-2"></i>Name</th>
                                <th><i class="fa-solid fa-briefcase me-2"></i>Position</th>
                                <th><i class="fa-solid fa-phone me-2"></i>Contact</th>
                                <th><i class="fa-solid fa-building me-2"></i>Department</th>
                                <th><i class="fa-solid fa-gear me-2"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($row = $result->fetch(PDO::FETCH_ASSOC)) { ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($row['id']); ?></td>
                                    <td><?php echo htmlspecialchars($row['emp_name']); ?></td>
                                    <td><?php echo htmlspecialchars($row['position']); ?></td>
                                    <td><?php echo htmlspecialchars($row['contact_number']); ?></td>
                                    <td><?php echo htmlspecialchars($row['department_name']); ?></td>
                                    <td>
                                        <button onclick='editEmployee(<?php echo json_encode($row, JSON_HEX_APOS | JSON_HEX_QUOT); ?>)' class="btn btn-warning btn-sm">
                                            <i class="fa-solid fa-pen me-1"></i>Edit
                                        </button>
                                    </td>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Form validation
        (function () {
            'use strict'
            var forms = document.querySelectorAll('.needs-validation')
            Array.prototype.slice.call(forms).forEach(function (form) {
                form.addEventListener('submit', function (event) {
                    if (!form.checkValidity()) {
                        event.preventDefault()
                        event.stopPropagation()
                    }
                    form.classList.add('was-validated')
                }, false)
            })
        })()

        // Edit employee function
        function editEmployee(employee) {
            document.getElementById('employeeId').value = employee.id;
            document.getElementById('emp_name').value = employee.emp_name;
            document.getElementById('position').value = employee.position;
            document.getElementById('contact_number').value = employee.contact_number;
            document.getElementById('department_id').value = employee.department_id;
            document.getElementById('address').value = employee.address;

            // Change form button to Update
            const submitButton = document.getElementById('submitButton');
            submitButton.innerHTML = '<i class="fa-solid fa-pen me-2"></i>Update Employee';
            submitButton.name = 'update_employee';

            // Show cancel button
            const cancelButton = document.getElementById('cancelButton');
            cancelButton.classList.remove('d-none');

            // Scroll to form
            document.querySelector('.form-section').scrollIntoView({ behavior: 'smooth' });
        }

        // Cancel button handler
        document.getElementById('cancelButton').addEventListener('click', function() {
            document.getElementById('employeeForm').reset();
            document.getElementById('employeeId').value = '';
            
            // Reset form button to Add
            const submitButton = document.getElementById('submitButton');
            submitButton.innerHTML = '<i class="fa-solid fa-plus me-2"></i>Add Employee';
            submitButton.name = 'add_employee';

            // Hide cancel button
            this.classList.add('d-none');

            // Remove validation classes
            document.getElementById('employeeForm').classList.remove('was-validated');
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
