<?php
require_once 'databasehdbo.php';

// Create findings table if not exists
$sql = "CREATE TABLE IF NOT EXISTS findings (
    findings_id INT(10) NOT NULL AUTO_INCREMENT,
    findings_description TEXT NOT NULL,
    findings_audit_id INT(10) NULL DEFAULT NULL,
    findings_responsible_person TEXT NOT NULL,
    findings_priority ENUM('Low','Medium','High') NULL DEFAULT 'Medium',
    findings_status ENUM('Open','In Progress','Resolved','Closed') NULL DEFAULT 'Open',
    findings_date_completed DATE NULL DEFAULT NULL,
    findings_remarks TEXT NULL DEFAULT NULL,
    findings_attachment VARCHAR(255) NULL DEFAULT NULL,
    created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (findings_id),
    INDEX findings_audit_id (findings_audit_id),
    CONSTRAINT findings_ibfk_1 FOREIGN KEY (findings_audit_id) REFERENCES audit (audit_id) ON UPDATE NO ACTION ON DELETE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci";

$conn->exec($sql);

// Create Attachments directory if it doesn't exist
$attachments_dir = '../attachments';
if (!file_exists($attachments_dir)) {
    mkdir($attachments_dir, 0777, true);
}

// INSERT
if (isset($_POST['submit'])) {
    $attachment_path = null;
    if(isset($_FILES['findings_attachment']) && $_FILES['findings_attachment']['error'] == 0) {
        $file = $_FILES['findings_attachment'];
        $file_name = time() . '_' . basename($file['name']);
        $target_path = $attachments_dir . '/' . $file_name;
        
        if(move_uploaded_file($file['tmp_name'], $target_path)) {
            $attachment_path = $target_path;
        }
    }

    $sql = "INSERT INTO findings (findings_description, findings_audit_id, findings_responsible_person, 
            findings_priority, findings_status, findings_date_completed, findings_remarks, findings_attachment) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $date_completed = !empty($_POST['findings_date_completed']) ? $_POST['findings_date_completed'] : null;
    $stmt->execute([
        $_POST['findings_description'],
        $_POST['findings_audit_id'],
        $_POST['findings_responsible_person'],
        $_POST['findings_priority'],
        $_POST['findings_status'],
        $date_completed,
        $_POST['findings_remarks'],
        $attachment_path
    ]);
}

// UPDATE
if (isset($_POST['update'])) {
    $attachment_path = null;
    if(isset($_FILES['findings_attachment']) && $_FILES['findings_attachment']['error'] == 0) {
        // Delete old attachment if exists
        $old_attachment_query = "SELECT findings_attachment FROM findings WHERE findings_id = ?";
        $stmt = $conn->prepare($old_attachment_query);
        $stmt->execute([$_POST['findings_id']]);
        $old_attachment = $stmt->fetchColumn();
        
        if($old_attachment && file_exists($old_attachment)) {
            unlink($old_attachment);
        }

        // Upload new attachment
        $file = $_FILES['findings_attachment'];
        $file_name = time() . '_' . basename($file['name']);
        $target_path = $attachments_dir . '/' . $file_name;
        
        if(move_uploaded_file($file['tmp_name'], $target_path)) {
            $attachment_path = $target_path;
        }
    } else {
        // Keep existing attachment
        $attachment_path = $_POST['current_attachment'] ?? null;
    }

    $sql = "UPDATE findings SET 
            findings_description = ?, 
            findings_audit_id = ?,
            findings_responsible_person = ?,
            findings_priority = ?,
            findings_status = ?,
            findings_date_completed = ?,
            findings_remarks = ?,
            findings_attachment = ?
            WHERE findings_id = ?";
    $stmt = $conn->prepare($sql);
    $date_completed = !empty($_POST['findings_date_completed']) ? $_POST['findings_date_completed'] : null;
    $stmt->execute([
        $_POST['findings_description'],
        $_POST['findings_audit_id'],
        $_POST['findings_responsible_person'],
        $_POST['findings_priority'],
        $_POST['findings_status'],
        $date_completed,
        $_POST['findings_remarks'],
        $attachment_path,
        $_POST['findings_id']
    ]);
}

// SEARCH
$search = isset($_GET['search']) ? $_GET['search'] : '';
$audit_id = isset($_GET['audit_id']) ? $_GET['audit_id'] : '';

$sql = "SELECT f.*, a.audit_date, a.audit_description 
        FROM findings f 
        LEFT JOIN audit a ON f.findings_audit_id = a.audit_id 
        WHERE (f.findings_description LIKE ? 
        OR f.findings_responsible_person LIKE ? 
        OR f.findings_status LIKE ?)";

if ($audit_id) {
    $sql .= " AND f.findings_audit_id = ?";
}

$sql .= " ORDER BY f.created_at ASC";

$stmt = $conn->prepare($sql);
$searchTerm = "%$search%";

if ($audit_id) {
    $stmt->execute([$searchTerm, $searchTerm, $searchTerm, $audit_id]);
} else {
    $stmt->execute([$searchTerm, $searchTerm, $searchTerm]);
}

$results = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get counts for dashboard
$dashboardSql = "SELECT 
    COUNT(CASE WHEN findings_status = 'Open' THEN 1 END) as open_count,
    COUNT(CASE WHEN findings_status = 'In Progress' THEN 1 END) as in_progress_count,
    COUNT(CASE WHEN findings_status = 'Resolved' THEN 1 END) as resolved_count,
    COUNT(CASE WHEN findings_status = 'Closed' THEN 1 END) as closed_count
    FROM findings";

if ($audit_id) {
    $dashboardSql .= " WHERE findings_audit_id = ?";
    $dashboardStmt = $conn->prepare($dashboardSql);
    $dashboardStmt->execute([$audit_id]);
} else {
    $dashboardStmt = $conn->prepare($dashboardSql);
    $dashboardStmt->execute();
}

$dashboard = $dashboardStmt->fetch(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Healthcare Audit Findings</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --healthcare-primary: #0d6efd;
            --healthcare-light: #f8f9fa;
            --healthcare-dark: #212529;
        }
        body {
            background-color: var(--healthcare-light);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .dashboard-sidebar {
            width: 200px;
            position: fixed;
            left: 0;
            top: 0;
            bottom: 0;
            background-color: var(--healthcare-light);
            padding: 20px;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        .main-content {
            margin-left: 200px;
            padding: 10px;
        }
        .stat-card {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Left Sidebar Dashboard -->
    <div class="dashboard-sidebar">
        <h4 class="mb-4"><i class="fas fa-chart-pie me-2"></i>Dashboard</h4>
        <div class="stat-card card bg-danger text-white">
            <div class="card-body">
                <h6 class="card-title"><i class="fas fa-exclamation-circle me-2"></i>Open</h6>
                <h2 class="mb-0"><?php echo $dashboard['open_count']; ?></h2>
            </div>
        </div>
        <div class="stat-card card bg-warning text-dark">
            <div class="card-body">
                <h6 class="card-title"><i class="fas fa-clock me-2"></i>In Progress</h6>
                <h2 class="mb-0"><?php echo $dashboard['in_progress_count']; ?></h2>
            </div>
        </div>
        <div class="stat-card card bg-info text-white">
            <div class="card-body">
                <h6 class="card-title"><i class="fas fa-check-circle me-2"></i>Resolved</h6>
                <h2 class="mb-0"><?php echo $dashboard['resolved_count']; ?></h2>
            </div>
        </div>
        <div class="stat-card card bg-success text-white">
            <div class="card-body">
                <h6 class="card-title"><i class="fas fa-lock me-2"></i>Closed</h6>
                <h2 class="mb-0"><?php echo $dashboard['closed_count']; ?></h2>
            </div>
        </div>
        <div class="stat-card card bg-primary text-white">
            <div class="card-body">
                <h6 class="card-title"><i class="fas fa-file-alt me-2"></i>Status Report</h6>
                <a href="status_report.php" class="btn btn-light btn-sm mt-2 w-100">View Report</a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="card shadow-lg rounded-4">
            <div class="card-header bg-primary py-4">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="mb-0 text-white">
                        <i class="fas fa-notes-medical me-2"></i>Healthcare Audit Findings
                    </h3>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#findingsModal">
                            <i class="fas fa-plus-circle me-2"></i>New Finding
                        </button>
                        <a href="rled_audit.php" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Audits
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="card-body p-4">
                <!-- Search Form -->
                <form method="GET" class="mb-4">
                    <div class="input-group">
                        <input type="text" name="search" class="form-control form-control-lg" placeholder="Search findings..." value="<?php echo htmlspecialchars($search); ?>">
                        <input type="hidden" name="audit_id" value="<?php echo htmlspecialchars($audit_id); ?>">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-search me-2"></i>Search
                        </button>
                    </div>
                </form>

                <!-- Findings Table -->
                <div class="table-responsive" style="height: 850px;">
                    <table class="table table-hover align-middle">
                        <thead class="table-light sticky-top bg-light">
                            <tr>
                                <th>Description</th>
                                <th>Audit Date</th>
                                <th>Responsible Person</th>
                                <th>Priority</th>
                                <th>Status</th>
                                <th>Date Completed</th>
                                <th>Remarks</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($results as $row): ?>
                            <tr class="<?php echo $row['findings_status'] === 'Resolved' ? 'bg-success bg-opacity-10' : ''; ?>">
                                <td><?php echo htmlspecialchars($row['findings_description']); ?></td>
                                <td><?php echo htmlspecialchars($row['audit_date']); ?></td>
                                <td><?php echo htmlspecialchars($row['findings_responsible_person']); ?></td>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo $row['findings_priority'] === 'High' ? 'danger' : 
                                            ($row['findings_priority'] === 'Medium' ? 'warning' : 'info'); 
                                    ?>">
                                        <?php echo htmlspecialchars($row['findings_priority']); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo $row['findings_status'] === 'Open' ? 'danger' : 
                                            ($row['findings_status'] === 'In Progress' ? 'warning' : 
                                            ($row['findings_status'] === 'Resolved' ? 'info' : 'success')); 
                                    ?>">
                                        <?php echo htmlspecialchars($row['findings_status']); ?>
                                    </span>
                                </td>
                                <td><?php echo $row['findings_date_completed'] ? htmlspecialchars($row['findings_date_completed']) : '-'; ?></td>
                                <td><?php echo htmlspecialchars($row['findings_remarks']); ?></td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-primary edit-finding" 
                                            data-findings-id="<?php echo $row['findings_id']; ?>"
                                            data-description="<?php echo htmlspecialchars($row['findings_description']); ?>"
                                            data-audit-id="<?php echo $row['findings_audit_id']; ?>"
                                            data-responsible="<?php echo htmlspecialchars($row['findings_responsible_person']); ?>"
                                            data-priority="<?php echo $row['findings_priority']; ?>"
                                            data-status="<?php echo $row['findings_status']; ?>"
                                            data-completed="<?php echo $row['findings_date_completed']; ?>"
                                            data-remarks="<?php echo htmlspecialchars($row['findings_remarks']); ?>"
                                            data-attachment="<?php echo htmlspecialchars($row['findings_attachment']); ?>">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <a href="findings_action_taken.php?findings_id=<?php echo $row['findings_id']; ?>" 
                                       class="btn btn-sm btn-info">
                                        <i class="fas fa-tasks"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- Findings Modal -->
    <div class="modal fade" id="findingsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content rounded-4 shadow-lg">
                <div class="modal-header bg-primary text-white border-0">
                    <h5 class="modal-title">
                        <i class="fas fa-clipboard-check me-2"></i>Add/Edit Finding
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-4">
                    <form id="findingsForm" method="POST" enctype="multipart/form-data" class="row g-4">
                        <input type="hidden" name="findings_id" id="findings_id">
                        <input type="hidden" name="current_attachment" id="current_attachment">
                        
                        <div class="col-md-6">
                            <label class="form-label fw-bold">
                                <i class="fas fa-align-left me-2"></i>Description
                            </label>
                            <textarea class="form-control form-control-lg shadow-sm" name="findings_description" id="findings_description" required rows="3"></textarea>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label fw-bold">
                                <i class="fas fa-file-medical me-2"></i>Audit Reference
                            </label>
                            <select class="form-select form-select-lg shadow-sm" name="findings_audit_id" id="findings_audit_id" required>
                                <?php
                                $audit_sql = "SELECT audit_id, audit_date, audit_description FROM audit ORDER BY audit_date DESC";
                                $audit_stmt = $conn->query($audit_sql);
                                while ($audit = $audit_stmt->fetch(PDO::FETCH_ASSOC)) {
                                    echo "<option value='" . $audit['audit_id'] . "'>" . 
                                         htmlspecialchars($audit['audit_date'] . ' - ' . $audit['audit_description']) . 
                                         "</option>";
                                }
                                ?>
                            </select>
                        </div>
                        
                        <div class="col-md-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-user me-2"></i>Responsible Person
                            </label>
                            <input type="text" class="form-control form-control-lg shadow-sm" name="findings_responsible_person" id="findings_responsible_person" required>
                        </div>
                        
                        <div class="col-md-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-flag me-2"></i>Priority
                            </label>
                            <select class="form-select form-select-lg shadow-sm" name="findings_priority" id="findings_priority">
                                <option value="Low">🟢 Low</option>
                                <option value="Medium" selected>🟡 Medium</option>
                                <option value="High">🔴 High</option>
                            </select>
                        </div>
                        
                        <div class="col-md-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-tasks me-2"></i>Status
                            </label>
                            <select class="form-select form-select-lg shadow-sm" name="findings_status" id="findings_status">
                                <option value="Open" selected>⭕ Open</option>
                                <option value="In Progress">⏳ In Progress</option>
                                <option value="Resolved">✅ Resolved</option>
                                <option value="Closed">🔒 Closed</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar-check me-2"></i>Date Completed
                            </label>
                            <input type="date" class="form-control form-control-lg shadow-sm" name="findings_date_completed" id="findings_date_completed">
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label fw-bold">
                                <i class="fas fa-paperclip me-2"></i>Attachment
                            </label>
                            <input type="file" class="form-control form-control-lg shadow-sm" name="findings_attachment" id="findings_attachment">
                            <div id="current_attachment_display" class="mt-2 text-muted fst-italic"></div>
                        </div>
                        
                        <div class="col-12">
                            <label class="form-label fw-bold">
                                <i class="fas fa-comment-dots me-2"></i>Remarks
                            </label>
                            <textarea class="form-control form-control-lg shadow-sm" name="findings_remarks" id="findings_remarks" rows="2"></textarea>
                        </div>
                        
                        <div class="col-12 text-end mt-4">
                            <button type="button" class="btn btn-lg btn-light shadow-sm" data-bs-dismiss="modal">
                                <i class="fas fa-times me-2"></i>Cancel
                            </button>
                            <button type="submit" class="btn btn-lg btn-primary shadow-sm ms-2" name="submit">
                                <i class="fas fa-save me-2"></i>Save Finding
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle edit button clicks
            document.querySelectorAll('.edit-finding').forEach(button => {
                button.addEventListener('click', function() {
                    const modal = new bootstrap.Modal(document.getElementById('findingsModal'));
                    
                    // Populate form fields
                    document.getElementById('findings_id').value = this.dataset.findingsId;
                    document.getElementById('findings_description').value = this.dataset.description;
                    document.getElementById('findings_audit_id').value = this.dataset.auditId;
                    document.getElementById('findings_responsible_person').value = this.dataset.responsible;
                    document.getElementById('findings_priority').value = this.dataset.priority;
                    document.getElementById('findings_status').value = this.dataset.status;
                    document.getElementById('findings_date_completed').value = this.dataset.completed;
                    document.getElementById('findings_remarks').value = this.dataset.remarks;
                    document.getElementById('current_attachment').value = this.dataset.attachment;
                    
                    // Show current attachment if exists
                    const attachmentDisplay = document.getElementById('current_attachment_display');
                    if (this.dataset.attachment) {
                        attachmentDisplay.innerHTML = `Current attachment: <a href="${this.dataset.attachment}" target="_blank">${this.dataset.attachment.split('/').pop()}</a>`;
                    } else {
                        attachmentDisplay.innerHTML = '';
                    }
                    
                    // Change form submission
                    const form = document.getElementById('findingsForm');
                    form.querySelector('button[type="submit"]').name = 'update';
                    form.querySelector('button[type="submit"]').textContent = 'Update Finding';
                    
                    modal.show();
                });
            });
            
            // Reset form when modal is hidden
            document.getElementById('findingsModal').addEventListener('hidden.bs.modal', function() {
                document.getElementById('findingsForm').reset();
                document.getElementById('findings_id').value = '';
                document.getElementById('current_attachment').value = '';
                document.getElementById('current_attachment_display').innerHTML = '';
                const submitButton = document.querySelector('button[type="submit"]');
                submitButton.name = 'submit';
                submitButton.textContent = 'Save Finding';
            });
        });
    </script>
</body>
</html>
