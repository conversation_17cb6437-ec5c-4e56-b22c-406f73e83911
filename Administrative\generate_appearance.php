<?php
require_once('../fpdf/fpdf.php');
require_once '../database.php';

class PDF extends FPDF {
    function Header() {
        // This function intentionally left empty to handle headers manually
    }

    function PrintCertificate($certificate, $y_offset = 0) {
        // Set 8.5x13 dimensions (215.9mm x 330.2mm)
        $pageWidth = 215.9;
        $pageHeight = 330.2;
        $this->SetMargins(10, 10, 10);

        // Calculate center positions
        $centerX = $pageWidth / 2;

        // Draw decorative border
        $this->SetLineWidth(0.5);
        $borderX = 20;
        $borderY = 10 + $y_offset;
        $borderWidth = $pageWidth - 40;
        $borderHeight = 150;
        
        // Outer border
        $this->SetDrawColor(0, 0, 0);
        $this->Rect($borderX, $borderY, $borderWidth, $borderHeight);
        
        // Inner decorative border
        $this->SetDrawColor(128, 128, 128);
        $this->SetLineWidth(0.2);
        $this->Rect($borderX + 3, $borderY + 3, $borderWidth - 6, $borderHeight - 6);
        
        // Corner decorations
        $cornerSize = 10;
        // Top left corner
        $this->Line($borderX, $borderY + $cornerSize, $borderX + $cornerSize, $borderY);
        // Top right corner
        $this->Line($borderX + $borderWidth - $cornerSize, $borderY, $borderX + $borderWidth, $borderY + $cornerSize);
        // Bottom left corner
        $this->Line($borderX, $borderY + $borderHeight - $cornerSize, $borderX + $cornerSize, $borderY + $borderHeight);
        // Bottom right corner
        $this->Line($borderX + $borderWidth - $cornerSize, $borderY + $borderHeight, $borderX + $borderWidth, $borderY + $borderHeight - $cornerSize);

        // Add header logos with adjusted positions
        if (file_exists('../images/pgns.png') && file_exists('../images/bdh.png')) {
            $this->Image('../images/pgns.png', $centerX - 55, 20 + $y_offset, 25);
            $this->Image('../images/bdh.png', $centerX + 30, 20 + $y_offset, 25);
        }

        // Header section
        $this->SetFont('Arial', '', 9);
        $this->SetY(20 + $y_offset);
        $this->Cell(0, 5, 'Republic of the Philippines', 0, 1, 'C');
        $this->Cell(0, 5, 'Province of Northern Samar', 0, 1, 'C');
        $this->Cell(0, 5, 'Provincial Health Office', 0, 1, 'C');
        
        // Hospital name
        $this->SetFont('Arial', 'B', 12);
        $this->Ln(2);
        $this->Cell(0, 6, 'BIRI DISTRICT HOSPITAL', 0, 1, 'C');
        
        $this->SetFont('Arial', '', 9);
        $this->Cell(0, 5, 'Biri Northern Samar', 0, 1, 'C');
        
        // Title with decorative lines
        $this->Ln(6);
        $this->SetLineWidth(0.3);
        $this->Line($centerX - 40, $this->GetY(), $centerX + 40, $this->GetY());
        $this->SetFont('Arial', 'B', 11);
        $this->Cell(0, 6, 'CERTIFICATE OF APPEARANCE', 0, 1, 'C');
        $this->Line($centerX - 40, $this->GetY(), $centerX + 40, $this->GetY());
        $this->Ln(4);

        // Content
        $this->SetLeftMargin(35);
        $this->SetRightMargin(35);

        $this->SetFont('Arial', 'B', 9);
        $this->Cell(0, 6, 'TO WHOM IT MAY CONCERN:', 0, 1, 'L');

        $this->Ln(3);
        $this->SetFont('Arial', '', 9);
        $this->MultiCell(0, 6, 'This is to certify that ' . $this->SetFont('Arial', 'B', 9) . strtoupper($certificate['full_name']) . $this->SetFont('Arial', '', 9) . ' has appeared in this office from ' . 
            date('F d, Y', strtotime($certificate['date_from'])) . ' to ' . date('F d, Y', strtotime($certificate['date_to'])) . 
            ' for the purpose of ' . $certificate['purpose'] . '.', 0, 'J');

        $this->Ln(4);
        $this->MultiCell(0, 6, 'This certification is issued upon the request of the above-named person for whatever legal purpose it may serve.', 0, 'J');

        $this->Ln(4);
        $this->Cell(0, 6, 'Issued this ' . date('jS', strtotime($certificate['date_issued'])) . ' day of ' . 
            date('F Y', strtotime($certificate['date_issued'])) . ' at ' . $certificate['place_issued'] . '.', 0, 1, 'L');

        // Signature section
        $this->Ln(20);
        $this->SetFont('Arial', 'B', 9);
        $this->Cell(0, 4, strtoupper($certificate['issued_by']), 0, 1, 'L');
        $this->SetFont('Arial', '', 9);
        $this->Cell(0, 4, $certificate['position'], 0, 1, 'L');
        $this->Cell(60, 4, 'Reference Number: ' . date('Y') . '-' . $certificate['id'], 0, 1, 'L');
    }

    function AliasNbPages($alias = '{nb}') {
        $this->AliasNbPages = $alias;
    }
}

// Initialize PDF with 8.5x13 format (215.9mm x 330.2mm)
$pdf = new PDF('P', 'mm', array(215.9, 330.2));
$pdf->AddPage();

// Fetch certificate data
$id = isset($_GET['id']) ? $_GET['id'] : null;
$sql = "SELECT * FROM certificate_of_appearance WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$id]);
$certificate = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$certificate) {
    die("Certificate not found");
}

// Print two copies on one 8.5x13 page with adjusted spacing
$pdf->PrintCertificate($certificate, 0);  // First copy at top
$pdf->PrintCertificate($certificate, 165);  // Second copy adjusted for 8.5x13

// Generate PDF
$pdf->Output('I', 'certificate_of_appearance.pdf', true);
