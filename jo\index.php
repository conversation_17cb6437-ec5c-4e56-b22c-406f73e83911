<?php
include_once '../jo/database.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['save'])) {
        $employee_id = $_POST['employee_id'];
        
        // Check for duplicate employee_id
        $check_sql = "SELECT COUNT(*) FROM jo_profile WHERE employee_id = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->execute([$employee_id]);
        $count = $check_stmt->fetchColumn();
        
        if ($count > 0) {
            echo "<script>alert('Error: Employee ID already exists!');</script>";
        } else {
            $jo_name = strtoupper($_POST['jo_name']);
            $jo_birthday = $_POST['jo_birthday'];
            $jo_gender = $_POST['jo_gender'];
            $jo_address = $_POST['jo_address'];
            $jo_course = $_POST['jo_course'];
            $jo_educ_attain = $_POST['jo_educ_attain'];
            $jo_eligibility = $_POST['jo_eligibility'];
            $jo_phonenumber = $_POST['jo_phonenumber'];
            $jo_remakrs = $_POST['jo_remarks'];
            
            // Handle file upload
            if(isset($_FILES['jo_image']) && $_FILES['jo_image']['error'] == 0) {
                $target_dir = "../images/";
                $file_extension = strtolower(pathinfo($_FILES['jo_image']['name'], PATHINFO_EXTENSION));
                $new_filename = uniqid() . '.' . $file_extension;
                $target_file = $target_dir . $new_filename;
                
                if(move_uploaded_file($_FILES['jo_image']['tmp_name'], $target_file)) {
                    $image_path = $target_file;
                } else {
                    $image_path = 'default.jpg'; // Set default image path
                }
            } else {
                $image_path = 'default.jpg'; // Set default image path
            }
            
            $sql = "INSERT INTO jo_profile (employee_id, jo_name, jo_birthday, jo_gender, jo_address, 
                    jo_course, jo_educ_attain, jo_eligibility, jo_phonenumber, Remarks, image_path) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$employee_id, $jo_name, $jo_birthday, $jo_gender, $jo_address, 
                           $jo_course, $jo_educ_attain, $jo_eligibility, $jo_phonenumber, $jo_remakrs, $image_path]);
        }
    }
}

if (isset($_POST['update']) && isset($_POST['profile_id'])) {
    $profile_id = $_POST['profile_id'];
    $employee_id = $_POST['employee_id'];
    
    // Check for duplicate employee_id excluding current profile
    $check_sql = "SELECT COUNT(*) FROM jo_profile WHERE employee_id = ? AND profile_id != ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->execute([$employee_id, $profile_id]);
    $count = $check_stmt->fetchColumn();
    
    if ($count > 0) {
        echo "<script>alert('Error: Employee ID already exists!');</script>";
    } else {
        $jo_name = strtoupper($_POST['jo_name']);
        $jo_birthday = $_POST['jo_birthday'];
        $jo_gender = $_POST['jo_gender'];
        $jo_address = $_POST['jo_address'];
        $jo_course = $_POST['jo_course'];
        $jo_educ_attain = $_POST['jo_educ_attain'];
        $jo_eligibility = $_POST['jo_eligibility'];
        $jo_phonenumber = $_POST['jo_phonenumber'];
        $jo_remakrs = $_POST['jo_remarks'];
        
        // Get current image path from database
        $get_current_image = "SELECT image_path FROM jo_profile WHERE profile_id = ?";
        $stmt = $conn->prepare($get_current_image);
        $stmt->execute([$profile_id]);
        $current_image = $stmt->fetchColumn();
        
        // Handle file upload for update
        if(isset($_FILES['jo_image']) && $_FILES['jo_image']['error'] == 0) {
            $target_dir = "../images/";
            $file_extension = strtolower(pathinfo($_FILES['jo_image']['name'], PATHINFO_EXTENSION));
            $new_filename = uniqid() . '.' . $file_extension;
            $target_file = $target_dir . $new_filename;
            
            if(move_uploaded_file($_FILES['jo_image']['tmp_name'], $target_file)) {
                $image_path = $target_file;
            } else {
                $image_path = $current_image; // Keep current image if upload fails
            }
        } else {
            $image_path = $current_image; // Keep current image if no new image uploaded
        }
        
        $sql = "UPDATE jo_profile SET employee_id=?, jo_name=?, jo_birthday=?, jo_gender=?, 
                jo_address=?, jo_course=?, jo_educ_attain=?, jo_eligibility=?, 
                jo_phonenumber=?, Remarks=?, image_path=? WHERE profile_id=?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$employee_id, $jo_name, $jo_birthday, $jo_gender, $jo_address, 
                       $jo_course, $jo_educ_attain, $jo_eligibility, 
                       $jo_phonenumber, $jo_remakrs, $image_path, $profile_id]);
    }
}

// Initialize search variable
$search = isset($_GET['search']) ? $_GET['search'] : '';

$employee_id = $_GET['employee_id'] ?? '';

try {
    // Fetch employee profile and contracts
    $sql = "SELECT 
        p.jo_name,
        MIN(c.appointment_from) as first_appointment,
        MAX(c.appointment_to) as last_appointment
        FROM jo_profile p
        LEFT JOIN jo_contract c ON p.employee_id = c.employee_id
        WHERE p.employee_id = ?
        GROUP BY p.employee_id, p.jo_name";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$employee_id]);
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

    // Only output if employee_id was provided in GET parameters
    if (isset($_GET['employee_id']) && $_GET['employee_id'] !== '') {
        if (!$row) {
            echo "No profile found.";
        } elseif (empty($row['first_appointment']) || empty($row['last_appointment'])) {
            echo "NEW";
        } else {
            // Calculate service duration
            $start = new DateTime($row['first_appointment']);
            $end = new DateTime($row['last_appointment']);
            $currentDate = new DateTime();
            
            // Use current date if last_appointment is in future
            if ($end > $currentDate) {
                $end = $currentDate;
            }
            
            $interval = $start->diff($end);
            
            $years = $interval->y;
            $months = $interval->m;
            
            // Format output
            if ($years > 0 && $months > 0) {
                echo "{$years} year" . ($years > 1 ? "s" : "") . " and {$months} month" . ($months > 1 ? "s" : "");
            } elseif ($years > 0) {
                echo "{$years} year" . ($years > 1 ? "s" : "");
            } else {
                echo "{$months} month" . ($months > 1 ? "s" : "");
            }
        }
    }
} catch(PDOException $e) {
    echo "Error: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>HR Personnel Management System</title>
    <link rel="icon" type="image/x-icon" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
      <style>
                :root {
            --primary-color: #2563eb;
            --secondary-color: #1d4ed8;
            --accent-color: #60a5fa;
            --background-color: #f8fafc;
            --card-background: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            color: var(--text-primary);
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .container {
            max-width: 1400px;
            padding: 1rem;
        }

        .page-header {
            background: linear-gradient(135deg, #1e40af, #1d4ed8);
            color: white;
            padding: 1.5rem;
            border-radius: 0.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .page-header h1 {
            font-size: 1.5rem;
            margin: 0;
            font-weight: 600;
        }

        .card {
            border: none;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            background: var(--card-background);
        }

        .table {
            font-size: 0.813rem;
            margin-bottom: 0;
        }

        .table th {
            font-weight: 600;
            background: #f1f5f9;
            padding: 0.5rem;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: var(--text-secondary);
        }

        .table td {
            padding: 0.5rem;
            vertical-align: middle;
            color: var(--text-primary);
        }

        .table-responsive {
            max-height: 70vh;
            overflow-y: auto;
            scrollbar-width: thin;
        }

        .table-responsive::-webkit-scrollbar {
            width: 6px;
        }

        .table-responsive::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        .table-responsive::-webkit-scrollbar-thumb {
            background: #94a3b8;
            border-radius: 3px;
        }

        .btn {
            padding: 0.375rem 0.75rem;
            font-size: 0.75rem;
            border-radius: 0.375rem;
            font-weight: 500;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.7rem;
        }

        .action-cell {
            white-space: nowrap;
            width: 100px;
        }

        .search-form .input-group {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }

        .form-control {
            font-size: 0.875rem;
            border: 1px solid var(--border-color);
        }

        .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .table-striped > tbody > tr:nth-of-type(odd) {
            background-color: #f8fafc;
        }

        .table-hover > tbody > tr:hover {
            background-color: #f1f5f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <h1><i class="fas fa-users me-3"></i>Hospital Personnel Management System</h1>
            <p class="mb-0">Job Order Personnel Records Management</p>
        </div>
        
        <div class="card">
            <div class="card-body">
                <form method="GET" class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-magnifying-glass"></i></span>
                        <input type="text" name="search" class="form-control" placeholder="Search by name or employee ID..." value="<?php echo htmlspecialchars($search); ?>">
                        <button type="submit" class="btn btn-primary px-4">Search</button>
                    </div>
                </form>

                <div class="action-buttons mb-4">
                    <button type="button" class="btn btn-success btn-lg" data-bs-toggle="modal" data-bs-target="#addModal">
                        <i class="fa-solid fa-plus me-2"></i>Add New Record
                    </button>
                    <a href="export_years_service_pdf.php" class="btn btn-primary btn-lg">
                        <i class="fa-solid fa-file-export me-2"></i>Export to PDF
                    </a>
                    <a href="../index.php" class="btn btn-secondary btn-lg">
                        <i class="fa-solid fa-home me-2"></i>Back to Dashboard
                    </a>
                </div>

                <!-- Add Modal -->
                <div class="modal fade" id="addModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add New Record</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form method="POST" id="addForm">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa-solid fa-id-card"></i></span>
                                                <input type="text" name="employee_id" class="form-control" placeholder="Employee ID" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa-solid fa-user"></i></span>
                                                <input type="text" name="jo_name" class="form-control" placeholder="Full Name" required 
                                                       oninput="this.value = this.value.toUpperCase()">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa-solid fa-calendar"></i></span>
                                                <input type="date" name="jo_birthday" class="form-control" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa-solid fa-venus-mars"></i></span>
                                                <select name="jo_gender" class="form-control" required>
                                                    <option value="">Select Gender</option>
                                                    <option value="Male">Male</option>
                                                    <option value="Female">Female</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa-solid fa-location-dot"></i></span>
                                                <input type="text" name="jo_address" class="form-control" placeholder="Address" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa-solid fa-graduation-cap"></i></span>
                                                <input type="text" name="jo_course" class="form-control" placeholder="Course" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa-solid fa-book"></i></span>
                                                <input type="text" name="jo_educ_attain" class="form-control" placeholder="Educational Attainment" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa-solid fa-certificate"></i></span>
                                                <input type="text" name="jo_eligibility" class="form-control" placeholder="Eligibility" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa-solid fa-phone"></i></span>
                                                <input type="text" name="jo_phonenumber" class="form-control" placeholder="Phone Number" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa-solid fa-clipboard-list fa-2x"></i></span>
                                                <textarea name="jo_remarks" class="form-control" placeholder="Remarks" rows="3" required></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa-solid fa-image"></i></span>
                                                <input type="file" name="jo_image" class="form-control" accept="image/*">
                                                <input type="hidden" name="jo_image_path" value="../images/">
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <button type="submit" form="addForm" name="save" class="btn btn-success">Save Record</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Edit Modal -->
                <div class="modal fade" id="editModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Record</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form method="POST" id="editForm" enctype="multipart/form-data">
                                    <input type="hidden" name="profile_id" id="edit_profile_id">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa-solid fa-id-card"></i></span>
                                                <input type="text" name="employee_id" id="edit_employee_id" class="form-control" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa-solid fa-user"></i></span>
                                                <input type="text" name="jo_name" id="edit_jo_name" class="form-control" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa-solid fa-calendar"></i></span>
                                                <input type="date" name="jo_birthday" id="edit_jo_birthday" class="form-control" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa-solid fa-venus-mars"></i></span>
                                                <select name="jo_gender" id="edit_jo_gender" class="form-control" required>
                                                    <option value="Male">Male</option>
                                                    <option value="Female">Female</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa-solid fa-location-dot"></i></span>
                                                <input type="text" name="jo_address" id="edit_jo_address" class="form-control" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa-solid fa-graduation-cap"></i></span>
                                                <input type="text" name="jo_course" id="edit_jo_course" class="form-control" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa-solid fa-book"></i></span>
                                                <input type="text" name="jo_educ_attain" id="edit_jo_educ_attain" class="form-control" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa-solid fa-certificate"></i></span>
                                                <input type="text" name="jo_eligibility" id="edit_jo_eligibility" class="form-control" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa-solid fa-phone"></i></span>
                                                <input type="text" name="jo_phonenumber" id="edit_jo_phonenumber" class="form-control" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa-solid fa-clipboard-list fa-2x"></i></span>
                                                <textarea name="jo_remarks" id="edit_jo_remarks" class="form-control" rows="3" required></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="image-upload-container">
                                                <div class="input-group">
                                                    <span class="input-group-text bg-primary text-white">
                                                        <i class="fa-solid fa-cloud-upload-alt fa-lg"></i>
                                                    </span>
                                                    <input type="file" name="jo_image" class="form-control custom-file-input" 
                                                           accept="image/*" id="edit_jo_image">
                                                    <input type="hidden" name="jo_image_path" id="edit_jo_image_path" value="../images/">
                                                </div>
                                                <div id="current_image_display" class="image-preview mt-2 rounded shadow-sm"></div>
                                                <small class="text-muted mt-1">
                                                    <i class="fa-solid fa-info-circle"></i> 
                                                    Upload image (max 5MB)
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <button type="submit" form="editForm" name="update" class="btn btn-warning">Update Record</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Table section -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive" style="max-height: 600px;">
                    <table class="table table-striped table-hover">
                        <thead class="table-light" style="position: sticky; top: 0; z-index: 1;">
                            <tr>
                                <th><i class="fa-solid fa-id-card me-2"></i>Employee ID</th>
                                <th><i class="fa-solid fa-user me-2"></i>Name</th>
                                <th><i class="fa-solid fa-calendar me-2"></i>Birthday</th>
                                <th><i class="fa-solid fa-venus-mars me-2"></i>Gender</th>
                                <th><i class="fa-solid fa-clock me-2"></i>Years in Service</th>
                                <th><i class="fa-solid fa-gear me-2"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $search = isset($_GET['search']) ? $_GET['search'] : '';
                            
                            $sql = "SELECT 
                                p.*, 
                                MIN(c.appointment_from) as first_appointment,
                                MAX(c.appointment_to) as last_appointment
                                FROM jo_profile p
                                LEFT JOIN jo_contract c ON p.employee_id = c.employee_id";
                            
                            if ($search) {
                                $sql .= " WHERE p.employee_id LIKE ? OR p.jo_name LIKE ?";
                                $searchParam = "%$search%";
                                $stmt = $conn->prepare($sql . " GROUP BY p.profile_id");
                                $stmt->execute([$searchParam, $searchParam]);
                            } else {
                                $stmt = $conn->prepare($sql . " GROUP BY p.profile_id");
                                $stmt->execute();
                            }
                            
                            $result = $stmt->fetchAll();
                            
                            foreach ($result as $row):
                                // Calculate service duration
                                if (empty($row['first_appointment']) || empty($row['last_appointment'])) {
                                    $row['total_years'] = "NEW";
                                } else {
                                    $start = new DateTime($row['first_appointment']);
                                    $end = new DateTime($row['last_appointment']);
                                    $currentDate = new DateTime();
                                    
                                    if ($end > $currentDate) {
                                        $end = $currentDate;
                                    }
                                    
                                    $interval = $start->diff($end);
                                    
                                    // Get start and end months
                                    $startMonth = (int)$start->format('n');
                                    $endMonth = (int)$end->format('n');
                                    
                                    // Calculate years normally
                                    $years = $interval->y;
                                    
                                    // Calculate months with January-June and July-December counting as 6 months
                                    $months = $interval->m;
                                    if (($startMonth >= 7 && $endMonth >= 7) || ($startMonth <= 6 && $endMonth <= 6)) {
                                        $months = 6;
                                    }
                                    
                                    if ($years > 0 && $months > 0) {
                                        $row['total_years'] = "{$years}y {$months}m";
                                    } elseif ($years > 0) {
                                        $row['total_years'] = "{$years}y";
                                    } elseif ($months > 0) {
                                        $row['total_years'] = "{$months}m";
                                    } else {
                                        $row['total_years'] = "NEW";
                                    }
                                }
                            ?>
                            <tr>
                                <td><?php echo htmlspecialchars($row['employee_id']); ?></td>
                                <td><?php echo htmlspecialchars($row['jo_name']); ?></td>
                                <td><?php 
                                    $birthday = new DateTime($row['jo_birthday']);
                                    $today = new DateTime();
                                    // Set birthday to this year, considering leap years
                                    $birthday->modify($today->format('Y') . '-' . $birthday->format('m-d'));
                                    
                                    $diff = $today->diff($birthday);
                                    $daysDiff = $diff->invert ? 365 - $diff->days : $diff->days;
                                    
                                    if ($daysDiff <= 30 && $daysDiff >= 0) {
                                        // Birthday notification badge
                                        echo '<div class="birthday-notification position-relative d-inline-block" 
                                              data-bs-toggle="modal" data-bs-target="#birthdayModal' . $row['profile_id'] . '">';
                                        echo '<span class="birthday-date d-flex align-items-center gap-2 text-primary fw-bold" 
                                              style="cursor: pointer; transition: all 0.3s ease;">';
                                        echo '<i class="fas fa-birthday-cake animate__animated animate__swing animate__infinite"></i>';
                                        echo date('F d, Y', strtotime($row['jo_birthday']));
                                        echo '<span class="notification-badge position-absolute top-0 start-100 translate-middle 
                                              badge rounded-pill bg-danger animate__animated animate__pulse animate__infinite">Coming Soon!</span>';
                                        echo '</span></div>';
                                        
                                        // Modern Birthday Celebration Modal
                                        echo '<div class="modal fade" id="birthdayModal' . $row['profile_id'] . '" tabindex="-1">';
                                        echo '<div class="modal-dialog modal-lg modal-dialog-centered">';
                                        echo '<div class="modal-content border-0 overflow-hidden">';
                                        echo '<div class="modal-body p-0 position-relative">';
                                        echo '<div class="celebration-card p-5" style="background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%); 
                                              border-radius: 24px; box-shadow: 0 20px 40px rgba(0,0,0,0.2);">';
                                        
                                        // Profile Image Section
                                        echo '<div class="profile-section position-relative mb-5 text-center">';
                                        echo '<div class="profile-image-wrapper position-relative d-inline-block">';
                                        echo '<img src="' . ($row['image_path'] ?: '../images/default.jpg') . '" 
                                              class="profile-image rounded-circle animate__animated animate__zoomIn" 
                                              style="width: 200px; height: 200px; object-fit: cover; border: 10px solid rgba(255,255,255,0.2); 
                                              box-shadow: 0 15px 35px rgba(0,0,0,0.2);">';
                                        echo '<div class="crown-icon position-absolute top-0 end-0 translate-middle-y 
                                              animate__animated animate__bounceIn animate__delay-1s">';
                                        echo '<i class="fas fa-crown fa-3x" style="color: #ffd700; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));"></i>';
                                        echo '</div></div></div>';
                                        
                                        // Celebration Content
                                        echo '<div class="celebration-content text-center animate__animated animate__fadeInUp animate__delay-1s">';
                                        echo '<h2 class="celebration-title display-4 fw-bold text-white mb-4" 
                                              style="text-shadow: 2px 2px 4px rgba(0,0,0,0.2);">Happy Birthday! 🎉</h2>';
                                        echo '<h3 class="celebrant-name h2 text-white mb-4">' . htmlspecialchars($row['jo_name']) . '</h3>';
                                        echo '<p class="celebration-message lead text-white mb-4 opacity-90">
                                              Celebrating your special day on ' . date('F d', strtotime($row['jo_birthday'])) . '</p>';
                                        echo '<p class="service-appreciation lead text-white mb-5 opacity-90">
                                              Proud ' . htmlspecialchars($row['total_years']) . ' of dedicated service at Biri District Hospital</p>';
                                        
                                        // Celebration Icons
                                        echo '<div class="celebration-icons d-flex justify-content-center gap-5 
                                              animate__animated animate__bounceIn animate__delay-2s">';
                                        echo '<i class="fas fa-gift fa-3x" style="color: #ffd700; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));"></i>';
                                        echo '<i class="fas fa-star fa-3x" style="color: #ffd700; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));"></i>';
                                        echo '<i class="fas fa-heart fa-3x" style="color: #ff6b6b; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));"></i>';
                                        echo '<i class="fas fa-cake-candles fa-3x" style="color: #ffd700; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));"></i>';
                                        echo '</div></div></div></div></div></div></div>';
                                    } else {
                                        echo '<span class="text-muted">' . date('F d, Y', strtotime($row['jo_birthday'])) . '</span>';
                                    }
                                ?></td>
                                <td><?php echo htmlspecialchars($row['jo_gender']); ?></td>
                                <td><?php echo htmlspecialchars($row['total_years']); ?></td>
                                <td class="action-cell">
                                    <button type="button" class="btn btn-warning btn-sm" onclick='editProfile(<?php echo json_encode($row); ?>)'>
                                        <i class="fa-solid fa-edit"></i>
                                    </button>
                                    <a href="contract_history.php?id=<?php echo $row['profile_id']; ?>" class="btn btn-info btn-sm">
                                        <i class="fa-solid fa-history"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    <script>
    function editProfile(profile) {
        // Show the edit modal
        var editModal = new bootstrap.Modal(document.getElementById('editModal'));
        editModal.show();
        
        // Fill the form fields
        document.querySelector('#edit_profile_id').value = profile.profile_id;
        document.querySelector('#edit_employee_id').value = profile.employee_id;
        document.querySelector('#edit_jo_name').value = profile.jo_name;
        document.querySelector('#edit_jo_birthday').value = profile.jo_birthday;
        document.querySelector('#edit_jo_gender').value = profile.jo_gender;
        document.querySelector('#edit_jo_address').value = profile.jo_address;
        document.querySelector('#edit_jo_course').value = profile.jo_course;
        document.querySelector('#edit_jo_educ_attain').value = profile.jo_educ_attain;
        document.querySelector('#edit_jo_eligibility').value = profile.jo_eligibility;
        document.querySelector('#edit_jo_phonenumber').value = profile.jo_phonenumber;        
        document.querySelector('#edit_jo_remarks').value = profile.Remarks;        
        // Display current image if it exists
        if (profile.image_path) {
            document.querySelector('#current_image_display').innerHTML = `<img src="${profile.image_path}" height="50" class="mt-2">`;
        }
        document.querySelector('#edit_jo_years_inservice').value = profile.jo_years_inservice;
    }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
