<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Warehouse Barcode Scanner - BIRI District Hospital">
    <title>Warehouse Scanner - BIRI District Hospital</title>
    
    <!-- Stylesheets -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Scripts -->
    <script src="https://unpkg.com/quagga@0.12.1/dist/quagga.min.js" defer></script>
    <style>
        /* Previous styles remain the same */
        .scan-mode-selector {
            margin-bottom: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .manual-input {
            margin-top: 1rem;
            padding: 1rem;
            background: #fff;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .barcode-info {
            margin-top: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .barcode-type {
            font-size: 0.9rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="warehouse-card">
                    <div class="warehouse-header">
                        <div class="d-flex align-items-center">
                            <div class="bg-white p-3 rounded-circle me-3">
                                <i class="fas fa-barcode text-primary fs-4"></i>
                            </div>
                            <div>
                                <h4 class="text-white mb-0">Warehouse Scanner</h4>
                                <p class="text-white-50 mb-0">Scan inventory items</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-4">
                        <div class="scan-mode-selector">
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary active" id="cameraMode">
                                    <i class="fas fa-camera me-2"></i>Camera Scanner
                                </button>
                                <button type="button" class="btn btn-outline-primary" id="manualMode">
                                    <i class="fas fa-keyboard me-2"></i>Manual Input
                                </button>
                            </div>
                        </div>
                        
                        <div id="cameraScanner">
                            <div id="interactive" class="viewport mb-4"></div>
                        </div>
                        
                        <div id="manualScanner" class="manual-input d-none">
                            <div class="input-group">
                                <input type="text" class="form-control" id="barcodeInput" 
                                       placeholder="Scan or type barcode here" autofocus>
                                <button class="btn btn-primary" type="button" id="submitBarcode">
                                    <i class="fas fa-check me-2"></i>Submit
                                </button>
                            </div>
                        </div>

                        <div class="barcode-display d-none">
                            <img id="barcodeImage" alt="Barcode">
                        </div>
                        <div id="result" class="scan-result p-3 d-none">
                            <i class="fas fa-check-circle me-2"></i>
                            <span class="scan-text"></span>
                            <a href="#" id="googleSearch" target="_blank" class="btn btn-primary btn-sm ms-2">
                                <i class="fas fa-search me-1"></i>Search on Google
                            </a>
                        </div>
                        <div class="barcode-info d-none" id="barcodeInfo">
                            <h5>Barcode Information</h5>
                            <div class="barcode-type"></div>
                            <div class="barcode-details mt-2"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const resultDiv = document.getElementById('result');
            const scanText = resultDiv.querySelector('.scan-text');
            const googleSearch = document.getElementById('googleSearch');
            const barcodeDisplay = document.querySelector('.barcode-display');
            const barcodeImage = document.getElementById('barcodeImage');
            const barcodeInfo = document.getElementById('barcodeInfo');
            const barcodeType = barcodeInfo.querySelector('.barcode-type');
            const barcodeDetails = barcodeInfo.querySelector('.barcode-details');
            const cameraScanner = document.getElementById('cameraScanner');
            const manualScanner = document.getElementById('manualScanner');
            const cameraModeBtn = document.getElementById('cameraMode');
            const manualModeBtn = document.getElementById('manualMode');
            const barcodeInput = document.getElementById('barcodeInput');
            const submitBarcode = document.getElementById('submitBarcode');

            let quaggaInitialized = false;

            function initializeQuagga() {
                if (quaggaInitialized) return;
                
                Quagga.init({
                    inputStream: {
                        name: "Live",
                        type: "LiveStream",
                        target: document.querySelector("#interactive"),
                        constraints: {
                            facingMode: "environment",
                            aspectRatio: { min: 1.6, max: 1.8 }
                        },
                    },
                    decoder: {
                        readers: [
                            "ean_reader",
                            "ean_8_reader",
                            "code_128_reader",
                            "code_39_reader",
                            "upc_reader"
                        ]
                    }
                }, (err) => {
                    if (err) {
                        console.error('Scanner initialization failed:', err);
                        return;
                    }
                    console.log("Scanner ready");
                    Quagga.start();
                    quaggaInitialized = true;
                });
            }

            async function processBarcode(code) {
                scanText.textContent = `Item Code: ${code}`;
                googleSearch.href = `https://www.google.com/search?q=${encodeURIComponent(code)}`;
                resultDiv.classList.remove('d-none');
                barcodeDisplay.classList.remove('d-none');
                barcodeInfo.classList.remove('d-none');

                // Generate barcode
                JsBarcode("#barcodeImage", code, {
                    format: "CODE128",
                    width: 2,
                    height: 100,
                    displayValue: true
                });

                // Get barcode type
                let barcodeTypeText = '';
                if (code.length === 8) barcodeTypeText = 'EAN-8';
                else if (code.length === 13) barcodeTypeText = 'EAN-13';
                else if (code.length === 12) barcodeTypeText = 'UPC-A';
                else barcodeTypeText = 'CODE-128';

                barcodeType.textContent = `Type: ${barcodeTypeText}`;
                barcodeDetails.innerHTML = '<p>Click "Search on Google" for more information</p>';

                try {
                    const response = await fetch('process_barcode.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ 
                            barcode: code,
                            type: barcodeTypeText
                        })
                    });
                    
                    const data = await response.json();
                    console.log('Success:', data);
                } catch (error) {
                    console.error('Error:', error);
                }
            }

            // Mode switching
            cameraModeBtn.addEventListener('click', () => {
                cameraModeBtn.classList.add('active');
                manualModeBtn.classList.remove('active');
                cameraScanner.classList.remove('d-none');
                manualScanner.classList.add('d-none');
                initializeQuagga();
            });

            manualModeBtn.addEventListener('click', () => {
                manualModeBtn.classList.add('active');
                cameraModeBtn.classList.remove('active');
                manualScanner.classList.remove('d-none');
                cameraScanner.classList.add('d-none');
                if (quaggaInitialized) {
                    Quagga.stop();
                    quaggaInitialized = false;
                }
                barcodeInput.focus();
            });

            // Manual input handling
            submitBarcode.addEventListener('click', () => {
                const code = barcodeInput.value.trim();
                if (code) {
                    processBarcode(code);
                    barcodeInput.value = '';
                }
            });

            barcodeInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    const code = barcodeInput.value.trim();
                    if (code) {
                        processBarcode(code);
                        barcodeInput.value = '';
                    }
                }
            });

            // Camera detection
            Quagga.onDetected((result) => {
                const code = result.codeResult.code;
                processBarcode(code);
            });

            // Initialize camera mode by default
            initializeQuagga();
        });
    </script>
</body>
</html>
