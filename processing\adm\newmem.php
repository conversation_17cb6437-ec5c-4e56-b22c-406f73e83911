<?php
require_once '../../database.php';

// INSERT
if (isset($_POST['submit'])) {
    $sql = "INSERT INTO newmemtracker (phicnum, memlast, memfirst, memmid, memext, membday, 
            deplast, depfirst, depmid, depext, depbday, depgender, dadmitted, ddischarge, 
            dsubmitted, trasmittalnum, status) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $_POST['phicnum'], $_POST['memlast'], $_POST['memfirst'], $_POST['memmid'], $_POST['memext'],
        $_POST['membday'], $_POST['deplast'], $_POST['depfirst'], $_POST['depmid'], $_POST['depext'],
        $_POST['depbday'], $_POST['depgender'], $_POST['dadmitted'], $_POST['ddischarge'],
        $_POST['dsubmitted'], $_POST['trasmittalnum'], $_POST['status']
    ]);
}

// UPDATE
if (isset($_POST['update'])) {
    // Handle empty date fields
    $membday = !empty($_POST['membday']) ? $_POST['membday'] : null;
    $depbday = !empty($_POST['depbday']) ? $_POST['depbday'] : null;
    $dadmitted = !empty($_POST['dadmitted']) ? $_POST['dadmitted'] : null;
    $ddischarge = !empty($_POST['ddischarge']) ? $_POST['ddischarge'] : null;
    $dsubmitted = !empty($_POST['dsubmitted']) ? $_POST['dsubmitted'] : null;
    
    $sql = "UPDATE newmemtracker SET 
            phicnum=?, memlast=?, memfirst=?, memmid=?, memext=?, membday=?,
            deplast=?, depfirst=?, depmid=?, depext=?, depbday=?, depgender=?,
            dadmitted=?, ddischarge=?, dsubmitted=?, trasmittalnum=?, status=?
            WHERE id=?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $_POST['phicnum'], $_POST['memlast'], $_POST['memfirst'], $_POST['memmid'], $_POST['memext'],
        $membday, $_POST['deplast'], $_POST['depfirst'], $_POST['depmid'], $_POST['depext'],
        $depbday, $_POST['depgender'], $dadmitted, $ddischarge, $dsubmitted,
        $_POST['trasmittalnum'], $_POST['status'], $_POST['id']
    ]);
}

// SEARCH
$search = isset($_GET['search']) ? $_GET['search'] : '';
$sql = "SELECT *, 
        CASE 
            WHEN status = 'Completed' THEN 3
            WHEN status = 'Pending' THEN 2
            ELSE 1
        END as priority 
        FROM newmemtracker 
        WHERE phicnum LIKE ? OR memlast LIKE ? OR memfirst LIKE ? OR 
        deplast LIKE ? OR depfirst LIKE ? 
        ORDER BY priority ASC, ddischarge ASC";
$stmt = $conn->prepare($sql);
$searchTerm = "%$search%";
$stmt->execute([$searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm]);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Member/Dependent Tracker</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 20px auto; }
        .table-responsive { margin-top: 20px; }
        .modal-lg { max-width: 800px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card shadow-lg rounded-4 border-0">
            <div class="card-header bg-gradient bg-primary bg-opacity-25 border-0 py-4 d-flex justify-content-between align-items-center">
                <h3 class="mb-0 text-primary">
                    <i class="fas fa-users me-2"></i>New Member/Dependent Tracker
                </h3>
                <div class="d-flex align-items-center">
                    <button type="button" class="btn btn-primary btn-lg rounded-3 shadow-sm" data-bs-toggle="modal" data-bs-target="#memberModal">
                        <i class="fas fa-user-plus me-2"></i>New Record
                    </button>
                    <div class="ms-2">
                        <a href="../../index.php" class="btn btn-outline-primary btn-lg rounded-3 shadow-sm">
                            <i class="fas fa-home me-2"></i>Homepage
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                <form method="GET" class="mb-4">
                    <div class="input-group input-group-lg shadow-sm rounded-3 overflow-hidden">
                        <span class="input-group-text border-0 bg-light">
                            <i class="fas fa-search text-primary"></i>
                        </span>
                        <input type="text" class="form-control border-0 bg-light py-3" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Search by PHIC number or name...">
                        <button class="btn btn-primary px-4" type="submit">
                            <i class="fas fa-search-plus me-2"></i>Search
                        </button>
                    </div>
                </form>

                <div class="table-responsive rounded-3 border">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="bg-light">
                            <tr>
                                <th><i class="fas fa-id-card me-2"></i>PHIC Number</th>
                                <th><i class="fas fa-user me-2"></i>Member</th>
                                <th><i class="fas fa-user-plus me-2"></i>Dependent</th>
                                <th><i class="fas fa-calendar-alt me-2"></i>Dates</th>
                                <th><i class="fas fa-file-alt me-2"></i>Transmittal</th>
                                <th><i class="fas fa-chart-line me-2"></i>Status</th>
                                <th><i class="fas fa-cog me-2"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($results as $row): ?>
                            <tr>
                                <td class="fw-bold text-primary"><?php echo htmlspecialchars($row['phicnum']); ?></td>
                                <td><?php echo htmlspecialchars($row['memlast'] . ', ' . $row['memfirst']); ?></td>
                                <td><?php echo htmlspecialchars($row['deplast'] . ', ' . $row['depfirst']); ?></td>
                                <td>
                                    <small class="d-block text-muted">
                                        <i class="fas fa-hospital me-1"></i>Admitted: <?php echo htmlspecialchars($row['dadmitted']); ?>
                                    </small>
                                    <?php
                                    if (!empty($row['ddischarge'])) {
                                        $dischargeDate = new DateTime($row['ddischarge']);
                                        $today = new DateTime();
                                        $diff = $today->diff($dischargeDate);
                                        $daysPast = $diff->days;
                                        
                                        echo '<small class="d-block">';
                                        echo '<i class="fas fa-home me-1"></i>Discharged: ';
                                        if ($daysPast >= 60) {
                                            echo '<span class="pulsing text-danger">';
                                            echo htmlspecialchars($row['ddischarge']);
                                            echo ' <small>(' . $daysPast . ' days ago)</small>';
                                            echo '</span>';
                                        } elseif ($daysPast >= 30) {
                                            echo '<span class="pulsing text-warning">';
                                            echo htmlspecialchars($row['ddischarge']);
                                            echo ' <small>(' . $daysPast . ' days ago)</small>';
                                            echo '</span>';
                                        } else {
                                            echo htmlspecialchars($row['ddischarge']);
                                            echo ' <small class="text-muted">(' . $daysPast . ' days ago)</small>';
                                        }
                                        echo '</small>';
                                        
                                        // Add CSS for pulsing zoom effect
                                        echo '<style>
                                            @keyframes pulse {
                                                0% { transform: scale(1); }
                                                50% { transform: scale(1.1); }
                                                100% { transform: scale(1); }
                                            }
                                            .pulsing {
                                                display: inline-block;
                                                animation: pulse 1.5s ease-in-out infinite;
                                                transform-origin: center;
                                            }
                                        </style>';
                                    }
                                    ?>
                                    <?php
                                    if (!empty($row['dsubmitted'])) {
                                        $submitDate = new DateTime($row['dsubmitted']);
                                        $today = new DateTime();
                                        $diff = $today->diff($submitDate);
                                        $daysPast = $diff->days;
                                        
                                        echo '<small class="d-block">';
                                        echo '<i class="fas fa-paper-plane me-1"></i>Submitted: ';
                                        if ($daysPast >= 60) {
                                            echo '<span class="text-danger">' . htmlspecialchars($row['dsubmitted']) . '</span>';
                                            echo ' <small class="text-danger">(' . $daysPast . ' days ago)</small>';
                                        } elseif ($daysPast >= 30) {
                                            echo '<span class="text-warning">' . htmlspecialchars($row['dsubmitted']) . '</span>';
                                            echo ' <small class="text-warning">(' . $daysPast . ' days ago)</small>';
                                        } else {
                                            echo htmlspecialchars($row['dsubmitted']);
                                            echo ' <small class="text-muted">(' . $daysPast . ' days ago)</small>';
                                        }
                                        echo '</small>';
                                    }
                                    ?>
                                </td>
                                <td><?php echo htmlspecialchars($row['trasmittalnum']); ?></td>
                                <td>
                                    <span class="badge rounded-pill bg-<?php 
                                        echo $row['status'] == 'Pending' ? 'secondary' : 
                                            ($row['status'] == 'Submitted' ? 'warning' : 
                                            ($row['status'] == 'Completed' ? 'success' : 
                                            ($row['status'] == 'Non-PhilHealth' ? 'dark' : 'secondary'))); 
                                    ?> px-3 py-2">
                                        <i class="fas fa-<?php 
                                            echo $row['status'] == 'Pending' ? 'hourglass' : 
                                                ($row['status'] == 'Submitted' ? 'paper-plane' : 
                                                ($row['status'] == 'Completed' ? 'check' : 
                                                ($row['status'] == 'Non-PhilHealth' ? 'times' : 'question'))); 
                                        ?> me-1"></i>
                                        <?php echo htmlspecialchars($row['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-info btn-sm rounded-3" onclick="editMember(<?php echo htmlspecialchars(json_encode($row)); ?>)">
                                        <i class="fas fa-pen-to-square me-1"></i> Edit
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Add/Edit -->
    <div class="modal fade" id="memberModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="modalTitle">
                        <i class="fas fa-user-plus me-2"></i>Add New Member
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="memberForm" method="POST" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <input type="hidden" name="id" id="member_id">
                        
                        <!-- Member Information -->
                        <div class="card mb-4 border-primary">
                            <div class="card-header bg-primary bg-opacity-10">
                                <i class="fas fa-user me-2"></i>Member Information
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-12">
                                        <label class="form-label">
                                            <i class="fas fa-id-card me-2"></i>PHIC Number
                                        </label>
                                        <input type="text" class="form-control" name="phicnum" id="phicnum" required>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Last Name</label>
                                        <input type="text" class="form-control" name="memlast" id="memlast" required>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">First Name</label>
                                        <input type="text" class="form-control" name="memfirst" id="memfirst" required>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Middle Name</label>
                                        <input type="text" class="form-control" name="memmid" id="memmid">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Extension</label>
                                        <input type="text" class="form-control" name="memext" id="memext">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">
                                            <i class="fas fa-calendar me-2"></i>Birth Date
                                        </label>
                                        <input type="date" class="form-control" name="membday" id="membday">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Dependent Information -->
                        <div class="card mb-4 border-info">
                            <div class="card-header bg-info bg-opacity-10">
                                <i class="fas fa-user-plus me-2"></i>Dependent Information
                                <div class="form-check float-end">
                                    <input class="form-check-input" type="checkbox" id="sameAsMember">
                                    <label class="form-check-label" for="sameAsMember">
                                        Same as Member
                                    </label>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <label class="form-label">Last Name</label>
                                        <input type="text" class="form-control" name="deplast" id="deplast" required>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">First Name</label>
                                        <input type="text" class="form-control" name="depfirst" id="depfirst" required>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Middle Name</label>
                                        <input type="text" class="form-control" name="depmid" id="depmid">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Extension</label>
                                        <input type="text" class="form-control" name="depext" id="depext">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">
                                            <i class="fas fa-calendar me-2"></i>Birth Date
                                        </label>
                                        <input type="date" class="form-control" name="depbday" id="depbday">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">
                                            <i class="fas fa-venus-mars me-2"></i>Gender
                                        </label>
                                        <select class="form-select" name="depgender" id="depgender" required>
                                            <option value="">Select Gender</option>
                                            <option value="Male">Male</option>
                                            <option value="Female">Female</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <script>
                                document.getElementById('sameAsMember').addEventListener('change', function() {
                                    if (this.checked) {
                                        document.getElementById('deplast').value = document.getElementById('memlast').value;
                                        document.getElementById('depfirst').value = document.getElementById('memfirst').value;
                                        document.getElementById('depmid').value = document.getElementById('memmid').value;
                                        document.getElementById('depext').value = document.getElementById('memext').value;
                                        document.getElementById('depbday').value = document.getElementById('membday').value;
                                    } else {
                                        document.getElementById('deplast').value = '';
                                        document.getElementById('depfirst').value = '';
                                        document.getElementById('depmid').value = '';
                                        document.getElementById('depext').value = '';
                                        document.getElementById('depbday').value = '';
                                        document.getElementById('depgender').value = '';
                                    }
                                });
                            </script>
                        </div>

                        <!-- Transaction Details -->
                        <div class="card mb-4 border-success">
                            <div class="card-header bg-success bg-opacity-10">
                                <i class="fas fa-file-alt me-2"></i>Transaction Details
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <label class="form-label">
                                            <i class="fas fa-hospital me-2"></i>Date Admitted
                                        </label>
                                        <input type="date" class="form-control" name="dadmitted" id="dadmitted" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">
                                            <i class="fas fa-home me-2"></i>Date Discharged
                                        </label>
                                        <input type="date" class="form-control" name="ddischarge" id="ddischarge" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">
                                            <i class="fas fa-paper-plane me-2"></i>Date Submitted
                                        </label>
                                        <input type="date" class="form-control" name="dsubmitted" id="dsubmitted">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">
                                            <i class="fas fa-file-invoice me-2"></i>Transmittal Number
                                        </label>
                                        <input type="text" class="form-control" name="trasmittalnum" id="trasmittalnum">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">
                                            <i class="fas fa-tasks me-2"></i>Status
                                        </label>
                                        <select class="form-select" name="status" id="status" required>
                                            <option value="Pending" class="text-secondary">Pending</option>
                                            <option value="Submitted" class="text-warning">Submitted</option>
                                            <option value="Completed" class="text-success">Completed</option>
                                            <option value="Non-PhilHealth" class="text-dark">Non-PhilHealth</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Close
                        </button>
                        <button type="submit" class="btn btn-primary" id="submitBtn" name="submit">
                            <i class="fas fa-save me-2"></i>Save Member
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editMember(data) {
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-edit me-2"></i>Edit Member';
            document.getElementById('member_id').value = data.id;
            
            // Fill form fields
            document.getElementById('phicnum').value = data.phicnum;
            document.getElementById('memlast').value = data.memlast;
            document.getElementById('memfirst').value = data.memfirst;
            document.getElementById('memmid').value = data.memmid;
            document.getElementById('memext').value = data.memext;
            document.getElementById('membday').value = data.membday;
            document.getElementById('deplast').value = data.deplast;
            document.getElementById('depfirst').value = data.depfirst;
            document.getElementById('depmid').value = data.depmid;
            document.getElementById('depext').value = data.depext;
            document.getElementById('depbday').value = data.depbday;
            document.getElementById('depgender').value = data.depgender;
            document.getElementById('dadmitted').value = data.dadmitted;
            document.getElementById('ddischarge').value = data.ddischarge;
            document.getElementById('dsubmitted').value = data.dsubmitted;
            document.getElementById('trasmittalnum').value = data.trasmittalnum;
            document.getElementById('status').value = data.status;
            
            // Change submit button
            document.getElementById('submitBtn').name = 'update';
            document.getElementById('submitBtn').innerHTML = '<i class="fas fa-save me-2"></i>Update Member';
            
            // Show modal
            new bootstrap.Modal(document.getElementById('memberModal')).show();
        }

        // Reset form when modal is closed
        document.getElementById('memberModal').addEventListener('hidden.bs.modal', function () {
            document.getElementById('memberForm').reset();
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-user-plus me-2"></i>Add New Member';
            document.getElementById('submitBtn').name = 'submit';
            document.getElementById('submitBtn').innerHTML = '<i class="fas fa-save me-2"></i>Save Member';
        });
    </script>
</body>
</html>
