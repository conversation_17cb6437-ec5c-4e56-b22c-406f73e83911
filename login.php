<?php
session_start();
require_once 'database.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = $_POST['username'];
    $password = $_POST['password'];

    try {
        $stmt = $conn->prepare("SELECT u.*, d.department_name 
            FROM users u 
            INNER JOIN employees e ON u.emp_id = e.id 
            INNER JOIN departments d ON d.id = e.department_id 
            WHERE u.username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch();

        if ($user && password_verify($password, $user['password'])) {
            $_SESSION['user_id'] = $user['user_id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['type'] = $user['type'];
            $_SESSION['department'] = $user['department_name'];
            
            header("Location: index.php");
            exit();
        } else {
            $error = "Invalid username or password";
        }
    } catch(PDOException $e) {
        $error = "Error: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - BIRI District Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary: #64748b;
            --background: rgba(248, 250, 252, 0.9);
        }
        body {
            font-family: 'Inter', sans-serif;
            background: url('images/bdh.jpg') no-repeat center center fixed;
            background-size: cover;
            min-height: 100vh;
            display: grid;
            place-items: center;
            margin: 0;
            padding: 1rem;
            position: relative;
        }
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 2.5rem;
            border-radius: 1rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 440px;
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 2;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .login-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }
        .login-header h1 {
            font-size: 1.875rem;
            font-weight: 700;
            color: #1a365d;
            margin-bottom: 0.75rem;
            letter-spacing: -0.025em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        .login-header p {
            color: var(--secondary);
            font-size: 1rem;
            font-weight: 500;
        }
        .form-label {
            color: #334155;
            font-weight: 600;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }
        .input-group {
            border: 2px solid #e2e8f0;
            border-radius: 0.75rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }
        .input-group:focus-within {
            border-color: var(--primary);
            box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.2);
            transform: translateY(-1px);
        }
        .input-group-text {
            background-color: transparent;
            border: none;
            color: var(--secondary);
            padding-left: 1.25rem;
        }
        .form-control {
            border: none;
            padding: 0.875rem;
            font-size: 0.925rem;
            background: transparent;
        }
        .form-control:focus {
            box-shadow: none;
        }
        .btn-login {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 0.875rem;
            width: 100%;
            font-weight: 600;
            border: none;
            border-radius: 0.75rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .btn-login:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }
        .alert {
            border: none;
            border-radius: 0.75rem;
            font-size: 0.875rem;
            padding: 1rem;
            background: rgba(220, 38, 38, 0.1);
            border-left: 4px solid #dc2626;
        }
        .footer-info {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid rgba(226, 232, 240, 0.7);
            color: var(--secondary);
            font-size: 0.75rem;
            line-height: 1.25rem;
        }
        .footer-info p {
            margin-bottom: 0.25rem;
            text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1><i class="fas fa-hospital me-2"></i>BIRI District Hospital</h1>
            <p>Electronic Data Server (ED-S) Management System</p>
        </div>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-danger" role="alert">
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="">
            <div class="mb-4">
                <label for="username" class="form-label">Username</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                    <input type="text" class="form-control" id="username" name="username" placeholder="Enter your username" required>
                </div>
            </div>
            <div class="mb-4">
                <label for="password" class="form-label">Password</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                    <input type="password" class="form-control" id="password" name="password" placeholder="Enter your password" required>
                </div>
            </div>
            <button type="submit" class="btn btn-login">
                Sign In
                <i class="fas fa-arrow-right ms-2"></i>
            </button>
        </form>
        <div class="footer-info">
            <p>Created by: EDUARDO O. SABANGAN, JR</p>
            <p>Email: <EMAIL></p>
            <p>Contact: 09394736830</p>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
