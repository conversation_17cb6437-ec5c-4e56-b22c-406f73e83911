<?php
require_once 'database.php';

try {
    // Get current year
    $currentYear = date('Y');
    
    // Get total allocation for current year
    $stmt = $conn->prepare("SELECT SUM(account_amount) as total FROM accounts WHERE account_year = ?");
    $stmt->execute([$currentYear]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode(['totalAllocation' => floatval($result['total'])]);
} catch(PDOException $e) {
    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode(['error' => $e->getMessage()]);
}
