<?php
// Database connection parameters
include_once '../config/database.php'; 
// Handle form submissions
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['save'])) {
        $full_name = $_POST['full_name'];
        $position = $_POST['position'];
        $phone_number = $_POST['phone_number'];
        
        $sql = "INSERT INTO employees (full_name, position, phone_number, hired_at) 
                VALUES (?, ?, ?, NOW())";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$full_name, $position, $phone_number]);
    }
}
if (isset($_POST['update']) && isset($_POST['employee_id'])) {
    $employee_id = $_POST['employee_id'];
    $full_name = $_POST['full_name'];
    $position = $_POST['position'];
    $phone_number = $_POST['phone_number'];
    
    $sql = "UPDATE employees SET full_name=?, position=?, phone_number=? WHERE employee_id=?";
    $sql = "UPDATE employees SET full_name=?, position=?, phone_number=? WHERE employee_id=?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$full_name, $position, $phone_number, $employee_id]);
}

// Search functionality
$search = isset($_GET['search']) ? $_GET['search'] : '';
$sql = "SELECT * FROM employees WHERE full_name LIKE ? OR position LIKE ?";
$search_term = "%$search%";
$sql = "SELECT * FROM employees WHERE full_name LIKE ? OR position LIKE ?";
$search_term = "%$search%";
$stmt = $conn->prepare($sql);
$stmt->execute([$search_term, $search_term]);
$result = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Employee Management</title>
    <link rel="icon" type="image/x-icon" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; }
        .table thead th { background-color: #0d6efd; color: white; }
        .input-group { box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .table { box-shadow: 0 0 20px rgba(0,0,0,0.1); }
    </style>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <h2>Employee Management</h2>
        
        <!-- Search Form -->
        <form method="GET" class="mb-4">
            <div class="input-group">
                <span class="input-group-text"><i class="fa-solid fa-magnifying-glass"></i></span>
                <input type="text" name="search" class="form-control" placeholder="Search employees..." value="<?php echo htmlspecialchars($search); ?>">
                <button type="submit" class="btn btn-primary"><i class="fa-solid fa-search"></i> Search</button>
            </div>
        </form>

        <!-- Add/Edit Employee Form -->
        <form method="POST" class="mb-4">
            <input type="hidden" name="employee_id" id="employee_id">
            <div class="row mb-3">
            <div class="col">
                <div class="input-group">
                <span class="input-group-text"><i class="fa-solid fa-user"></i></span>
                <input type="text" name="full_name" class="form-control" placeholder="Full Name" required>
                </div>
            </div>
            <div class="col">
                <div class="input-group">
                <span class="input-group-text"><i class="fa-solid fa-briefcase"></i></span>
                <input type="text" name="position" class="form-control" placeholder="Position" required>
                </div>
            </div>
            <div class="col">
                <div class="input-group">
                <span class="input-group-text"><i class="fa-solid fa-phone"></i></span>
                <input type="tel" name="phone_number" class="form-control" placeholder="Phone Number" required>
                </div>
            </div>
            <div class="col">
                <div class="btn-group" role="group">
                    <button type="submit" name="save" class="btn btn-success"><i class="fa-solid fa-plus"></i> Save New</button>
                    <button type="submit" name="update" class="btn btn-warning"><i class="fa-solid fa-pen"></i> Update</button>
                    <a href="../index.php" class="btn btn-secondary"><i class="fa-solid fa-home"></i> Home</a>
                </div>
            </div>
            </div>
        </form>

        <!-- Employees Table -->
        <div style="max-height: 400px; overflow-y: auto;">
            <table class="table table-striped">
            <thead style="position: sticky; top: 0; background: white;">
                <tr>
                <th><i class="fa-solid fa-id-card"></i> ID</th>
                <th><i class="fa-solid fa-user"></i> Full Name</th>
                <th><i class="fa-solid fa-briefcase"></i> Position</th>
                <th><i class="fa-solid fa-phone"></i> Phone Number</th>
                <th><i class="fa-solid fa-calendar-days"></i> Hired At</th>
                <th><i class="fa-solid fa-gear"></i> Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($result as $row): ?>
                <tr>
                <td><?php echo htmlspecialchars($row['employee_id']); ?></td>
                <td><?php echo htmlspecialchars($row['full_name']); ?></td>
                <td><?php echo htmlspecialchars($row['position']); ?></td>
                <td><?php echo htmlspecialchars($row['phone_number']); ?></td>
                <td><?php echo htmlspecialchars($row['hired_at']); ?></td>
                <td>
                    <button onclick="editEmployee(<?php echo htmlspecialchars(json_encode($row)); ?>)" class="btn btn-sm btn-primary">Edit</button>
                </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
            </table>
        </div>
    </div>

    <script>
    function editEmployee(employee) {
        document.querySelector('[name="employee_id"]').value = employee.employee_id;
        document.querySelector('[name="full_name"]').value = employee.full_name;
        document.querySelector('[name="position"]').value = employee.position;
        document.querySelector('[name="phone_number"]').value = employee.phone_number;
    }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>