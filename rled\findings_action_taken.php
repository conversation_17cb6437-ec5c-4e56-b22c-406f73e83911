<?php
require_once 'databasehdbo.php';

// Create findings_action_taken table if it doesn't exist
$sql = "CREATE TABLE IF NOT EXISTS findings_action_taken (
    id INT AUTO_INCREMENT PRIMARY KEY,
    findings_id INT NOT NULL,
    action_description TEXT,
    action_date DATE,
    action_responsible_person VARCHAR(255),
    action_status VARCHAR(50),
    action_attachment VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (findings_id) REFERENCES findings(id) ON DELETE CASCADE
)";

$conn->exec($sql);

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $attachment_name = null;
    if (isset($_FILES['attachment']) && $_FILES['attachment']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = 'uploads/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }
        
        $file_extension = strtolower(pathinfo($_FILES['attachment']['name'], PATHINFO_EXTENSION));
        $allowed_extensions = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'];
        
        if (in_array($file_extension, $allowed_extensions) && $_FILES['attachment']['size'] <= 5242880) { // 5MB limit
            $attachment_name = uniqid() . '.' . $file_extension;
            move_uploaded_file($_FILES['attachment']['tmp_name'], $upload_dir . $attachment_name);
        }
    }

    if (isset($_POST['action']) && $_POST['action'] === 'add') {
        // Insert new action
        $insert_sql = "INSERT INTO findings_action_taken (findings_id, action_description, action_date, 
                      action_responsible_person, action_status, action_attachment) 
                      VALUES (:findings_id, :description, :date, :responsible, :status, :attachment)";
        
        $stmt = $conn->prepare($insert_sql);
        $stmt->execute([
            ':findings_id' => $_POST['findings_id'],
            ':description' => $_POST['action_description'],
            ':date' => $_POST['action_date'],
            ':responsible' => $_POST['responsible_person'],
            ':status' => $_POST['status'],
            ':attachment' => $attachment_name
        ]);
    } elseif (isset($_POST['action']) && $_POST['action'] === 'update') {
        // Get current attachment
        $stmt = $conn->prepare("SELECT action_attachment FROM findings_action_taken WHERE id = ?");
        $stmt->execute([$_POST['action_id']]);
        $current_attachment = $stmt->fetchColumn();

        // Update existing action
        $update_sql = "UPDATE findings_action_taken 
                      SET action_description = :description,
                          action_date = :date,
                          action_responsible_person = :responsible,
                          action_status = :status,
                          action_attachment = :attachment
                      WHERE id = :id AND findings_id = :findings_id";
        
        $stmt = $conn->prepare($update_sql);
        $stmt->execute([
            ':description' => $_POST['action_description'],
            ':date' => $_POST['action_date'],
            ':responsible' => $_POST['responsible_person'],
            ':status' => $_POST['status'],
            ':attachment' => $attachment_name ?? $current_attachment,
            ':id' => $_POST['action_id'],
            ':findings_id' => $_POST['findings_id']
        ]);

        // Delete old attachment if new one was uploaded
        if ($attachment_name && $current_attachment) {
            $old_file = 'uploads/' . $current_attachment;
            if (file_exists($old_file)) {
                unlink($old_file);
            }
        }
    }
    
    // Redirect to prevent form resubmission
    header("Location: " . $_SERVER['PHP_SELF'] . "?findings_id=" . $_POST['findings_id']);
    exit();
}

// Validate and sanitize findings_id
if (!isset($_GET['findings_id']) || !is_numeric($_GET['findings_id'])) {
    die("Invalid findings ID");
}
$findings_id = filter_var($_GET['findings_id'], FILTER_SANITIZE_NUMBER_INT);

// Get actions for the finding
$query = "SELECT * FROM findings_action_taken WHERE findings_id = :findings_id ORDER BY action_date DESC, id DESC";
$stmt = $conn->prepare($query);
$stmt->bindParam(':findings_id', $findings_id, PDO::PARAM_INT);
$stmt->execute();
$result = $stmt;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Action Taken Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 20px auto; }
        .table-responsive { margin-top: 20px; }
        .modal-lg { max-width: 800px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-clipboard-list me-2"></i>Action Taken Management</h2>
            <div>
                <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#actionModal">
                    <i class="fas fa-plus-circle me-2"></i>Add New Action
                </button>
                <a href="rled_audit_findings.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Findings
                </a>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-hover bg-white rounded shadow-sm">
                <thead class="table-light">
                    <tr>
                        <th><i class="fas fa-file-alt me-2"></i>ID</th>
                        <th><i class="fas fa-file-alt me-2"></i>Description</th>
                        <th><i class="fas fa-calendar me-2"></i>Date</th>
                        <th><i class="fas fa-user me-2"></i>Responsible Person</th>
                        <th><i class="fas fa-tasks me-2"></i>Status</th>
                        <th><i class="fas fa-paperclip me-2"></i>Attachment</th>
                        <th><i class="fas fa-cogs me-2"></i>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($row = $result->fetch(PDO::FETCH_ASSOC)): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($row['id']); ?></td>
                            <td><?php echo htmlspecialchars($row['action_description']); ?></td>
                            <td><?php echo htmlspecialchars($row['action_date']); ?></td>
                            <td><?php echo htmlspecialchars($row['action_responsible_person']); ?></td>
                            <td>
                                <span class="badge <?php 
                                    echo match($row['action_status']) {
                                        'Completed' => 'bg-success',
                                        'In Progress' => 'bg-warning',
                                        default => 'bg-secondary'
                                    };
                                ?>">
                                    <?php echo htmlspecialchars($row['action_status']); ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($row['action_attachment']): ?>
                                    <a href="uploads/<?php echo htmlspecialchars($row['action_attachment']); ?>" 
                                       target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-download me-1"></i>Download
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted"><i class="fas fa-times me-1"></i>No attachment</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-primary" 
                                        data-bs-toggle="modal" data-bs-target="#actionModal"
                                        onclick="editAction(<?php echo $row['id']; ?>)">
                                    <i class="fas fa-edit"></i>
                                </button>

                                <button class="btn btn-sm btn-danger" onclick="deleteAction(<?php echo $row['id']; ?>)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Single Modal for both Add and Edit -->
    <div class="modal fade" id="actionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" id="modalHeader">
                    <h5 class="modal-title" id="modalTitle">
                        <i class="fas fa-plus-circle me-2"></i>New Action
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="actionForm" method="POST" action="" enctype="multipart/form-data" class="needs-validation" novalidate>
                    <input type="hidden" name="action" id="formAction" value="add">
                    <input type="hidden" name="action_id" id="actionId" value="">
                    <input type="hidden" name="findings_id" value="<?php echo htmlspecialchars($findings_id); ?>">
                    <div class="modal-body p-4">
                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-file-alt me-2"></i>Action Description
                            </label>
                            <textarea class="form-control" name="action_description" id="actionDescription" rows="4" required></textarea>
                            <div class="invalid-feedback">Please provide an action description.</div>
                        </div>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">
                                    <i class="fas fa-calendar me-2"></i>Action Date
                                </label>
                                <input type="date" class="form-control" name="action_date" id="actionDate" required>
                                <div class="invalid-feedback">Please select a date.</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">
                                    <i class="fas fa-user me-2"></i>Responsible Person
                                </label>
                                <input type="text" class="form-control" name="responsible_person" id="responsiblePerson" required>
                                <div class="invalid-feedback">Please enter responsible person.</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">
                                    <i class="fas fa-tasks me-2"></i>Status
                                </label>
                                <select class="form-select" name="status" id="actionStatus" required>
                                    <option value="">Select Status</option>
                                    <option value="Pending">Pending</option>
                                    <option value="In Progress">In Progress</option>
                                    <option value="Completed">Completed</option>
                                </select>
                                <div class="invalid-feedback">Please select a status.</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">
                                    <i class="fas fa-paperclip me-2"></i>Attachment
                                </label>
                                <input type="file" class="form-control" name="attachment" id="actionAttachment">
                                <div class="form-text">Max file size: 5MB</div>
                                <div id="currentAttachment" class="mt-2"></div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Cancel
                        </button>
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-save me-2"></i><span id="submitBtnText">Save Action</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form validation
        (() => {
            'use strict'
            const forms = document.querySelectorAll('.needs-validation')
            Array.from(forms).forEach(form => {
                form.addEventListener('submit', event => {
                    if (!form.checkValidity()) {
                        event.preventDefault()
                        event.stopPropagation()
                    }
                    form.classList.add('was-validated')
                }, false)
            })
        })()

        // Action handlers
        function editAction(id) {
            fetch(`get_action.php?id=${id}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('formAction').value = 'update';
                    document.getElementById('actionId').value = id;
                    document.getElementById('actionDescription').value = data.action_description;
                    document.getElementById('actionDate').value = data.action_date;
                    document.getElementById('responsiblePerson').value = data.action_responsible_person;
                    document.getElementById('actionStatus').value = data.action_status;
                    
                    // Show current attachment if exists
                    const attachmentDiv = document.getElementById('currentAttachment');
                    if (data.action_attachment) {
                        attachmentDiv.innerHTML = `
                            <small class="text-muted">Current file: 
                                <a href="uploads/${data.action_attachment}" target="_blank">${data.action_attachment}</a>
                            </small>`;
                    } else {
                        attachmentDiv.innerHTML = '<small class="text-muted">No current attachment</small>';
                    }
                    
                    // Update modal appearance
                    document.getElementById('modalTitle').innerHTML = '<i class="fas fa-edit me-2"></i>Edit Action';
                    document.getElementById('submitBtnText').textContent = 'Update Action';
                    
                    const modal = new bootstrap.Modal(document.getElementById('actionModal'));
                    modal.show();
                });
        }

        // Reset modal when closed
        document.getElementById('actionModal').addEventListener('hidden.bs.modal', function () {
            const form = document.getElementById('actionForm');
            form.reset();
            form.classList.remove('was-validated');
            document.getElementById('formAction').value = 'add';
            document.getElementById('actionId').value = '';
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-plus-circle me-2"></i>New Action';
            document.getElementById('submitBtnText').textContent = 'Save Action';
            document.getElementById('currentAttachment').innerHTML = '';
        });

        function deleteAction(id) {
            if (confirm('Are you sure you want to delete this action?')) {
                fetch(`delete_action.php?id=${id}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error deleting action. Please try again.');
                    }
                });
            }
        }
    </script>
</body>
</html>
