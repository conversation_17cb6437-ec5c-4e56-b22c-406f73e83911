<?php
require_once 'database.php';
require('fpdf/fpdf.php');

// Get current year
$currentYear = date('Y');

// Fetch transaction summary data
$transactionSummary = $conn->query("
    SELECT 
        t.transaction_id,
        t.transaction_ref,
        t.transaction_date,
        e.emp_name,
        d.department_name,
        COUNT(td.detail_id) as items_count,
        SUM(td.amount) as total_amount
    FROM transactions t
    JOIN employees e ON t.employee_id = e.id
    JOIN departments d ON e.department_id = d.id
    JOIN transaction_details td ON t.transaction_id = td.transaction_id
    GROUP BY t.transaction_id, t.transaction_ref, t.transaction_date, e.emp_name, d.department_name
    ORDER BY t.transaction_id DESC")->fetchAll();

// Calculate transaction statistics
$totalTransactions = count($transactionSummary);
$totalAmount = 0;
$departmentTotals = [];
$employeeTotals = [];

foreach ($transactionSummary as $trans) {
    $totalAmount += $trans['total_amount'];
    
    // Department statistics
    if (!isset($departmentTotals[$trans['department_name']])) {
        $departmentTotals[$trans['department_name']] = 0;
    }
    $departmentTotals[$trans['department_name']] += $trans['total_amount'];
    
    // Employee statistics
    if (!isset($employeeTotals[$trans['emp_name']])) {
        $employeeTotals[$trans['emp_name']] = 0;
    }
    $employeeTotals[$trans['emp_name']] += $trans['total_amount'];
}

arsort($departmentTotals);
arsort($employeeTotals);

// Create PDF document with financial styling
$pdf = new FPDF('P', 'mm', 'A4');
$pdf->AddPage();

// Add logos with professional spacing
$pdf->Image('images/pgns.png', 40, 10, 25);
$pdf->Image('images/bdh.png', 145, 10, 25);

// Header section with financial styling
$pdf->SetFont('Times', '', 12);
$pdf->Cell(0,6,'Republic of the Philippines',0,1,'C');
$pdf->Cell(0,6,'Province of Northern Samar',0,1,'C');
$pdf->Cell(0,6,'Provincial Health Office',0,1,'C');
$pdf->SetFont('Times', 'B', 16);
$pdf->Cell(0,8,'BIRI DISTRICT HOSPITAL',0,1,'C');
$pdf->SetFont('Times', 'I', 11);
$pdf->Cell(0,6,'Biri Northern Samar',0,1,'C');

// Report title with financial formatting
$pdf->SetFont('Times', 'B', 14);
$pdf->Cell(0, 10, 'FINANCIAL TRANSACTION REPORT', 0, 1, 'C');
$pdf->SetFont('Times', '', 11);
$pdf->Cell(0, 6, 'For the Period Ending ' . date('F d, Y'), 0, 1, 'C');
$pdf->Ln(10);

// Executive Summary Section
$pdf->SetFillColor(240, 240, 240);
$pdf->Rect(10, $pdf->GetY(), 190, 40, 2, 'F');
$pdf->SetFont('Times', 'B', 13);
$pdf->Cell(0, 10, 'EXECUTIVE SUMMARY', 0, 1, 'C');

// Professional borders
$pdf->SetDrawColor(100, 100, 100);
$pdf->Line(20, $pdf->GetY(), 190, $pdf->GetY());

// Key metrics with financial styling
$pdf->SetFont('Times', '', 11);
$pdf->Cell(130, 8, 'Aggregate Transaction Count:', 0, 0, 'L');
$pdf->SetFont('Times', 'B', 11);
$pdf->Cell(50, 8, number_format($totalTransactions), 0, 1, 'R');

$pdf->SetFont('Times', '', 11);
$pdf->Cell(130, 8, 'Total Financial Volume:', 0, 0, 'L');
$pdf->SetFont('Times', 'B', 11);
$pdf->Cell(50, 8, 'PHP ' . number_format($totalAmount, 2), 0, 1, 'R');

$pdf->Ln(15);

// Detailed Transaction Analysis
$pdf->SetFont('Times', 'B', 13);
$pdf->Cell(0, 8, 'DETAILED TRANSACTION ANALYSIS', 0, 1, 'L');
$pdf->SetFillColor(230, 230, 230);
$pdf->SetFont('Times', '', 10);

// Table layout optimization
$pageWidth = $pdf->GetPageWidth() - 20;
$requiredWidth = 190;

if ($pageWidth < $requiredWidth) {
    $pdf->AddPage('L');
}

// Enhanced table headers
$pdf->SetFillColor(220, 220, 220);
$pdf->SetFont('Times', 'B', 10);
$pdf->Cell(30, 8, 'Date', 1, 0, 'C', true);
$pdf->Cell(35, 8, 'Reference No.', 1, 0, 'C', true);
$pdf->Cell(50, 8, 'Personnel', 1, 0, 'C', true);
$pdf->Cell(45, 8, 'Cost Center', 1, 0, 'C', true);
$pdf->Cell(30, 8, 'Amount (PHP)', 1, 1, 'C', true);

// Transaction details with improved formatting
$pdf->SetFont('Times', '', 9);
foreach ($transactionSummary as $trans) {
    $empNameLines = ceil(strlen($trans['emp_name']) / 20);
    $deptNameLines = ceil(strlen($trans['department_name']) / 18);
    $lineHeight = max($empNameLines, $deptNameLines) * 6;
    
    $pdf->Cell(30, $lineHeight, date('m/d/Y', strtotime($trans['transaction_date'])), 1, 0, 'C');
    $pdf->Cell(35, $lineHeight, $trans['transaction_ref'], 1, 0, 'C');
    
    $x = $pdf->GetX();
    $y = $pdf->GetY();
    $pdf->MultiCell(50, $lineHeight/max(1,$empNameLines), wordwrap($trans['emp_name'], 20), 1, 'L');
    $pdf->SetXY($x + 50, $y);
    
    $x = $pdf->GetX();
    $pdf->MultiCell(45, $lineHeight/max(1,$deptNameLines), wordwrap($trans['department_name'], 18), 1, 'L');
    $pdf->SetXY($x + 45, $y);
    
    $pdf->Cell(30, $lineHeight, number_format($trans['total_amount'], 2), 1, 1, 'R');
}

$pdf->Ln(15);

// Authorization section with enhanced styling
$pdf->SetFont('Times', '', 11);
$pdf->Cell(95, 6, 'Prepared and Submitted by:', 0, 0, 'L');
$pdf->Cell(95, 6, 'Reviewed and Approved by:', 0, 1, 'L');
$pdf->Ln(15);

$pdf->SetFont('Times', 'B', 11);
$pdf->Cell(95, 5, 'EDUARDO O. SABANGAN, JR', 0, 0, 'L');
$pdf->Cell(95, 5, 'LUCILLE G. ROMINES, MD, FPCP', 0, 1, 'L');
$pdf->SetFont('Times', 'I', 10);
$pdf->Cell(95, 5, 'Senior Bookkeeper', 0, 0, 'L');
$pdf->Cell(95, 5, 'Chief of Hospital', 0, 1, 'L');

// Generate financial report
$pdf->Output('Financial_Transaction_Report_' . $currentYear . '.pdf', 'D');
