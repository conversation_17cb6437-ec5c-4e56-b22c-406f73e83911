<?php
require_once '../database.php';
require_once '../fpdf/fpdf.php';

class InventoryPDF extends FPDF {
    function Header() {
        $this->SetFont('Arial', 'B', 15);
        $this->Cell(0, 10, 'Inventory Report', 0, 1, 'C');
        $this->Ln(10);

        // Column headers
        $this->SetFont('Arial', 'B', 11);
        $this->Cell(40, 7, 'Date', 1);
        $this->Cell(60, 7, 'Item Name', 1);
        $this->Cell(60, 7, 'Patient Name', 1);
        $this->Cell(30, 7, 'Quantity', 1);
        $this->Ln();
    }

    function Footer() {
        $this->SetY(-15);
        $this->SetFont('Arial', 'I', 8);
        $this->Cell(0, 10, 'Page ' . $this->PageNo(), 0, 0, 'C');
    }
}

// Get all items for dropdown if needed
$items_sql = "SELECT itemid, generaldescription FROM items ORDER BY generaldescription";
$items_stmt = $conn->prepare($items_sql);
$items_stmt->execute();
$items = $items_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get selected filters
$type = $_GET['type'] ?? 'daily';
$selected_date = $_GET['date'] ?? date('Y-m-d');
$start_date = $_GET['start_date'] ?? date('Y-m-01');
$end_date = $_GET['end_date'] ?? date('Y-m-t');
$selected_item = $_GET['item_id'] ?? '';

// SQL based on type
if ($type === 'daily') {
    $sql = "
        SELECT
            DATE(pt.transaction_date) AS transaction_date,
            i.generaldescription AS item_name,
            p.patientname,
            SUM(pd.quantity) AS quantity_utilized
        FROM pharmatransactions pt
        JOIN pharmatransaction_details pd ON pt.transaction_id = pd.transaction_id
        JOIN patient p ON pt.patientid = p.patientid
        JOIN items i ON pd.item_id = i.itemid
        WHERE pt.transaction_date BETWEEN :date_start AND :date_end
        " . ($selected_item ? "AND i.itemid = :item_id " : "") . "
        GROUP BY DATE(pt.transaction_date), i.generaldescription, p.patientname
        ORDER BY transaction_date, item_name, p.patientname
    ";
    $stmt = $conn->prepare($sql);
    $date_start = $selected_date . ' 00:00:00';
    $date_end = $selected_date . ' 23:59:59';
    $stmt->bindParam(':date_start', $date_start);
    $stmt->bindParam(':date_end', $date_end);
} else {
    $sql = "
        SELECT
            pt.transaction_date,
            i.generaldescription AS item_name,
            p.patientname,
            pd.quantity AS quantity_utilized
        FROM pharmatransactions pt
        JOIN pharmatransaction_details pd ON pt.transaction_id = pd.transaction_id
        JOIN patient p ON pt.patientid = p.patientid
        JOIN items i ON pd.item_id = i.itemid
        WHERE pt.transaction_date BETWEEN :start_date AND :end_date
        " . ($selected_item ? "AND i.itemid = :item_id " : "") . "
        ORDER BY pt.transaction_date DESC, item_name, p.patientname
    ";
    $stmt = $conn->prepare($sql);
    $start_full = $start_date . ' 00:00:00';
    $end_full = $end_date . ' 23:59:59';
    $stmt->bindParam(':start_date', $start_full);
    $stmt->bindParam(':end_date', $end_full);
}

if ($selected_item) {
    $stmt->bindParam(':item_id', $selected_item);
}

// Execute and fetch results
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Debug if empty
if (isset($_GET['generate_pdf']) && empty($results)) {
    echo "<h3>No data found for selected filters.</h3>";
    echo "<pre>";
    print_r([
        'Report Type' => $type,
        'Date' => $selected_date,
        'Start Date' => $start_date,
        'End Date' => $end_date,
        'Item ID' => $selected_item,
        'SQL' => $stmt->queryString,
        'SQL Errors' => $stmt->errorInfo()
    ]);
    echo "</pre>";
    exit;
}

// Optional: Use dummy data for testing PDF structure
/*
$results = [
    ['transaction_date' => '2025-07-01', 'item_name' => 'Paracetamol', 'patientname' => 'Juan Dela Cruz', 'quantity_utilized' => 10],
    ['transaction_date' => '2025-07-02', 'item_name' => 'Amoxicillin', 'patientname' => 'Maria Santos', 'quantity_utilized' => 5],
];
*/

// Generate PDF
if (isset($_GET['generate_pdf'])) {
    $pdf = new InventoryPDF('L');
    $pdf->AddPage();
    $pdf->SetFont('Arial', '', 10);

    foreach ($results as $row) {
        $pdf->Cell(40, 6, date('Y-m-d', strtotime($row['transaction_date'])), 1);
        $pdf->Cell(60, 6, $row['item_name'], 1);
        $pdf->Cell(60, 6, $row['patientname'], 1);
        $pdf->Cell(30, 6, $row['quantity_utilized'], 1);
        $pdf->Ln();
    }

    $pdf->Output('Inventory_Report.pdf', 'D');
    exit;
}
?>
