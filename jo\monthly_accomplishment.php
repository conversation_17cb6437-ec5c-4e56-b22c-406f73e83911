<?php
require('../fpdf/fpdf.php');
require_once('../database.php');

// Get month and year from parameters
$month = isset($_GET['month']) ? $_GET['month'] : date('m');
$year = isset($_GET['year']) ? $_GET['year'] : date('Y');

// Create query to get monthly accomplishment report
$sql = "SELECT 
    dt.employee_id,
    jp.jo_name,
    t.task_description,
    SUM(dt.times_accomplished) as total_accomplished
FROM daily_tasks dt
JOIN tasks t ON dt.task_id = t.task_id
JOIN jo_profile jp ON dt.employee_id = jp.employee_id
WHERE MONTH(dt.task_date) = ? AND YEAR(dt.task_date) = ?
GROUP BY dt.employee_id, dt.task_id, jp.jo_name, t.task_description
ORDER BY jp.jo_name, t.task_description";

$stmt = $conn->prepare($sql);
$stmt->execute([$month, $year]);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Create professional PDF with enhanced styling
class PDF extends FPDF {
    function Header() {
        // Add logos with proper spacing
        $this->Image('../images/pgns.png', 90, 10, 25);
        $this->Image('../images/bdh.png', 180, 10, 25);
        
        // Official letterhead styling
        $this->SetFont('Times', '', 12);
        $this->Cell(0, 6, 'Republic of the Philippines', 0, 1, 'C');
        $this->Cell(0, 6, 'Province of Northern Samar', 0, 1, 'C');
        $this->Cell(0, 6, 'Provincial Health Office', 0, 1, 'C');
        $this->SetFont('Times', 'B', 16);
        $this->Cell(0, 8, 'BIRI DISTRICT HOSPITAL', 0, 1, 'C');
        $this->SetFont('Times', 'I', 11);
        $this->Cell(0, 6, 'Biri Northern Samar', 0, 1, 'C');
        
        // Professional report header
        $this->Ln(10);
        $this->SetFillColor(28, 58, 95); // Deep professional blue
        $this->SetTextColor(255);
        $this->SetFont('Times', 'B', 14);
        $pageWidth = $this->GetPageWidth();
        $textWidth = $this->GetStringWidth('MONTHLY ACCOMPLISHMENT REPORT');
        $x = ($pageWidth - $textWidth) / 2;
        $this->SetX($x);
        $this->Cell($textWidth, 10, 'MONTHLY ACCOMPLISHMENT REPORT', 0, 1, 'C', true);
        
        // Report metadata with improved formatting
        $this->SetTextColor(0);
        $this->SetFont('Times', '', 12);
        $this->Cell(0, 10, 'For the Month of ' . date('F Y', strtotime($_GET['year'].'-'.$_GET['month'].'-01')), 0, 1, 'C');
        $this->SetFont('Times', 'B', 12);
        $this->Cell(0, 10, 'Employee: ' . $this->employee_name, 0, 1, 'C');
        $this->Ln(5);
    }
    
    public $employee_name = '';
}

$pdf = new PDF('L');
$current_employee = '';

// Group results by employee
$grouped_results = [];
foreach($results as $row) {
    if (!isset($grouped_results[$row['jo_name']])) {
        $grouped_results[$row['jo_name']] = [];
    }
    $grouped_results[$row['jo_name']][] = $row;
}

// Create pages for each employee
foreach($grouped_results as $employee_name => $employee_tasks) {
    $pdf->employee_name = $employee_name;
    $pdf->AddPage();
    
    // Calculate table position for centering
    $tableWidth = 250; // Total width of the table
    $pageWidth = $pdf->GetPageWidth();
    $leftMargin = ($pageWidth - $tableWidth) / 2;
    $pdf->SetLeftMargin($leftMargin);
    
    // Professional table styling
    $pdf->SetFillColor(28, 58, 95); // Deep professional blue
    $pdf->SetTextColor(255);
    $pdf->SetFont('Times', 'B', 11);
    
    // Centered table headers
    $pdf->Cell(180, 8, 'Task Description', 1, 0, 'C', true);
    $pdf->Cell(70, 8, 'Times Accomplished', 1, 1, 'C', true);
    
    // Add data with subtle alternating colors
    $pdf->SetTextColor(0);
    $pdf->SetFont('Times', '', 10);
    $fill = false;
    foreach($employee_tasks as $row) {
        $pdf->SetFillColor(245, 246, 250); // Subtle alternating background
        
        // Handle wrapped text for task description
        $x = $pdf->GetX();
        $y = $pdf->GetY();
        $pdf->MultiCell(180, 8, $row['task_description'], 1, 'L', $fill);
        $new_y = $pdf->GetY();
        $pdf->SetXY($x + 180, $y);
        
        $pdf->Cell(70, $new_y - $y, $row['total_accomplished'], 1, 1, 'C', $fill);
        $fill = !$fill;
    }
    
    // Professional footer
    $pdf->Ln(10);
    $pdf->SetFont('Times', 'I', 10);
    $pdf->Cell(0, 6, 'Report Generated: ' . date('F d, Y'), 0, 1, 'R');
}

$pdf->Output();
?>
