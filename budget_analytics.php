<?php
require_once 'database.php';

// Get current year and month
$currentYear = isset($_GET['year']) ? $_GET['year'] : date('Y');
$currentMonth = isset($_GET['month']) ? $_GET['month'] : date('m');
$selectedDepartment = isset($_GET['department']) ? $_GET['department'] : '';
$selectedEmployee = isset($_GET['employee']) ? $_GET['employee'] : '';

// Fetch departments and employees for filters
$departments = $conn->query("SELECT id, department_name FROM departments ORDER BY department_name")->fetchAll();
$employees = $conn->query("SELECT id, emp_name FROM employees ORDER BY emp_name")->fetchAll();

// Build the base query
$query = "
    SELECT 
        t.transaction_id,
        t.transaction_ref,
        t.transaction_date,
        e.emp_name,
        d.department_name,
        COUNT(td.detail_id) as items_count,
        SUM(td.amount) as total_amount
    FROM transactions t
    JOIN employees e ON t.employee_id = e.id
    JOIN departments d ON e.department_id = d.id
    JOIN transaction_details td ON t.transaction_id = td.transaction_id
    WHERE YEAR(t.transaction_date) = :year
";

// Add filters based on user selection
$params = [':year' => $currentYear];

if ($currentMonth != 'all') {
    $query .= " AND MONTH(t.transaction_date) = :month";
    $params[':month'] = $currentMonth;
}

if ($selectedDepartment) {
    $query .= " AND d.id = :department";
    $params[':department'] = $selectedDepartment;
}

if ($selectedEmployee) {
    $query .= " AND e.id = :employee";
    $params[':employee'] = $selectedEmployee;
}

$query .= " GROUP BY t.transaction_id, t.transaction_ref, t.transaction_date, e.emp_name, d.department_name
           ORDER BY t.transaction_date DESC";

// Prepare and execute the query
$stmt = $conn->prepare($query);
$stmt->execute($params);
$transactionSummary = $stmt->fetchAll();

// Calculate statistics
$totalTransactions = count($transactionSummary);
$totalAmount = 0;
$departmentTotals = [];
$employeeTotals = [];

foreach ($transactionSummary as $trans) {
    $totalAmount += $trans['total_amount'];
    
    if (!isset($departmentTotals[$trans['department_name']])) {
        $departmentTotals[$trans['department_name']] = 0;
    }
    $departmentTotals[$trans['department_name']] += $trans['total_amount'];
    
    if (!isset($employeeTotals[$trans['emp_name']])) {
        $employeeTotals[$trans['emp_name']] = 0;
    }
    $employeeTotals[$trans['emp_name']] += $trans['total_amount'];
}

arsort($departmentTotals);
arsort($employeeTotals);

// Store transaction details in session
session_start();
$_SESSION['transaction_details'] = $transactionSummary;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Budget Analytics Dashboard - BIRI District Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
        }
        .dashboard-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
            margin-bottom: 1.5rem;
        }
        .metric-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
        }
        .metric-label {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }
        .filter-section {
            background: #fff;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-1">
                    <i class="fas fa-chart-line me-2 text-primary"></i>
                    Budget Analytics Dashboard
                </h2>
                <p class="text-muted mb-0">Financial overview and analysis</p>
            </div>
            <div>
                <a href="reports.php" class="btn btn-outline-primary px-4">
                    <i class="fas fa-arrow-left me-2"></i>Back to Reports
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="filter-section">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Year</label>
                    <select name="year" class="form-select" onchange="this.form.submit()">
                        <?php for($y = date('Y'); $y >= date('Y')-5; $y--): ?>
                            <option value="<?= $y ?>" <?= $y == $currentYear ? 'selected' : '' ?>><?= $y ?></option>
                        <?php endfor; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Month</label>
                    <select name="month" class="form-select" onchange="this.form.submit()">
                        <option value="all">All Months</option>
                        <?php for($m = 1; $m <= 12; $m++): ?>
                            <option value="<?= $m ?>" <?= $m == $currentMonth ? 'selected' : '' ?>>
                                <?= date('F', mktime(0,0,0,$m,1)) ?>
                            </option>
                        <?php endfor; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Department</label>
                    <select name="department" class="form-select" onchange="this.form.submit()">
                        <option value="">All Departments</option>
                        <?php foreach($departments as $dept): ?>
                            <option value="<?= $dept['id'] ?>" <?= $dept['id'] == $selectedDepartment ? 'selected' : '' ?>>
                                <?= htmlspecialchars($dept['department_name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Employee</label>
                    <select name="employee" class="form-select" onchange="this.form.submit()">
                        <option value="">All Employees</option>
                        <?php foreach($employees as $emp): ?>
                            <option value="<?= $emp['id'] ?>" <?= $emp['id'] == $selectedEmployee ? 'selected' : '' ?>>
                                <?= htmlspecialchars($emp['emp_name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </form>
        </div>

        <!-- Key Metrics -->
        <div class="row g-4 mb-5">
            <div class="col-md-3">
                <div class="dashboard-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="metric-label">Total Transactions</div>
                                <div class="metric-value"><?= number_format($totalTransactions) ?></div>
                            </div>
                            <div class="text-primary">
                                <i class="fas fa-file-invoice fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="dashboard-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="metric-label">Total Amount</div>
                                <div class="metric-value">₱<?= number_format($totalAmount, 2) ?></div>
                            </div>
                            <div class="text-success">
                                <i class="fas fa-money-bill-wave fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="dashboard-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="metric-label">Departments</div>
                                <div class="metric-value"><?= count($departmentTotals) ?></div>
                            </div>
                            <div class="text-info">
                                <i class="fas fa-building fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="dashboard-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="metric-label">Active Employees</div>
                                <div class="metric-value"><?= count($employeeTotals) ?></div>
                            </div>
                            <div class="text-warning">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Department Analysis -->
        <div class="dashboard-card mb-5">
            <div class="card-body">
                <h5 class="card-title mb-4">Department Spending Analysis</h5>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Department</th>
                                <th>Total Spending</th>
                                <th>% of Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($departmentTotals as $dept => $amount): ?>
                            <tr>
                                <td><?= htmlspecialchars($dept) ?></td>
                                <td>₱<?= number_format($amount, 2) ?></td>
                                <td><?= number_format(($amount / $totalAmount) * 100, 1) ?>%</td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Employee Analysis -->
        <div class="dashboard-card">
            <div class="card-body">
                <h5 class="card-title mb-4">Employee Transaction Summary</h5>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Employee</th>
                                <th>Total Transactions</th>
                                <th>Total Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($employeeTotals as $emp => $amount): ?>
                            <tr>
                                <td><?= htmlspecialchars($emp) ?></td>
                                <td>₱<?= number_format($amount, 2) ?></td>
                                <td><?= number_format(($amount / $totalAmount) * 100, 1) ?>%</td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
