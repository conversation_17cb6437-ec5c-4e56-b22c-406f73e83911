<?php
require_once '../database.php';

// Get date filters
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01');
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');

// Monitoring compliance report
$compliance_sql = "SELECT 
    mmc.category_name,
    COUNT(pmm.monitoring_id) as total_patients,
    COUNT(CASE WHEN pmm.monitoring_status = 'Active' THEN 1 END) as active_monitoring,
    COUNT(CASE WHEN pmm.monitoring_status = 'Overdue' THEN 1 END) as overdue_monitoring,
    COUNT(CASE WHEN pmm.last_monitoring_date IS NOT NULL THEN 1 END) as completed_monitoring,
    AVG(pmm.compliance_score) as avg_compliance_score
FROM patient_medication_monitoring pmm
JOIN medication_monitoring_rules mmr ON pmm.rule_id = mmr.rule_id
JOIN medication_monitoring_categories mmc ON mmr.category_id = mmc.category_id
WHERE pmm.created_at BETWEEN ? AND ?
GROUP BY mmc.category_id, mmc.category_name
ORDER BY total_patients DESC";

$compliance_stmt = $conn->prepare($compliance_sql);
$compliance_stmt->execute([$start_date, $end_date]);
$compliance_data = $compliance_stmt->fetchAll(PDO::FETCH_ASSOC);

// Alert statistics
$alert_stats_sql = "SELECT 
    alert_type,
    alert_priority,
    COUNT(*) as alert_count,
    COUNT(CASE WHEN is_acknowledged = 1 THEN 1 END) as acknowledged_count,
    COUNT(CASE WHEN is_resolved = 1 THEN 1 END) as resolved_count
FROM medication_monitoring_alerts
WHERE created_at BETWEEN ? AND ?
GROUP BY alert_type, alert_priority
ORDER BY alert_count DESC";

$alert_stats_stmt = $conn->prepare($alert_stats_sql);
$alert_stats_stmt->execute([$start_date, $end_date]);
$alert_stats = $alert_stats_stmt->fetchAll(PDO::FETCH_ASSOC);

// High-risk patients report
$high_risk_sql = "SELECT 
    p.patientname,
    p.patient_type,
    DATEDIFF(CURDATE(), p.birthdate) / 365.25 as age,
    COUNT(pmm.monitoring_id) as active_monitoring_count,
    COUNT(CASE WHEN pmm.priority_level = 'Critical' THEN 1 END) as critical_monitoring,
    COUNT(CASE WHEN pmm.monitoring_status = 'Overdue' THEN 1 END) as overdue_count,
    GROUP_CONCAT(DISTINCT mmc.category_name SEPARATOR ', ') as monitoring_categories
FROM patient p
JOIN patient_medication_monitoring pmm ON p.patientid = pmm.patientid
JOIN medication_monitoring_rules mmr ON pmm.rule_id = mmr.rule_id
JOIN medication_monitoring_categories mmc ON mmr.category_id = mmc.category_id
WHERE pmm.monitoring_status IN ('Active', 'Overdue')
GROUP BY p.patientid, p.patientname, p.patient_type, p.birthdate
HAVING active_monitoring_count >= 2 OR critical_monitoring > 0 OR overdue_count > 0
ORDER BY critical_monitoring DESC, overdue_count DESC, active_monitoring_count DESC
LIMIT 20";

$high_risk_patients = $conn->query($high_risk_sql)->fetchAll(PDO::FETCH_ASSOC);

// Beers Criteria report
$beers_sql = "SELECT 
    p.patientname,
    DATEDIFF(CURDATE(), p.birthdate) / 365.25 as age,
    i.generaldescription as medication_name,
    pmm.priority_level,
    pmm.monitoring_status,
    pmm.next_monitoring_date
FROM patient_medication_monitoring pmm
JOIN patient p ON pmm.patientid = p.patientid
JOIN items i ON pmm.itemid = i.itemid
JOIN medication_monitoring_rules mmr ON pmm.rule_id = mmr.rule_id
JOIN medication_monitoring_categories mmc ON mmr.category_id = mmc.category_id
WHERE mmc.beers_criteria = 1
AND pmm.monitoring_status = 'Active'
AND DATEDIFF(CURDATE(), p.birthdate) / 365.25 >= 65
ORDER BY age DESC, pmm.priority_level DESC";

$beers_patients = $conn->query($beers_sql)->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medication Monitoring Reports</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #ecf0f1;
            --white: #ffffff;
            --text-dark: #2c3e50;
            --text-muted: #7f8c8d;
            --border-color: #bdc3c7;
            --shadow-light: 0 2px 10px rgba(44, 62, 80, 0.1);
            --shadow-medium: 0 4px 20px rgba(44, 62, 80, 0.15);
        }

        body {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-dark);
        }

        .dashboard-card {
            border: none;
            border-radius: 12px;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
            background: var(--white);
            border-left: 4px solid var(--accent-color);
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: var(--shadow-light);
        }

        .table thead th {
            background: var(--primary-color);
            color: white;
            border: none;
            font-weight: 600;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 16px 12px;
        }

        .badge {
            font-weight: 600;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 0.8rem;
        }

        .nav-tabs .nav-link {
            border: none;
            border-radius: 8px 8px 0 0;
            color: var(--text-muted);
            font-weight: 500;
            padding: 12px 20px;
        }

        .nav-tabs .nav-link.active {
            background: var(--accent-color);
            color: white;
        }

        .tab-content {
            background: white;
            border-radius: 0 8px 8px 8px;
            padding: 20px;
            box-shadow: var(--shadow-light);
        }

        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .btn-primary {
            background: var(--accent-color);
            border-color: var(--accent-color);
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: #2980b9;
            border-color: #2980b9;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="dashboard-card card">
                    <div class="card-body py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-0">
                                    <i class="fas fa-chart-bar me-3 text-primary"></i>
                                    Medication Monitoring Reports
                                </h3>
                                <p class="text-muted mb-0 small">Comprehensive monitoring compliance and safety analytics</p>
                            </div>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="window.print()">
                                    <i class="fas fa-print me-1"></i>Print
                                </button>
                                <a href="medication_monitoring_dashboard.php" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-arrow-left me-1"></i>Back
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Date Filter -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="dashboard-card card">
                    <div class="card-body py-3">
                        <form method="GET" class="row g-3 align-items-end">
                            <div class="col-md-3">
                                <label class="form-label small">Start Date</label>
                                <input type="date" class="form-control form-control-sm" name="start_date" value="<?php echo $start_date; ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label small">End Date</label>
                                <input type="date" class="form-control form-control-sm" name="end_date" value="<?php echo $end_date; ?>">
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary btn-sm w-100">
                                    <i class="fas fa-search me-1"></i>Apply
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reports Tabs -->
        <div class="row">
            <div class="col-12">
                <ul class="nav nav-tabs mb-0" id="reportTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="compliance-tab" data-bs-toggle="tab" data-bs-target="#compliance" type="button" role="tab">
                            <i class="fas fa-chart-pie me-2"></i>Compliance Report
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="alerts-tab" data-bs-toggle="tab" data-bs-target="#alerts" type="button" role="tab">
                            <i class="fas fa-exclamation-triangle me-2"></i>Alert Statistics
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="high-risk-tab" data-bs-toggle="tab" data-bs-target="#high-risk" type="button" role="tab">
                            <i class="fas fa-user-shield me-2"></i>High-Risk Patients
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="beers-tab" data-bs-toggle="tab" data-bs-target="#beers" type="button" role="tab">
                            <i class="fas fa-user-clock me-2"></i>Beers Criteria
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="reportTabContent">
                    <!-- Compliance Report Tab -->
                    <div class="tab-pane fade show active" id="compliance" role="tabpanel">
                        <h5 class="mb-3">
                            <i class="fas fa-chart-line text-primary me-2"></i>Monitoring Compliance by Category
                        </h5>
                        
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th>Total Patients</th>
                                        <th>Active Monitoring</th>
                                        <th>Overdue</th>
                                        <th>Completed</th>
                                        <th>Compliance Score</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($compliance_data as $category): ?>
                                        <?php 
                                        $compliance_rate = $category['total_patients'] > 0 ? 
                                            (($category['completed_monitoring'] / $category['total_patients']) * 100) : 0;
                                        ?>
                                        <tr>
                                            <td><strong><?php echo htmlspecialchars($category['category_name']); ?></strong></td>
                                            <td><?php echo $category['total_patients']; ?></td>
                                            <td>
                                                <span class="badge bg-info"><?php echo $category['active_monitoring']; ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $category['overdue_monitoring'] > 0 ? 'danger' : 'success'; ?>">
                                                    <?php echo $category['overdue_monitoring']; ?>
                                                </span>
                                            </td>
                                            <td><?php echo $category['completed_monitoring']; ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $category['avg_compliance_score'] >= 80 ? 'success' : ($category['avg_compliance_score'] >= 60 ? 'warning' : 'danger'); ?>">
                                                    <?php echo number_format($category['avg_compliance_score'] ?? 0, 1); ?>%
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $compliance_rate >= 80 ? 'success' : ($compliance_rate >= 60 ? 'warning' : 'danger'); ?>">
                                                    <?php echo number_format($compliance_rate, 1); ?>%
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Alert Statistics Tab -->
                    <div class="tab-pane fade" id="alerts" role="tabpanel">
                        <h5 class="mb-3">
                            <i class="fas fa-bell text-warning me-2"></i>Alert Statistics
                        </h5>

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Alert Type</th>
                                        <th>Priority</th>
                                        <th>Total Alerts</th>
                                        <th>Acknowledged</th>
                                        <th>Resolved</th>
                                        <th>Response Rate</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($alert_stats as $alert): ?>
                                        <?php
                                        $response_rate = $alert['alert_count'] > 0 ?
                                            (($alert['acknowledged_count'] / $alert['alert_count']) * 100) : 0;
                                        ?>
                                        <tr>
                                            <td><?php echo str_replace('_', ' ', $alert['alert_type']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $alert['alert_priority'] === 'Critical' ? 'danger' : ($alert['alert_priority'] === 'High' ? 'warning' : 'info'); ?>">
                                                    <?php echo $alert['alert_priority']; ?>
                                                </span>
                                            </td>
                                            <td><?php echo $alert['alert_count']; ?></td>
                                            <td><?php echo $alert['acknowledged_count']; ?></td>
                                            <td><?php echo $alert['resolved_count']; ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $response_rate >= 80 ? 'success' : ($response_rate >= 60 ? 'warning' : 'danger'); ?>">
                                                    <?php echo number_format($response_rate, 1); ?>%
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- High-Risk Patients Tab -->
                    <div class="tab-pane fade" id="high-risk" role="tabpanel">
                        <h5 class="mb-3">
                            <i class="fas fa-user-shield text-danger me-2"></i>High-Risk Patients
                        </h5>

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Patient</th>
                                        <th>Age</th>
                                        <th>Type</th>
                                        <th>Active Monitoring</th>
                                        <th>Critical</th>
                                        <th>Overdue</th>
                                        <th>Categories</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($high_risk_patients as $patient): ?>
                                        <tr>
                                            <td><strong><?php echo htmlspecialchars($patient['patientname']); ?></strong></td>
                                            <td>
                                                <span class="badge bg-<?php echo $patient['age'] >= 75 ? 'danger' : ($patient['age'] >= 65 ? 'warning' : 'info'); ?>">
                                                    <?php echo number_format($patient['age'], 0); ?> years
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($patient['patient_type']); ?></td>
                                            <td>
                                                <span class="badge bg-info"><?php echo $patient['active_monitoring_count']; ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $patient['critical_monitoring'] > 0 ? 'danger' : 'success'; ?>">
                                                    <?php echo $patient['critical_monitoring']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $patient['overdue_count'] > 0 ? 'danger' : 'success'; ?>">
                                                    <?php echo $patient['overdue_count']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small><?php echo htmlspecialchars($patient['monitoring_categories']); ?></small>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Beers Criteria Tab -->
                    <div class="tab-pane fade" id="beers" role="tabpanel">
                        <h5 class="mb-3">
                            <i class="fas fa-user-clock text-warning me-2"></i>Beers Criteria Patients (65+ years)
                        </h5>

                        <div class="alert alert-warning">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Beers Criteria:</strong> Potentially inappropriate medications for older adults requiring enhanced monitoring.
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Patient</th>
                                        <th>Age</th>
                                        <th>Medication</th>
                                        <th>Priority</th>
                                        <th>Status</th>
                                        <th>Next Monitoring</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($beers_patients as $patient): ?>
                                        <tr>
                                            <td><strong><?php echo htmlspecialchars($patient['patientname']); ?></strong></td>
                                            <td>
                                                <span class="badge bg-<?php echo $patient['age'] >= 75 ? 'danger' : 'warning'; ?>">
                                                    <?php echo number_format($patient['age'], 0); ?> years
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($patient['medication_name']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $patient['priority_level'] === 'Critical' ? 'danger' : ($patient['priority_level'] === 'High' ? 'warning' : 'info'); ?>">
                                                    <?php echo $patient['priority_level']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $patient['monitoring_status'] === 'Overdue' ? 'danger' : 'info'; ?>">
                                                    <?php echo $patient['monitoring_status']; ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('M d, Y', strtotime($patient['next_monitoring_date'])); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
