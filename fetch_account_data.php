<?php

require_once 'database.php';
try {
    // Get current year    $currentYear = date('Y');
    // Query to get account data with allocations and utilizations
    $stmt = $conn->prepare("        SELECT 
            a.account_description,            a.account_amount,
            COALESCE(SUM(td.amount), 0) as total_utilized        FROM accounts a
        LEFT JOIN transaction_details td ON a.account_code = td.account_code        LEFT JOIN transactions t ON t.transaction_id = td.transaction_id
        WHERE a.account_year = ?        GROUP BY a.account_description, a.account_amount
        ORDER BY a.account_description    ");
    
    $stmt->execute([$currentYear]);    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
    // Return JSON response
    header('Content-Type: application/json');    echo json_encode($result);
} catch(PDOException $e) {    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode(['error' => $e->getMessage()]);
}
?>















