<?php
require_once 'database.php';

// Get current year and month
$currentYear = date('Y');
$currentMonth = date('m');

// Fetch audit logs (transactions with details)
$auditLogs = $conn->query("
    SELECT 
        t.transaction_id,
        t.transaction_ref,
        t.transaction_date,
        e.emp_name,
        d.department_name,
        td.transaction_description,
        a.account_code,
        a.account_description,
        td.amount
    FROM transactions t
    JOIN employees e ON t.employee_id = e.id
    JOIN departments d ON e.department_id = d.id
    JOIN transaction_details td ON t.transaction_id = td.transaction_id
    JOIN accounts a ON td.account_code = a.account_code
    ORDER BY t.transaction_date DESC, t.transaction_id DESC
")->fetchAll();

// Calculate summary statistics
$totalTransactions = count($auditLogs);
$totalAmount = array_sum(array_column($auditLogs, 'amount'));
$uniqueAccounts = count(array_unique(array_column($auditLogs, 'account_code')));
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audit Reports - BIRI District Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
        }
        .audit-metric {
            padding: 1.5rem;
            border-radius: 10px;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.03);
        }
        .metric-title {
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
        }
        .metric-value {
            font-size: 1.8rem;
            font-weight: 600;
            color: #2c3e50;
        }
        .table th {
            font-weight: 600;
            color: #6c757d;
        }
        .transaction-row:hover {
            background-color: #f8f9fa;
        }
        .badge-outline {
            background-color: transparent;
            border: 1px solid;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0">
                <i class="fas fa-clipboard-list me-2 text-primary"></i>
                System Audit Reports
            </h2>
            <a href="reports.php" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Reports
            </a>
        </div>

        <!-- Audit Overview -->
        <div class="row g-4 mb-4">
            <div class="col-md-4">
                <div class="audit-metric">
                    <h6 class="metric-title">Total Transactions</h6>
                    <div class="metric-value"><?php echo $totalTransactions; ?></div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="audit-metric">
                    <h6 class="metric-title">Total Amount</h6>
                    <div class="metric-value">₱<?php echo number_format($totalAmount, 2); ?></div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="audit-metric">
                    <h6 class="metric-title">Unique Accounts Used</h6>
                    <div class="metric-value"><?php echo $uniqueAccounts; ?></div>
                </div>
            </div>
        </div>

        <!-- Audit Logs -->
        <div class="card">
            <div class="card-body">
                <h4 class="card-title mb-4">Transaction Audit Logs</h4>
                <!-- Search Box -->
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text bg-light border-0">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" id="auditSearch" class="form-control bg-light border-0" 
                               placeholder="Search transactions..." onkeyup="searchAuditLogs()">
                    </div>
                </div>

                <script>
                function searchAuditLogs() {
                    let input = document.getElementById('auditSearch');
                    let filter = input.value.toLowerCase();
                    let table = document.querySelector('.table');
                    let tr = table.getElementsByTagName('tr');

                    for (let i = 1; i < tr.length; i++) {
                        let td = tr[i].getElementsByTagName('td');
                        let found = false;
                        
                        for (let j = 0; j < td.length; j++) {
                            let cell = td[j];
                            if (cell) {
                                let text = cell.textContent || cell.innerText;
                                if (text.toLowerCase().indexOf(filter) > -1) {
                                    found = true;
                                    break;
                                }
                            }
                        }
                        
                        tr[i].style.display = found ? '' : 'none';
                    }
                }
                </script>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Reference</th>
                                <th>Employee</th>
                                <th>Department</th>
                                <th>Account Code</th>
                                <th>Description</th>
                                <th>Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($auditLogs as $log): ?>
                                <tr class="transaction-row">
                                    <td><?php echo date('M d, Y', strtotime($log['transaction_date'])); ?></td>
                                    <td>
                                        <span class="badge badge-outline text-primary">
                                            <?php echo htmlspecialchars($log['transaction_ref']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($log['emp_name']); ?></td>
                                    <td><?php echo htmlspecialchars($log['department_name']); ?></td>
                                    <td>
                                        <span class="badge bg-light text-dark">
                                            <?php echo htmlspecialchars($log['account_code']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small><?php echo htmlspecialchars($log['transaction_description']); ?></small>
                                    </td>
                                    <td class="fw-semibold text-<?php echo $log['amount'] > 0 ? 'success' : 'danger'; ?>">
                                        ₱<?php echo number_format($log['amount'], 2); ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
