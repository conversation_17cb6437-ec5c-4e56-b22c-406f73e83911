<?php
require_once '../database.php';

// Create table if not exists
$sql = "CREATE TABLE IF NOT EXISTS certificate_of_appearance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    full_name VARCHAR(255) NOT NULL,
    date_from DATE NOT NULL,
    date_to DATE NOT NULL,
    purpose TEXT NOT NULL,
    date_issued DATE NOT NULL,
    issued_by VARCHAR(255) NOT NULL,
    position VARCHAR(255) NOT NULL,
    place_issued VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

try {
    $conn->exec($sql);
} catch(PDOException $e) {
    die("Error creating table: " . $e->getMessage());
}

// INSERT
if (isset($_POST['submit'])) {
    try {
        $sql = "INSERT INTO certificate_of_appearance (full_name, date_from, date_to, purpose, 
                date_issued, issued_by, position, place_issued) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $_POST['full_name'],
            $_POST['date_from'],
            $_POST['date_to'],
            $_POST['purpose'],
            $_POST['date_issued'],
            $_POST['issued_by'],
            $_POST['position'],
            $_POST['place_issued']
        ]);
        header("Location: " . $_SERVER['PHP_SELF']);
        exit();
    } catch(PDOException $e) {
        die("Error inserting record: " . $e->getMessage());
    }
}

// UPDATE
if (isset($_POST['update'])) {
    try {
        $sql = "UPDATE certificate_of_appearance SET 
                full_name = ?, 
                date_from = ?, 
                date_to = ?, 
                purpose = ?, 
                date_issued = ?, 
                issued_by = ?, 
                position = ?, 
                place_issued = ? 
                WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $_POST['full_name'],
            $_POST['date_from'],
            $_POST['date_to'],
            $_POST['purpose'],
            $_POST['date_issued'],
            $_POST['issued_by'],
            $_POST['position'],
            $_POST['place_issued'],
            $_POST['id']
        ]);
        header("Location: " . $_SERVER['PHP_SELF']);
        exit();
    } catch(PDOException $e) {
        die("Error updating record: " . $e->getMessage());
    }
}

// SEARCH
$search = isset($_GET['search']) ? $_GET['search'] : '';
$sql = "SELECT * FROM certificate_of_appearance WHERE 
        full_name LIKE ? OR 
        purpose LIKE ? OR 
        issued_by LIKE ? OR 
        position LIKE ? OR 
        place_issued LIKE ?
        ORDER BY created_at DESC";
$stmt = $conn->prepare($sql);
$searchTerm = "%$search%";
$stmt->execute([$searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm]);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate of Appearance</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 20px auto; }
    </style>
</head>
<body>
    <!-- Add/Edit Certificate Modal -->
    <div class="modal fade" id="certificateModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="modalTitle">Add New Certificate</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="id" id="id">
                        <div class="mb-3">
                            <label class="form-label">Full Name</label>
                            <input type="text" class="form-control" name="full_name" id="full_name">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Date From</label>
                            <input type="date" class="form-control" name="date_from" id="date_from">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Date To</label>
                            <input type="date" class="form-control" name="date_to" id="date_to">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Purpose</label>
                            <textarea class="form-control" name="purpose" id="purpose" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Date Issued</label>
                            <input type="date" class="form-control" name="date_issued" id="date_issued" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Issued By</label>
                            <select class="form-select" name="issued_by" id="issued_by" onchange="updatePosition()" required>
                                <option value="">Select Employee</option>
                                <option value="LUCILLE G. ROMINES, MD, FPCP">LUCILLE G. ROMINES, MD, FPCP</option>
                                <option value="CHRISTEL D. BRAVO">CHRISTEL D. BRAVO</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Position</label>
                            <input type="text" class="form-control" name="position" id="position" readonly required>
                        </div>
                        <script>
                            function updatePosition() {
                                const issuedBy = document.getElementById('issued_by').value;
                                const positionInput = document.getElementById('position');
                                
                                if (issuedBy === 'LUCILLE G. ROMINES, MD, FPCP') {
                                    positionInput.value = 'Chief of Hospital';
                                } else if (issuedBy === 'CHRISTEL D. BRAVO') {
                                    positionInput.value = 'Administrative Officer IV';
                                } else {
                                    positionInput.value = '';
                                }
                            }
                        </script>
                        <div class="mb-3">
                            <label class="form-label">Place Issued</label>
                            <input type="text" class="form-control" name="place_issued" id="place_issued" value="Biri, Northern Samar">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary" name="submit" id="submitBtn">Add Certificate</button>
                        <button type="submit" class="btn btn-warning" name="update" id="updateBtn" style="display: none;">Update Certificate</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="card shadow-lg rounded-4 border-0">
            <div class="card-header bg-gradient bg-primary bg-opacity-25 border-0 py-4">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="mb-0 text-primary">
                        <i class="fas fa-certificate me-2"></i>Certificate of Appearance
                    </h3>
                    <div>
                        <button type="button" class="btn btn-primary btn-lg rounded-3 shadow-sm" data-bs-toggle="modal" data-bs-target="#certificateModal">
                            <i class="fas fa-plus me-2"></i>New Certificate
                        </button>
                        <a href="../index.php" class="btn btn-outline-primary btn-lg rounded-3 shadow-sm ms-2">
                            <i class="fas fa-home me-2"></i>Homepage
                        </a>
                        <a href="empty_apperance.php" class="btn btn-outline-success btn-lg rounded-3 shadow-sm ms-2" target="_blank">
                            <i class="fas fa-file me-2"></i>Print Empty Form
                        </a>
                    </div>
                </div>
            </div>

            <div class="card-body p-4">
                <!-- Search Form -->
                <form method="GET" class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text border-0 bg-light">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control border-0 bg-light" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Search certificates...">
                        <button class="btn btn-primary" type="submit">Search</button>
                    </div>
                </form>

                <!-- Certificates Table -->
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="bg-light">
                            <tr>
                                <th>ID</th>
                                <th>Full Name</th>
                                <th>Date From</th>
                                <th>Date To</th>
                                <th>Purpose</th>
                                <th>Date Issued</th>
                                <th>Issued By</th>
                                <th>Position</th>
                                <th>Place Issued</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($results as $row): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($row['id']); ?></td>
                                <td><?php echo htmlspecialchars($row['full_name']); ?></td>
                                <td><?php echo date('M d, Y', strtotime($row['date_from'])); ?></td>
                                <td><?php echo date('M d, Y', strtotime($row['date_to'])); ?></td>
                                <td><?php echo htmlspecialchars($row['purpose']); ?></td>
                                <td><?php echo date('M d, Y', strtotime($row['date_issued'])); ?></td>
                                <td><?php echo htmlspecialchars($row['issued_by']); ?></td>
                                <td><?php echo htmlspecialchars($row['position']); ?></td>
                                <td><?php echo htmlspecialchars($row['place_issued']); ?></td>
                                <td>
                                    <button class="btn btn-warning btn-sm" onclick="editCertificate(<?php echo htmlspecialchars(json_encode($row)); ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <a href="generate_appearance.php?id=<?php echo $row['id']; ?>" class="btn btn-primary btn-sm" target="_blank">
                                        <i class="fas fa-file-pdf"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    const certificateModal = new bootstrap.Modal(document.getElementById('certificateModal'));

    function editCertificate(certificate) {
        document.getElementById('modalTitle').textContent = 'Edit Certificate';
        document.getElementById('id').value = certificate.id;
        document.getElementById('full_name').value = certificate.full_name;
        document.getElementById('date_from').value = certificate.date_from;
        document.getElementById('date_to').value = certificate.date_to;
        document.getElementById('purpose').value = certificate.purpose;
        document.getElementById('date_issued').value = certificate.date_issued;
        document.getElementById('issued_by').value = certificate.issued_by;
        document.getElementById('position').value = certificate.position;
        document.getElementById('place_issued').value = certificate.place_issued;
        document.getElementById('submitBtn').style.display = 'none';
        document.getElementById('updateBtn').style.display = 'block';
        certificateModal.show();
    }

    // Reset modal when opening for new certificate
    document.querySelector('[data-bs-target="#certificateModal"]').addEventListener('click', () => {
        document.getElementById('modalTitle').textContent = 'Add New Certificate';
        document.getElementById('id').value = '';
        document.getElementById('submitBtn').style.display = 'block';
        document.getElementById('updateBtn').style.display = 'none';
        document.querySelector('#certificateModal form').reset();
    });
    </script>
</body>
</html>
