<?php
require_once '../database.php';
session_start();
$department = $_SESSION['department'];
$usertype = $_SESSION['type'];
// Get employee name from database
$stmt = $conn->prepare("SELECT e.emp_name FROM employees e 
                       INNER JOIN users u ON u.emp_id = e.id 
                       WHERE u.user_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$employee = $stmt->fetch();
$_SESSION['emp_name'] = $employee['emp_name'] ?? 'Unknown Employee';
// Check if user is not logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// SEARCH
    $search = isset($_GET['search']) ? $_GET['search'] : '';
    $sql = "SELECT 
                patient.patientid, 
                patient.patientname, 
                patient.patient_type, 
                patient.id_number, 
                patient.birthdate,
                pharmatransactions.transaction_date 
            FROM patient 
            INNER JOIN pharmatransactions 
                ON patient.patientid = pharmatransactions.patientid 
            WHERE DATE(pharmatransactions.transaction_date) = CURDATE()
            AND (
                patient.patientname LIKE ? OR 
                patient.patient_type LIKE ? OR
                patient.id_number LIKE ?
            )
            ORDER BY pharmatransactions.transaction_date DESC";

    $stmt = $conn->prepare($sql);
    $searchTerm = "%$search%";
    $stmt->execute([$searchTerm, $searchTerm, $searchTerm]);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);


    // Get today's total cash sales
    $todaySalesSql = "SELECT COALESCE(SUM(amount_paid), 0) as total 
                      FROM payment 
                      WHERE DATE(payment_date) = CURDATE() 
                      AND payment_type = 'Cash'";
    $todaySalesStmt = $conn->prepare($todaySalesSql);
    $todaySalesStmt->execute();
    $todaySales = $todaySalesStmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Get yesterday's total cash sales
    $yesterdaySalesSql = "SELECT COALESCE(SUM(amount_paid), 0) as total 
                         FROM payment 
                         WHERE DATE(payment_date) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
                         AND payment_type = 'Cash'";
    $yesterdaySalesStmt = $conn->prepare($yesterdaySalesSql);
    $yesterdaySalesStmt->execute();
    $yesterdaySales = $yesterdaySalesStmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Calculate percentage change
    $percentageChange = 0;
    if ($yesterdaySales > 0) {
        $percentageChange = (($todaySales - $yesterdaySales) / $yesterdaySales) * 100;
    }


    // Get daily sales for the current week
    $weeklySalesSql = "SELECT 
        DATE(payment_date) as sale_date,
        COALESCE(SUM(amount_paid), 0) as total 
        FROM payment 
        WHERE WEEK(payment_date, 1) = WEEK(CURDATE(), 1)
        AND YEAR(payment_date) = YEAR(CURDATE())
        AND payment_type = 'Cash'
        GROUP BY DATE(payment_date)
        ORDER BY sale_date ASC";
    $weeklySalesStmt = $conn->prepare($weeklySalesSql);
    $weeklySalesStmt->execute();
    $weeklySales = $weeklySalesStmt->fetchAll(PDO::FETCH_ASSOC);

    // Initialize array with zeros for all days
    $dailySalesData = array_fill(0, 7, 0);
    
    // Fill in actual sales data
    foreach ($weeklySales as $sale) {
        // Get numeric day of week (1=Monday, 7=Sunday) from sale_date
        $dayOfWeek = date('N', strtotime($sale['sale_date'])) - 1;
        if ($dayOfWeek >= 0 && $dayOfWeek < 7) {
            $dailySalesData[$dayOfWeek] = floatval($sale['total']);
        }
    }

    // Get total count of patients
    $transactionCountSql = "SELECT COUNT(*) as total FROM patient";
    $transactionStmt = $conn->prepare($transactionCountSql);
    $transactionStmt->execute();
    $transactionCount = $transactionStmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Get count of active patients (with transactions in last 3 months)
    $activePatientsSql = "SELECT COUNT(DISTINCT p.patientid) as active_count 
                         FROM patient p 
                         INNER JOIN pharmatransactions pt ON p.patientid = pt.patientid 
                         WHERE pt.transaction_date >= DATE_SUB(NOW(), INTERVAL 3 MONTH)";
    $activePatientsStmt = $conn->prepare($activePatientsSql);
    $activePatientsStmt->execute();
    $activePatients = $activePatientsStmt->fetch(PDO::FETCH_ASSOC)['active_count'];


    // Get today's total credit sales
    $todayCreditSql = "SELECT COALESCE(SUM(amount_paid), 0) as total 
                      FROM payment 
                      WHERE payment_type = 'Credit'";
    $todayCreditStmt = $conn->prepare($todayCreditSql);
    $todayCreditStmt->execute();
    $todayCredit = $todayCreditStmt->fetch(PDO::FETCH_ASSOC)['total'];

// Get all items
$itemsQuery = "SELECT i.*, psl.*, c.categorydesc 
               FROM items i 
               JOIN pharmacategory c ON i.category = c.categoryid
               JOIN pharmacy_stock_ledger psl ON i.itemid=psl.itemid 
               ORDER BY i.generaldescription";
$items = $conn->query($itemsQuery)->fetchAll();

// Get all credit transactions first
$creditTransSql = "SELECT pt.transaction_reference, p.employee_name, p.amount_paid as amount, pt.transaction_date 
                   FROM pharmatransactions pt 
                   JOIN payment p ON pt.transaction_id = p.transaction_id
                   WHERE p.payment_type = 'Credit'
                   AND (pt.transaction_reference LIKE :search OR p.employee_name LIKE :search)
                   ORDER BY pt.transaction_date DESC";
$creditTransStmt = $conn->prepare($creditTransSql);
$searchTerm = isset($_GET['search']) ? '%' . $_GET['search'] . '%' : '%%';
$creditTransStmt->bindParam(':search', $searchTerm);
$creditTransStmt->execute();
$creditTransactions = $creditTransStmt->fetchAll(PDO::FETCH_ASSOC);


// Get all categories
$categoriesQuery = "SELECT * FROM pharmacategory ORDER BY categorydesc";
$categoriesStmt = $conn->prepare($categoriesQuery);
$categoriesStmt->execute();
$categories = $categoriesStmt->fetchAll(PDO::FETCH_ASSOC);


// Load all unpaid transactions
$unpaidTransactions = [];
if (!isset($_GET['ref'])) {
    $sql = "SELECT pt.transaction_reference, pt.transaction_id, p.patientname, p.patient_type,
            pt.ward, pt.transaction_date, 
            (SELECT SUM(subtotal) FROM pharmatransaction_details WHERE transaction_id = pt.transaction_id) as total_amount
            FROM pharmatransactions pt
            LEFT JOIN payment pay ON pt.transaction_id = pay.transaction_id AND (pay.payment_type = 'Cash' OR pay.payment_type = 'Philhealth')
            JOIN patient p ON pt.patientid = p.patientid
            WHERE pay.payment_id IS NULL
            OR (pay.payment_id IS NOT NULL AND pay.payment_type = 'Credit' AND NOT EXISTS (
                SELECT 1 FROM payment p2 
                WHERE p2.transaction_id = pt.transaction_id 
                AND (p2.payment_type = 'Cash' OR p2.payment_type = 'Philhealth')
            ))
            ORDER BY pt.transaction_date DESC";
    
    $stmt = $conn->query($sql);
    $unpaidTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($unpaidTransactions as &$trans) {
        $sql = "SELECT
            ptd.transaction_id,
            ptd.itemid,
            ptd.quantity,
            ptd.unit_price,
            ptd.subtotal,
            ptd.original_price,
            ptd.discount_applied,
            i.generaldescription,
            i.unitmeasure,
            (SELECT psl.selling_price FROM pharmacy_stock_ledger psl
             WHERE psl.itemid = ptd.itemid
             ORDER BY psl.expiry_date ASC, psl.date_received ASC
             LIMIT 1) as selling_price,
            (SELECT psl.lot_no FROM pharmacy_stock_ledger psl
             WHERE psl.itemid = ptd.itemid
             ORDER BY psl.expiry_date ASC, psl.date_received ASC
             LIMIT 1) as lot_no,
            (SELECT psl.expiry_date FROM pharmacy_stock_ledger psl
             WHERE psl.itemid = ptd.itemid
             ORDER BY psl.expiry_date ASC, psl.date_received ASC
             LIMIT 1) as expiry_date
        FROM pharmatransaction_details ptd
        INNER JOIN items i ON ptd.itemid = i.itemid
        WHERE ptd.transaction_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$trans['transaction_id']]);
        $trans['items'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    unset($trans); // 🔥 This is crucial
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pharmacy POS Dashboard - BIRI District Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1d4ed8;
            --success-color: #16a34a;
            --warning-color: #ca8a04;
            --danger-color: #dc2626;
            --background-color: #f1f5f9;
            --card-bg: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            color: var(--text-primary);
        }

        .dashboard-wrapper {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            background: var(--card-bg);
            padding: 1rem;
            border-right: 1px solid #e2e8f0;
            width: 280px;
            min-width: 280px;
            max-width: 280px;
            overflow-y: auto;
            position: fixed;
            height: 100vh;
            z-index: 1000;
        }

        .main-content {
            flex: 1;
            padding: 1.5rem;
            margin-left: 280px;
            width: calc(100% - 280px);
        }

        /* Responsive design */
        @media (max-width: 1200px) {
            .sidebar {
                width: 260px;
                min-width: 260px;
                max-width: 260px;
            }
            .main-content {
                margin-left: 260px;
                width: calc(100% - 260px);
            }
        }

        @media (max-width: 992px) {
            .sidebar {
                width: 240px;
                min-width: 240px;
                max-width: 240px;
                padding: 0.75rem;
            }
            .main-content {
                margin-left: 240px;
                width: calc(100% - 240px);
                padding: 1rem;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            .sidebar.show {
                transform: translateX(0);
            }
            .main-content {
                margin-left: 0;
                width: 100%;
            }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .stat-card {
            background: var(--card-bg);
            border-radius: 8px;
            padding: 1.25rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        /* Navigation improvements */
        .nav-group {
            margin-bottom: 1rem !important;
        }

        .nav-group-title {
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 0.5rem !important;
            padding-left: 0.5rem;
        }

        .nav-link {
            padding: 0.5rem 0.75rem !important;
            margin-bottom: 0.25rem;
            font-size: 0.875rem;
            border-radius: 6px !important;
            transition: all 0.2s ease;
        }

        .nav-link:hover {
            background-color: #f1f5f9;
            color: var(--primary-color);
        }

        .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }

        .nav-link i {
            width: 16px;
            font-size: 0.875rem;
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem;
            background: var(--primary-color);
            color: white;
            border-radius: 8px;
            border: none;
            transition: background-color 0.2s ease;
        }

        .action-btn:hover {
            background: var(--secondary-color);
        }

        .recent-transactions {
            background: var(--card-bg);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .chart-container {
            background: var(--card-bg);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .search-bar {
            background: var(--card-bg);
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 2rem;
            display: flex;
            gap: 1rem;
        }

        .search-input {
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 0.5rem 1rem;
            flex-grow: 1;
        }

        /* Compact brand and user sections */
        .brand-section {
            margin-bottom: 1rem !important;
        }

        .logo-container {
            padding: 0.75rem !important;
        }

        .logo-container h4 {
            font-size: 1rem !important;
            margin-bottom: 0 !important;
        }

        .logo-container small {
            font-size: 0.7rem;
        }

        .user-welcome {
            padding: 0.75rem !important;
            margin-bottom: 1rem !important;
        }

        .user-welcome h5 {
            font-size: 0.9rem !important;
            margin-bottom: 0.25rem !important;
        }

        .user-welcome p {
            font-size: 0.75rem;
            margin-bottom: 0.25rem !important;
        }

        .badge {
            font-size: 0.7rem;
            padding: 0.25rem 0.5rem;
        }

        /* Time container optimization */
        .time-container {
            margin: 1rem auto !important;
            padding: 0.75rem !important;
            width: 200px !important;
        }

        .digital-clock {
            font-size: 1.2em !important;
            margin-bottom: 0.5rem !important;
        }

        /* Mobile toggle button */
        .mobile-toggle {
            display: none;
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 0.5rem;
            font-size: 1.2rem;
        }

        @media (max-width: 768px) {
            .mobile-toggle {
                display: block;
            }
        }
    </style>
</head>
<body>
    <!-- Mobile toggle button -->
    <button class="mobile-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <div class="dashboard-wrapper">
        <aside class="sidebar" id="sidebar">
            <div class="brand-section mb-4">
                <div class="logo-container d-flex align-items-center bg-white p-3 rounded-3 shadow-sm">
                    <img src="../images/bdh.png" alt="Pharmacy Logo" class="me-3" width="40" height="40">
                    <div>
                        <h4 class="mb-0 fw-bold text-primary">Pharmacy POS</h4>
                        <small class="text-muted">Management System</small>
                    </div>
                </div>
            </div>
            
            <div class="user-welcome bg-light p-3 rounded-3 mb-4">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <p class="mb-1 fw-medium text-primary">Welcome back,</p>
                        <h5 class="mb-1"><?php echo $_SESSION['emp_name'] = $employee['emp_name'] ?? 'Unknown Employee'; ?></h5>
                        <span class="badge bg-primary"><?php echo $department; ?></span>
                        <a href="../login.php" class="badge bg-danger ms-2 text-decoration-none">Log-out</a>
                    </div>
                </div>
            </div>
            <nav class="nav flex-column gap-2">
                <a href="../index.php" class="nav-link active rounded-3 d-flex align-items-center">
                    <i class="fas fa-tachometer-alt me-3"></i>
                    <span class="fw-medium">Dashboard</span>
                </a>
                <!-- Patient Management -->
                <div class="nav-group mb-3">
                    <h6 class="nav-group-title text-muted mb-2">Patient Management</h6>
                    <a href="patient.php" class="nav-link rounded-3 d-flex align-items-center">
                        <i class="fas fa-user-injured me-3"></i>
                        <span class="fw-medium">Patient</span>
                    </a>
                    <a href="register_doctor.php" class="nav-link rounded-3 d-flex align-items-center">
                        <i class="fas fa-user-md me-3"></i>
                        <span class="fw-medium">Register Doctor</span>
                    </a>
                </div>

                <!-- Inventory Management -->
                <div class="nav-group mb-3">
                    <h6 class="nav-group-title text-muted mb-2">Inventory Management</h6>
                    <a href="daily_utilizatiion.php" class="nav-link rounded-3 d-flex align-items-center">
                        <i class="fas fa-pills me-3"></i>
                        <span class="fw-medium">Inventory</span>
                    </a>
                    <a href="items.php" class="nav-link rounded-3 d-flex align-items-center">
                        <i class="fas fa-pills me-3"></i>
                        <span class="fw-medium">Items</span>
                    </a>
                    <a href="stock_ledger.php" class="nav-link rounded-3 d-flex align-items-center">
                        <i class="fas fa-book me-3"></i>
                        <span class="fw-medium">Stock Ledger</span>
                    </a>
                    <a href="movement.php" class="nav-link rounded-3 d-flex align-items-center">
                        <i class="fas fa-exchange-alt me-3"></i>
                        <span class="fw-medium">Supplies Movement</span>
                    </a>
                    <a href="mur.php" class="nav-link rounded-3 d-flex align-items-center">
                        <i class="fas fa-file-medical me-3"></i>
                        <span class="fw-medium">Monthly Utilization Report</span>
                    </a>
                    <a href="returnbatch.php" class="nav-link rounded-3 d-flex align-items-center">
                        <i class="fas fa-undo-alt me-3"></i>
                        <span class="fw-medium">Returned Items</span>
                    </a>
                </div>

                <!-- Medication Safety -->
                <div class="nav-group mb-3">
                    <h6 class="nav-group-title text-muted mb-2">Medication Safety</h6>
                    <a href="medication_monitoring_dashboard.php" class="nav-link rounded-3 d-flex align-items-center">
                        <i class="fas fa-heartbeat me-3"></i>
                        <span class="fw-medium">Monitoring Dashboard</span>
                    </a>
                    <a href="medication_monitoring.php" class="nav-link rounded-3 d-flex align-items-center">
                        <i class="fas fa-cogs me-3"></i>
                        <span class="fw-medium">Setup Monitoring</span>
                    </a>
                </div>

                <!-- Financial Management -->
                <div class="nav-group mb-3">
                    <h6 class="nav-group-title text-muted mb-2">Financial Management</h6>
                     <a href="admin_financial_dashboard.php" class="nav-link rounded-3 d-flex align-items-center">
                        <i class="fas fa-chart-pie me-3"></i>
                        <span class="fw-medium">Financial Analytics</span>
                    </a>
                    <a href="payment.php" class="nav-link rounded-3 d-flex align-items-center">
                        <i class="fas fa-money-bill me-3"></i>
                        <span class="fw-medium">Payment</span>
                    </a>
                    <a href="#" class="nav-link rounded-3 d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#unpaidTransactionsModal">
                        <i class="fas fa-clock me-3"></i>
                        <span class="fw-medium">Unpaid Transactions</span>
                    </a>
                    <a href="#" class="nav-link rounded-3 d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#priceCheckModal">
                        <i class="fas fa-tags me-3"></i>
                        <span class="fw-medium">Price Check</span>
                    </a>
                    <a href="reports.php" class="nav-link rounded-3 d-flex align-items-center">
                        <i class="fas fa-chart-bar me-3"></i>
                        <span class="fw-medium">Reports</span>
                    </a>
                </div>
            </nav>

            <!-- Digital Clock and Calendar -->
            <div class="time-container">
                <div id="digital-clock" class="digital-clock"></div>
            </div>

            <style>
                .time-container {
                    text-align: center;
                    margin: 15px auto;
                    padding: 10px;
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 0 10px rgba(0,0,0,0.1);
                    width: 220px;
                }
                .digital-clock {
                    font-size: 1.5em;
                    font-weight: bold;
                    color: #2563eb;
                    margin-bottom: 10px;
                }
                .calendar {
                    font-size: 0.8em;
                    color: #333;
                }
                .calendar-grid {
                    display: grid;
                    grid-template-columns: repeat(7, 1fr);
                    gap: 3px;
                    margin-top: 8px;
                }
                .calendar-header {
                    font-weight: bold;
                    text-align: center;
                    margin-bottom: 8px;
                }
                .calendar-day {
                    padding: 3px;
                    text-align: center;
                    border-radius: 4px;
                }
                .calendar-day.today {
                    background-color: #2563eb;
                    color: white;
                }
                .calendar-day.other-month {
                    color: #999;
                }
            </style>

            <script>
                function updateTime() {
                    const now = new Date();
                    const timeString = now.toLocaleTimeString('en-US', { 
                        hour12: true,
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                    });
                    document.getElementById('digital-clock').textContent = timeString;

                    // Update calendar only if it hasn't been created yet
                    if (!document.querySelector('.calendar-grid')) {
                        updateCalendar(now);
                    }
                }

                function updateCalendar(date) {
                    const calendarDiv = document.querySelector('.calendar');
                    const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
                    const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
                    
                    // Create calendar header
                    const monthYear = date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
                    let calendarHTML = `<div class="calendar-header">${monthYear}</div>`;
                    
                    // Create weekday headers
                    calendarHTML += '<div class="calendar-grid">';
                    const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
                    weekdays.forEach(day => {
                        calendarHTML += `<div class="calendar-day">${day}</div>`;
                    });

                    // Fill in the days
                    let currentDay = new Date(firstDay);
                    currentDay.setDate(currentDay.getDate() - firstDay.getDay()); // Start from last month if necessary

                    while (currentDay <= lastDay || currentDay.getDay() !== 0) {
                        const isToday = currentDay.toDateString() === date.toDateString();
                        const isOtherMonth = currentDay.getMonth() !== date.getMonth();
                        
                        calendarHTML += `<div class="calendar-day${isToday ? ' today' : ''}${isOtherMonth ? ' other-month' : ''}">${currentDay.getDate()}</div>`;
                        
                        currentDay.setDate(currentDay.getDate() + 1);
                    }
                    
                    calendarHTML += '</div>';
                    calendarDiv.innerHTML = calendarHTML;
                }

                setInterval(updateTime, 1000);
                updateTime(); // initial run
            </script>
        </aside>

        <main class="main-content">
            <div class="stats-grid">
                <div class="stat-card">
                    <h3 class="h6 mb-1">Today's Sales</h3>
                    <h2 class="h5">₱<?php echo number_format($todaySales, 2); ?> <small class="text-<?php echo $percentageChange > 0 ? 'success' : 'danger'; ?>"><i class="fas fa-<?php echo $percentageChange > 0 ? 'arrow-up' : 'arrow-down'; ?>"></i> <?php echo number_format(abs($percentageChange), 2); ?>%</small></h2>
                </div>
                <div class="stat-card" style="cursor: pointer;" data-bs-toggle="modal" data-bs-target="#creditTransactionsModal">
                    <h3 class="h6 mb-1">Accounts Receivable (Employee)</h3>
                    <h2 class="h5">₱<?php echo number_format($todayCredit, 2); ?> <small class="text-warning">View Details</small></h2>
                </div>

                <!-- Credit Transactions Modal -->
                <div class="modal fade" id="creditTransactionsModal" tabindex="-1" aria-labelledby="creditTransactionsModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="creditTransactionsModalLabel">Credit Transactions</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <input type="text" id="creditSearchInput" class="form-control" value="<?php echo htmlspecialchars(isset($_GET['search']) ? $_GET['search'] : ''); ?>" placeholder="Search by Transaction Ref...">
                                </div>
                                <div class="table-responsive">
                                    <table class="table" id="creditTransactionsTable">
                                        <thead>
                                            <tr>
                                                <th>Transaction Ref</th>
                                                <th>Employee Name</th>
                                                <th>Amount</th>
                                                <th>Date</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach($creditTransactions as $transaction): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($transaction['transaction_reference']); ?></td>
                                                <td><?php echo htmlspecialchars($transaction['employee_name']); ?></td>
                                                <td>₱<?php echo number_format($transaction['amount'], 2); ?></td>
                                                <td><?php echo date('M d, Y', strtotime($transaction['transaction_date'])); ?></td>
                                                <td><span class="badge bg-warning">Pending</span></td>
                                                <td>
                                                    <a href="payment.php?ref=<?php echo htmlspecialchars($transaction['transaction_reference']); ?>" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-check-circle me-2"></i>Process Payment
                                                    </a>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <script>
                    document.getElementById('creditSearchInput').addEventListener('input', function() {
                        const searchTerm = this.value.trim();
                        fetch(`get_credit_transactions.php?search=${searchTerm}`)
                            .then(response => response.json())
                            .then(data => {
                                const tbody = document.querySelector('#creditTransactionsTable tbody');
                                tbody.innerHTML = data.map(transaction => `
                                    <tr>
                                        <td>${transaction.transaction_id}</td>
                                        <td>${transaction.employee_name}</td>
                                        <td>₱${parseFloat(transaction.amount).toFixed(2)}</td>
                                        <td>${transaction.date}</td>
                                        <td><span class="badge bg-warning">Pending</span></td>
                                    </tr>
                                `).join('');
                            })
                            .catch(error => console.error('Error:', error));
                    });
                </script>
                <div class="stat-card">
                    <h3 class="h6 mb-1">Patients</h3>
                    <h2 class="h5"><?php echo $transactionCount; ?> <small class="text-primary">Active: <?php echo $activePatients; ?></small></h2>
                </div>
            </div>
            <div class="chart-container" style="margin-left: 50px;">
                <h5 class="mb-4">Sales Overview</h5>
                <canvas id="salesChart" height="30"></canvas>
            </div>

            <div class="recent-transactions" style="margin-left: 50px;">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="mb-0">Recent Transactions</h5>
                    <a href="transactions.php" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <form class="search-form mb-3" action="payment.php" method="GET" onsubmit="return validateForm()">
                    <div class="input-group">
                        <input type="text" name="ref" class="form-control" 
                               placeholder="Enter Transaction Reference"
                               value="<?php echo isset($_GET['ref']) ? $_GET['ref'] : ''; ?>" 
                               required>
                        <button type="submit" class="btn btn-primary">Search</button>
                    </div>
                </form>

                <script>
                    function validateForm() {
                        const ref = document.querySelector('input[name="ref"]').value;
                        if (!ref.trim()) {
                            return false;
                        }
                        return true;
                    }
                </script>
                <!-- Patients Table -->
                <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                    <table class="table table-hover align-middle">
                        <thead class="bg-light sticky-top" style="position: sticky; top: 0; background: white; z-index: 1;">
                            <tr>
                                <th><i class="fas fa-hashtag me-2"></i>ID</th>
                                <th><i class="fas fa-user me-2"></i>Patient Name</th>
                                <th><i class="fas fa-id-card me-2"></i>Patient Type</th>
                                <th><i class="fas fa-birthday-cake me-2"></i>Age</th>
                                <th><i class="fas fa-info-circle me-2"></i>ID Number</th>
                                <th><i class="fas fa-cog me-2"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($results as $row): ?>
                            <?php 
                                $age = '';
                                if (!empty($row['birthdate'])) {
                                    $birthDate = new DateTime($row['birthdate']);
                                    $today = new DateTime();
                                    $age = $birthDate->diff($today)->y;
                                }
                            ?>
                            <tr>
                                <td class="fw-bold text-primary"><?php echo htmlspecialchars($row['patientid']); ?></td>
                                <td><?php echo htmlspecialchars($row['patientname']); ?></td>
                                <td>
                                    <span class="badge bg-<?php echo $row['patient_type'] == 'Regular' ? 'secondary' : 'primary'; ?>">
                                        <?php echo htmlspecialchars($row['patient_type']); ?>
                                    </span>
                                </td>
                                <td><?php echo $age ? $age . ' years' : '-'; ?></td>
                                <td><?php echo htmlspecialchars($row['id_number'] ?: '-'); ?></td>
                                <td>
                                    <a href="transactions.php?id=<?php echo $row['patientid']; ?>" 
                                       class="btn btn-primary btn-sm" 
                                       data-bs-toggle="tooltip" 
                                       title="Go to Transactions">
                                        <i class="fas fa-arrow-right"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>
    <!-- Price Check Modal -->
    <div class="modal fade" id="priceCheckModal" tabindex="-1" aria-labelledby="priceCheckModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content border-0 shadow-lg rounded-3" style="background-color: #ffffff;">
                <div class="modal-header border-0 py-3" style="background: linear-gradient(135deg, #3b82f6, #2563eb);">
                    <h5 class="modal-title text-white" id="priceCheckModalLabel">
                        <i class="fas fa-tag me-2"></i>Price Checker
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="search-container mb-4">
                        <div class="row g-3">
                            <div class="col-md-8">
                                <div class="input-group shadow-sm rounded-3">
                                    <span class="input-group-text border-0 px-3" style="background-color: #3b82f6;">
                                        <i class="fas fa-search text-white"></i>
                                    </span>
                                    <input type="text" class="form-control border-0" id="priceSearchInput" 
                                           placeholder="Search medicine..." autocomplete="off">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <select class="form-select shadow-sm border-0" id="categoryFilter">
                                    <option value="">All Categories</option>
                                    <?php foreach($categories as $category): ?>
                                        <option value="<?php echo htmlspecialchars($category['categorydesc']); ?>">
                                            <?php echo htmlspecialchars($category['categorydesc']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="table-container rounded-3 shadow-sm border">
                        <div class="table-responsive" style="max-height: 50vh;">
                            <table class="table table-hover mb-0">
                                <thead class="sticky-top">
                                    <tr style="background-color: #f8fafc;">
                                        <th class="px-4 py-3">Medicine/Item</th>
                                        <th class="px-4 py-3">Category</th>
                                        <th class="px-4 py-3">Unit</th>
                                        <th class="px-4 py-3 text-end">Price</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($items as $item): ?>
                                    <tr class="price-item">
                                        <td class="px-4 py-3"><?php echo htmlspecialchars($item['generaldescription']); ?></td>
                                        <td class="px-4 py-3"><span class="badge bg-primary"><?php echo htmlspecialchars($item['categorydesc']); ?></span></td>
                                        <td class="px-4 py-3"><?php echo htmlspecialchars($item['unitmeasure']); ?></td>
                                        <td class="px-4 py-3 text-end fw-bold">₱<?php echo number_format($item['selling_price'], 2); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 pt-2 pb-4 px-4">
                    <button type="button" class="btn btn-primary px-4" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <style>
        .bg-gradient-primary {
            background: linear-gradient(45deg, #2563eb, #3b82f6);
        }
        .price-item:hover {
            background-color: #f8fafc;
            transform: scale(1.002);
            transition: all 0.2s ease;
        }
        .search-container .form-control:focus,
        .search-container .form-select:focus {
            box-shadow: 0 0 0 0.25rem rgba(37, 99, 235, 0.25);
        }
        #priceCheckModal .table-container {
            background: white;
            border-radius: 0.5rem;
        }
        #priceCheckModal .table th {
            font-weight: 600;
            color: #1e293b;
        }
    </style>
    <!-- Unpaid Transactions Modal -->
    <div class="modal fade" id="unpaidTransactionsModal" tabindex="-1" aria-labelledby="unpaidTransactionsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #3b82f6, #2563eb);">
                    <h5 class="modal-title text-white" id="unpaidTransactionsModalLabel">
                        <i class="fas fa-clock me-2"></i>Unpaid Transactions
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="unpaidTransactionsTable">
                            <thead>
                                <tr>
                                    <th>Reference</th>
                                    <th>Patient Name</th>
                                    <th>Patient Type</th>
                                    <th>Ward</th>
                                    <th>Date</th>
                                    <th>Total Amount</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($unpaidTransactions as $trans): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($trans['transaction_reference']); ?></td>
                                    <td><?php echo htmlspecialchars($trans['patientname']); ?></td>
                                    <td><span class="badge bg-info"><?php echo htmlspecialchars($trans['patient_type']); ?></span></td>
                                    <td><?php echo htmlspecialchars($trans['ward']); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($trans['transaction_date'])); ?></td>
                                    <td class="fw-bold">₱<?php echo number_format($trans['total_amount'] ?? 0, 2); ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-primary view-details" 
                                                data-transaction-id="<?php echo $trans['transaction_id']; ?>"
                                                data-bs-toggle="collapse" 
                                                data-bs-target="#details-<?php echo $trans['transaction_id']; ?>">
                                            <i class="fas fa-eye me-1"></i> View Details
                                        </button>
                                        <a href="payment.php?ref=<?php echo $trans['transaction_reference']; ?>" 
                                           class="btn btn-sm btn-success">
                                            <i class="fas fa-money-bill me-1"></i> Process Payment
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="7" class="p-0">
                                        <div class="collapse" id="details-<?php echo $trans['transaction_id']; ?>">
                                            <div class="card card-body m-2">
                                                <h6 class="mb-3">Transaction Details</h6>
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>Item</th>
                                                            <th>Unit</th>
                                                            <th>Quantity</th>
                                                            <th>Unit Price</th>
                                                            <th>Discount</th>
                                                            <th>Subtotal</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach($trans['items'] as $item): ?>
                                                        <tr>
                                                            <td><?php echo htmlspecialchars($item['generaldescription']); ?></td>
                                                            <td><?php echo htmlspecialchars($item['unitmeasure']); ?></td>
                                                            <td><?php echo $item['quantity']; ?></td>
                                                            <td>₱<?php echo number_format($item['unit_price'], 2); ?></td>
                                                            <td><?php echo $item['discount_applied']; ?>%</td>
                                                            <td>₱<?php echo number_format($item['subtotal'], 2); ?></td>
                                                        </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Handle view details buttons
            const viewDetailsButtons = document.querySelectorAll('.view-details');
            viewDetailsButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const transactionId = this.getAttribute('data-transaction-id');
                    const detailsRow = document.querySelector(`#details-${transactionId}`);
                    
                    // Toggle icon between eye and eye-slash
                    const icon = this.querySelector('i');
                    if (detailsRow.classList.contains('show')) {
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    } else {
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    }
                });
            });
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('priceSearchInput');
            const categoryFilter = document.getElementById('categoryFilter');
            const tableRows = document.querySelectorAll('.price-item');

            function filterTable() {
                const searchTerm = searchInput.value.toLowerCase();
                const selectedCategory = categoryFilter.value.toLowerCase();

                tableRows.forEach(row => {
                    const medicine = row.cells[0].textContent.toLowerCase();
                    const category = row.cells[1].textContent.toLowerCase();
                    
                    const matchesSearch = medicine.includes(searchTerm);
                    const matchesCategory = !selectedCategory || category === selectedCategory;

                    row.style.display = (matchesSearch && matchesCategory) ? '' : 'none';
                });
            }

            searchInput.addEventListener('input', filterTable);
            categoryFilter.addEventListener('change', filterTable);

            // Add focus animation
            searchInput.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.style.transition = 'transform 0.2s ease';
            });

            searchInput.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Initialize sales chart
        const ctx = document.getElementById('salesChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: <?php 
                    $weekDays = [];
                    $startOfWeek = new DateTime('monday this week');
                    for($i = 0; $i < 7; $i++) {
                        $date = clone $startOfWeek;
                        $date->modify("+$i days");
                        $weekDays[] = $date->format('D m/d');
                    }
                    echo json_encode($weekDays);
                ?>,
                datasets: [{
                    label: 'Sales',
                    data: <?php echo json_encode($dailySalesData); ?>, // Replace with your actual sales data
                    borderColor: '#2563eb',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Real-time updates
        function updateDashboardStats() {
            fetch('/api/dashboard-stats')
                .then(response => response.json())
                .then(data => {
                    // Update stats here
                })
                .catch(error => console.error('Error:', error));
        }

        // Update every 5 minutes
        setInterval(updateDashboardStats, 300000);

        // Mobile sidebar toggle function
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const toggleButton = document.querySelector('.mobile-toggle');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(event.target) &&
                !toggleButton.contains(event.target) &&
                sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');
            if (window.innerWidth > 768) {
                sidebar.classList.remove('show');
            }
        });
    </script>
</body>
</html>
