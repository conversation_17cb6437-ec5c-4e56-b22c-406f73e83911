<?php
require_once 'database.php';

// Get current year and month
$currentYear = date('Y');
$currentMonth = date('m');

// Fetch monthly performance data
$monthlyData = $conn->query("
    SELECT 
        MONTH(t.transaction_date) as month,
        SUM(td.amount) as total_amount
    FROM transactions t
    LEFT JOIN transaction_details td ON t.transaction_id = td.transaction_id
    WHERE YEAR(t.transaction_date) = $currentYear
    GROUP BY MONTH(t.transaction_date)
    ORDER BY month
")->fetchAll();

// Fetch quarterly performance data
$quarterlyData = $conn->query("
    SELECT 
        QUARTER(t.transaction_date) as quarter,
        SUM(td.amount) as total_amount
    FROM transactions t
    LEFT JOIN transaction_details td ON t.transaction_id = td.transaction_id
    WHERE YEAR(t.transaction_date) = $currentYear
    GROUP BY QUARTER(t.transaction_date)
    ORDER BY quarter
")->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Dashboard - BIRI District Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <style>
        body {
            background: #f8f9fa;
            font-family: system-ui, -apple-system, sans-serif;
        }
        .chart-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
            padding: 24px;
            margin-bottom: 24px;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <h2 class="mb-4">Performance Overview <?php echo $currentYear; ?></h2>
        
        <!-- Monthly Performance Chart -->
        <div class="chart-card">
            <h4>Monthly Performance</h4>
            <div id="monthlyChart"></div>
        </div>

        <!-- Quarterly Performance Chart -->
        <div class="chart-card">
            <h4>Quarterly Performance</h4>
            <div id="quarterlyChart"></div>
        </div>
    </div>

    <script>
        // Monthly Performance Chart
        const monthlyOptions = {
            series: [{
                name: 'Monthly Performance',
                data: <?php 
                    $monthlyValues = array_fill(0, 12, 0);
                    foreach ($monthlyData as $data) {
                        $monthlyValues[$data['month']-1] = floatval($data['total_amount']);
                    }
                    echo json_encode($monthlyValues);
                ?>
            }],
            chart: {
                height: 350,
                type: 'area',
                toolbar: {
                    show: false
                },
                animations: {
                    enabled: true,
                    easing: 'easeinout',
                    speed: 800
                }
            },
            stroke: {
                curve: 'smooth',
                width: 3
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.7,
                    opacityTo: 0.2,
                    stops: [0, 90, 100]
                }
            },
            dataLabels: {
                enabled: false
            },
            xaxis: {
                categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
            },
            colors: ['#4CAF50'],
            tooltip: {
                theme: 'dark',
                y: {
                    formatter: function(value) {
                        return '₱' + value.toLocaleString()
                    }
                }
            }
        };

        // Quarterly Performance Chart
        const quarterlyOptions = {
            series: [{
                name: 'Quarterly Performance',
                data: <?php 
                    $quarterlyValues = array_fill(0, 4, 0);
                    foreach ($quarterlyData as $data) {
                        $quarterlyValues[$data['quarter']-1] = floatval($data['total_amount']);
                    }
                    echo json_encode($quarterlyValues);
                ?>
            }],
            chart: {
                height: 350,
                type: 'area',
                toolbar: {
                    show: false
                },
                animations: {
                    enabled: true,
                    easing: 'easeinout',
                    speed: 800,
                    animateGradually: {
                        enabled: true,
                        delay: 150
                    },
                    dynamicAnimation: {
                        enabled: true,
                        speed: 350
                    }
                }
            },
            stroke: {
                curve: 'smooth',
                width: 2.5
            },
            markers: {
                size: 0,
                strokeWidth: 0,
                hover: {
                    size: 9,
                    sizeOffset: 3
                }
            },
            dataLabels: {
                enabled: false,
                hover: {
                    enabled: true,
                    formatter: function(value) {
                        return '₱' + value.toLocaleString()
                    }
                },
                offsetY: -10,
                style: {
                    fontSize: '13px',
                    colors: ["#304758"]
                }
            },
            colors: ['#ffd60a'],
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.7,
                    opacityTo: 0.2,
                    stops: [0, 90, 100]
                }
            },
            xaxis: {
                categories: ['Q1', 'Q2', 'Q3', 'Q4'],
                position: 'bottom',
                labels: {
                    style: {
                        fontWeight: 600
                    }
                }
            },
            yaxis: {
                labels: {
                    formatter: function(value) {
                        return '₱' + value.toLocaleString()
                    }
                }
            },
            tooltip: {
                theme: 'dark',
                y: {
                    formatter: function(value) {
                        return '₱' + value.toLocaleString()
                    }
                }
            }
        };

        const monthlyChart = new ApexCharts(document.querySelector("#monthlyChart"), monthlyOptions);
        const quarterlyChart = new ApexCharts(document.querySelector("#quarterlyChart"), quarterlyOptions);
        
        monthlyChart.render();
        quarterlyChart.render();
    </script>
</body>
</html>
