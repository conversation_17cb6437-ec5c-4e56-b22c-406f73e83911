<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Code 128 barcodes</title>
<style type="text/css">
body {font-family:"Times New Roman",serif}
h1 {font:bold 135% Arial,sans-serif; color:#4000A0; margin-bottom:0.9em}
h2 {font:bold 95% Arial,sans-serif; color:#900000; margin-top:1.5em; margin-bottom:1em}
</style>
</head>
<body>
<h1>Code 128 barcodes</h1>
<h2>Informations</h2>
Author: <a href="mailto:<EMAIL>?subject=Code%20128%20barcodes"><PERSON></a><br>
License: FPDF
<h2>Description</h2>
This script handles Code 128 barcodes (A, B and C). All the 128 ASCII characters are available.
A, B and C character sets are automatically selected according to the value to print.<br>
<br>
<code>Code128(<b>float</b> x, <b>float</b> y, <b>string</b> code, <b>float</b> w, <b>float</b> h)</code><br>
<br>
<code><u>x</u></code>: abscissa<br>
<code><u>y</u></code>: ordinate<br>
<code><u>code</u></code>: barcode value<br>
<code><u>w</u></code>: total width<br>
<code><u>h</u></code>: height<br>
</body>
</html>
