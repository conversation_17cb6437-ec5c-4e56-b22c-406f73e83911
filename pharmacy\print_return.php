<?php
require_once '../database.php';
require_once '../fpdf/fpdf.php';
session_start();

// Check if user is not logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Get rbid from URL
$rbid = isset($_GET['rbid']) ? $_GET['rbid'] : '';

// Get return details
$sql = "SELECT r.*, rb.rbid, i.generaldescription, rb.remarks, rb.date_returned as batch_date,
               u.username, e.emp_name
        FROM return_to_supplier r 
        JOIN items i ON r.itemid = i.itemid 
        JOIN returnbatch rb ON r.rbid = rb.rbid
        LEFT JOIN users u ON rb.user_id = u.user_id
        LEFT JOIN employees e ON u.emp_id = e.id
        WHERE r.rbid = ?";

$stmt = $conn->prepare($sql);
$stmt->execute([$rbid]);
$returns = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Check if data exists
if (empty($returns)) {
    header('Content-Type: text/html; charset=utf-8');
    echo '<div style="text-align: center; margin-top: 50px; font-family: Arial, sans-serif;">';
    echo '<h2 style="color: #dc3545;">No Return Data Found</h2>';
    echo '<p>No return data found for the selected batch.</p>';
    echo '<a href="javascript:history.back()" style="text-decoration: none; background-color: #007bff; color: white; padding: 10px 20px; border-radius: 5px;">Go Back</a>';
    echo '</div>';
    exit;
}

// Initialize PDF
$pdf = new FPDF('L', 'mm', 'A4');
$pdf->AddPage();
$pdf->SetMargins(15, 15, 15);

// Modern header with logos
$pdf->Image('../images/pgns.png', 25, 15, 20);
$pdf->Image('../images/bdh.png', 250, 15, 20);

// Enhanced typography for header
$pdf->SetFont('Arial', '', 11);
$pdf->Cell(0, 5, 'Republic of the Philippines', 0, 1, 'C');
$pdf->Cell(0, 5, 'Province of Northern Samar', 0, 1, 'C');
$pdf->Cell(0, 5, 'Provincial Health Office', 0, 1, 'C');
$pdf->SetFont('Arial', 'B', 16);
$pdf->Ln(2);
$pdf->Cell(0, 8, 'BIRI DISTRICT HOSPITAL', 0, 1, 'C');
$pdf->SetFont('Arial', 'I', 10);
$pdf->Cell(0, 5, 'Biri Northern Samar', 0, 1, 'C');
$pdf->SetFont('Arial', 'B', 14);
$pdf->Ln(3);
$pdf->Cell(0, 8, 'RETURN TO SUPPLIER TRANSMITTAL', 0, 1, 'C');
$pdf->Ln(5);

// Return batch information section with organized layout
$pdf->SetFillColor(245, 245, 245);
$pdf->Rect(15, $pdf->GetY(), 267, 35, 'F');
$pdf->SetFont('Arial', 'B', 12);
$pdf->Cell(0, 8, 'RETURN BATCH INFORMATION', 0, 1, 'C');
$pdf->Ln(2);

// Organized information in a structured format
$pdf->SetFont('Arial', '', 10);
$current_y = $pdf->GetY();

// Left column
$pdf->SetXY(25, $current_y);
$pdf->Cell(40, 6, 'Batch ID:', 0, 0, 'L');
$pdf->SetFont('Arial', 'B', 10);
$pdf->Cell(80, 6, $returns[0]['rbid'], 0, 1, 'L');

$pdf->SetXY(25, $pdf->GetY());
$pdf->SetFont('Arial', '', 10);
$pdf->Cell(40, 6, 'Return Date:', 0, 0, 'L');
$pdf->SetFont('Arial', 'B', 10);
$pdf->Cell(80, 6, date('F d, Y', strtotime($returns[0]['batch_date'])), 0, 1, 'L');

// Right column
$pdf->SetXY(155, $current_y);
$pdf->SetFont('Arial', '', 10);
$pdf->Cell(40, 6, 'Prepared by:', 0, 0, 'L');
$pdf->SetFont('Arial', 'B', 10);
$pdf->Cell(80, 6, $returns[0]['emp_name'], 0, 1, 'L');

$pdf->SetXY(155, $current_y + 6);
$pdf->SetFont('Arial', '', 10);
$pdf->Cell(40, 6, 'Total Items:', 0, 0, 'L');
$pdf->SetFont('Arial', 'B', 10);
$pdf->Cell(80, 6, count($returns) . ' items', 0, 1, 'L');

// Add remarks if available
if (!empty($returns[0]['remarks'])) {
    $pdf->SetXY(25, $current_y + 12);
    $pdf->SetFont('Arial', '', 10);
    $pdf->Cell(40, 6, 'Remarks:', 0, 0, 'L');
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(200, 6, $returns[0]['remarks'], 0, 1, 'L');
}

// Add spacing after batch information
$pdf->Ln(8);

// Modern table design
$pdf->SetFillColor(40, 65, 100);
$pdf->SetTextColor(255);
$pdf->SetFont('Arial', 'B', 9);

// Adjusted column widths for landscape
$w = array(80, 25, 30, 20, 25, 25, 62);
$headers = array('Item Description', 'Lot No.', 'Supplier', 'Qty', 'Unit Cost', 'Total', 'Reason');

// Table headers with modern styling
foreach($headers as $i => $header) {
    $pdf->Cell($w[$i], 8, $header, 1, 0, 'C', true);
}
$pdf->Ln();

// Table content with improved styling
$pdf->SetTextColor(0);
$pdf->SetFont('Arial', '', 8);
$total_amount = 0;

foreach ($returns as $index => $row) {
    $total = $row['quantity_returned'] * $row['unit_cost'];
    $total_amount += $total;
    
    // Zebra striping
    $pdf->SetFillColor($index % 2 == 0 ? 250 : 245, $index % 2 == 0 ? 250 : 245, $index % 2 == 0 ? 250 : 245);
    
    // Print row data
    $pdf->Cell($w[0], 7, $row['generaldescription'], 1, 0, 'L', true);
    $pdf->Cell($w[1], 7, $row['lot_no'], 1, 0, 'C', true);
    $pdf->Cell($w[2], 7, $row['supplier'], 1, 0, 'L', true);
    $pdf->Cell($w[3], 7, number_format($row['quantity_returned']), 1, 0, 'R', true);
    $pdf->Cell($w[4], 7, number_format($row['unit_cost'], 2), 1, 0, 'R', true);
    $pdf->Cell($w[5], 7, number_format($total, 2), 1, 0, 'R', true);
    $pdf->Cell($w[6], 7, $row['return_reason'], 1, 1, 'L', true);
}

// Modern total amount display
$pdf->SetFillColor(40, 65, 100);
$pdf->SetTextColor(255);
$pdf->SetFont('Arial', 'B', 9);
$pdf->Cell(array_sum($w) - 25, 8, 'Total Amount:', 1, 0, 'R', true);
$pdf->Cell(25, 8, 'PHP ' . number_format($total_amount, 2), 1, 1, 'R', true);

// Signature section with table layout
$pdf->Ln(5);
$pdf->SetTextColor(0);
$pdf->SetFont('Arial', '', 9);

// Create signature table
$pdf->SetFillColor(245, 245, 245);
$pdf->Cell(89, 8, 'Prepared by:', 1, 0, 'C', true);
$pdf->Cell(89, 8, 'Approved by:', 1, 0, 'C', true);
$pdf->Cell(89, 8, 'Received by:', 1, 1, 'C', true);

$pdf->Cell(89, 15, '', 1, 0, 'C');
$pdf->Cell(89, 15, '', 1, 0, 'C');
$pdf->Cell(89, 15, '', 1, 1, 'C');

$pdf->SetFont('Arial', '', 8);
$pdf->Cell(89, 8, $returns[0]['emp_name'], 1, 0, 'C');
$pdf->Cell(89, 8, 'LUCILLE G. ROMINES, MD, FPCP', 1, 0, 'C');
$pdf->Cell(89, 8, '', 1, 1, 'C');

$pdf->Cell(89, 8, 'Pharmacist', 1, 0, 'C');
$pdf->Cell(89, 8, 'Chief of Hospital', 1, 0, 'C');
$pdf->Cell(89, 8, 'Name and Signature', 1, 1, 'C');

$pdf->Cell(89, 8, 'Date: ' . date('F d, Y'), 1, 0, 'C');
$pdf->Cell(89, 8, 'Date:', 1, 0, 'C');
$pdf->Cell(89, 8, 'Date:', 1, 1, 'C');

// Output the PDF
$pdf->Output('I', 'return_transmittal_' . $rbid . '.pdf');
