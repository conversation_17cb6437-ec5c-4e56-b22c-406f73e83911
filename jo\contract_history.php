<?php
include_once '../jo/database.php'; 

// Process form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $stmt = $conn->prepare("INSERT INTO jo_contract (employee_id, appointment_from, appointment_to, designation, rate, assignment_area) VALUES (?, ?, ?, ?, ?, ?)");
                $stmt->execute([$_POST['employee_id'], $_POST['appointment_from'], $_POST['appointment_to'], $_POST['designation'], $_POST['rate'], $_POST['assignment_area']]);
                break;
            case 'update':
                $stmt = $conn->prepare("UPDATE jo_contract SET appointment_from=?, appointment_to=?, designation=?, rate=?, assignment_area=? WHERE contract_id=?");
                $stmt->execute([$_POST['appointment_from'], $_POST['appointment_to'], $_POST['designation'], $_POST['rate'], $_POST['assignment_area'], $_POST['contract_id']]);
                break;
            case 'delete':
                $stmt = $conn->prepare("DELETE FROM jo_contract WHERE contract_id=?");
                $stmt->execute([$_POST['contract_id']]);
                break;
        }
        header("Location: " . $_SERVER['PHP_SELF'] . "?id=" . $_GET['id']);
        exit;
    }
}

// Get profile_id from URL parameter
$profile_id = isset($_GET['id']) ? $_GET['id'] : null;

// Fetch employee profile
$profile_stmt = $conn->prepare("SELECT * FROM jo_profile WHERE profile_id = ?");
$profile_stmt->execute([$profile_id]);
$profile = $profile_stmt->fetch(PDO::FETCH_ASSOC);

// Fetch contract history
$contract_stmt = $conn->prepare("SELECT * FROM jo_contract WHERE employee_id = ? ORDER BY appointment_from DESC");
$contract_stmt->execute([$profile['employee_id']]);
$contracts = $contract_stmt->fetchAll(PDO::FETCH_ASSOC);

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contract History - <?php echo htmlspecialchars($profile['jo_name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
          <style>
        :root {
            --primary-color: #2c7be5;
            --secondary-color: #1e5daf;
            --accent-color: #4a94ea;
            --danger-color: #dc3545;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --background-color: #f8f9fa;
            --card-background: #ffffff;
            --text-primary: #2d3436;
            --text-secondary: #636e72;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--background-color);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        .profile-section {
            background-color: var(--card-background);
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
        }

        .profile-section::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, var(--accent-color) 0%, transparent 100%);
            opacity: 0.1;
            border-radius: 0 10px 0 100%;
        }

        .contract-card {
            background-color: var(--card-background);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .contract-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .btn {
            border-radius: 6px;
            padding: 0.6rem 1.2rem;
            font-weight: 500;
            transition: all 0.3s ease;
            letter-spacing: 0.3px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border: none;
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
        }

        .modal-content {
            border-radius: 10px;
            border: none;
        }

        .modal-header {
            background-color: var(--primary-color);
            color: white;
            border-radius: 10px 10px 0 0;
            padding: 1.2rem;
        }

        .form-control {
            border-radius: 6px;
            padding: 0.6rem 1rem;
            border: 1px solid #dee2e6;
        }

        .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 123, 229, 0.25);
        }

        h2, h3 {
            color: var(--primary-color);
            font-weight: 600;
        }

        .info-group {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 1.2rem;
        }

        .info-group i {
            color: var(--primary-color);
        }

        .period-badge {
            background-color: #e9ecef;
            border-radius: 6px;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(5px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .contract-card {
            animation: fadeIn 0.4s ease-out forwards;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <!-- Employee Profile Section -->
           <div class="profile-section shadow-lg rounded-4 mb-5">
            <h2 class="mb-4 text-primary"><i class="fas fa-user-circle me-2"></i>Employee Profile</h2>
            <div class="row g-4">
                <div class="col-md-3">
                    <div class="profile-image-container text-center mb-3">
                        <?php 
                            $image_path = isset($profile['image_path']) && !empty($profile['image_path']) ? $profile['image_path'] : '../images/bdh.png';
                        ?>
                        <img src="<?php echo htmlspecialchars($image_path); ?>" alt="Profile Image" class="profile-image img-fluid rounded-circle" style="width: 200px; height: 200px; object-fit: cover;">
                    </div>
                </div>
                <div class="col-md-9">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-group p-3 bg-light rounded-3">
                                <p class="mb-3"><i class="fas fa-id-card me-2 text-primary"></i><strong>Employee ID:</strong> 
                                    <span class="ms-2"><?php echo htmlspecialchars($profile['employee_id']); ?></span></p>
                                <p class="mb-3"><i class="fas fa-user me-2 text-primary"></i><strong>Name:</strong> 
                                    <span class="ms-2"><?php echo htmlspecialchars($profile['jo_name']); ?></span></p>
                                <p class="mb-3"><i class="fas fa-birthday-cake me-2 text-primary"></i><strong>Birthday:</strong> 
                                    <span class="ms-2"><?php echo date('F d, Y', strtotime($profile['jo_birthday'])); ?></span></p>
                                <p class="mb-3"><i class="fas fa-venus-mars me-2 text-primary"></i><strong>Gender:</strong> 
                                    <span class="ms-2"><?php echo htmlspecialchars($profile['jo_gender']); ?></span></p>
                                <p class="mb-0"><i class="fas fa-map-marker-alt me-2 text-primary"></i><strong>Address:</strong> 
                                    <span class="ms-2"><?php echo htmlspecialchars($profile['jo_address']); ?></span></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group p-3 bg-light rounded-3">
                                <p class="mb-3"><i class="fas fa-graduation-cap me-2 text-primary"></i><strong>Course:</strong> 
                                    <span class="ms-2"><?php echo htmlspecialchars($profile['jo_course']); ?></span></p>
                                <p class="mb-3"><i class="fas fa-book me-2 text-primary"></i><strong>Educational Attainment:</strong> 
                                    <span class="ms-2"><?php echo htmlspecialchars($profile['jo_educ_attain']); ?></span></p>
                                <p class="mb-3"><i class="fas fa-certificate me-2 text-primary"></i><strong>Eligibility:</strong> 
                                    <span class="ms-2"><?php echo htmlspecialchars($profile['jo_eligibility']); ?></span></p>
                                <p class="mb-3"><i class="fas fa-history me-2 text-primary"></i><strong>Years in Service:</strong> 
                                    <span class="ms-2"><?php 
                                        $sql = "SELECT COALESCE(SUM(
                                                    TIMESTAMPDIFF(MONTH, c.appointment_from, COALESCE(c.appointment_to, CURRENT_DATE)) + 1
                                                ), 0) as total_months_service 
                                                FROM jo_contract c 
                                                WHERE c.employee_id = ?";
                                        $stmt = $conn->prepare($sql);
                                        $stmt->execute([$profile['employee_id']]);
                                        $service = $stmt->fetch(PDO::FETCH_ASSOC);
                                        
                                        $total_months = (int)$service['total_months_service'];
                                        $years = floor($total_months / 12);
                                        $months = $total_months % 12;
                                        
                                        if ($years > 0 && $months > 0) {
                                            echo $years . " year" . ($years > 1 ? "s" : "") . " and " . $months . " month" . ($months > 1 ? "s" : "");
                                        } elseif ($years > 0) {
                                            echo $years . " year" . ($years > 1 ? "s" : "");
                                        } else {
                                            echo $months . " month" . ($months > 1 ? "s" : "");
                                        }
                                    ?></span></p>
                                <p class="mb-0"><i class="fas fa-phone me-2 text-primary"></i><strong>Phone Number:</strong> 
                                    <span class="ms-2"><?php echo htmlspecialchars($profile['jo_phonenumber']); ?></span></p>
                                <p class="mb-0"><i class="fas fa-clipboard-list me-2 text-primary"></i><strong>Remarks:</strong> 
                                    <span class="ms-2"><?php echo htmlspecialchars($profile['Remarks']); ?></span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Contract History Section -->
        <div class="contract-history">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h3 class="text-primary"><i class="fas fa-history me-2"></i>Contract History</h3>
                <div class="d-flex align-items-center gap-2">
                    <button class="btn btn-primary shadow-sm" data-bs-toggle="modal" data-bs-target="#addContractModal">
                        <i class="fas fa-plus-circle me-2"></i>Add New Contract
                    </button>
                    <a href="export_contract_pdf.php?id=<?php echo $profile_id; ?>" class="btn btn-success shadow-sm <?php echo !$contracts ? 'disabled' : ''; ?>">
                        <i class="fas fa-file-pdf me-2"></i>Export to PDF
                    </a>
                    <a href="certificate_of_employment_pdf.php?id=<?php echo $profile_id; ?>" class="btn btn-info shadow-sm <?php echo !$contracts ? 'disabled' : ''; ?>">
                        <i class="fas fa-file-certificate me-2"></i>Print Certificate
                    </a>
                    <a href="index.php" class="btn btn-secondary shadow-sm">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </a>
                </div>
            </div>

            <?php if ($contracts): ?>
                <?php foreach ($contracts as $contract): ?>
                    <div class="contract-card shadow hover-shadow">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <div class="period-badge bg-light p-3 rounded-3 text-center">
                                    <i class="fas fa-calendar-alt text-primary mb-2"></i>
                                    <p class="mb-0"><strong>Contract Period</strong><br>
                                    <small><?php 
                                        echo date('M d, Y', strtotime($contract['appointment_from'])) . ' to<br>' . 
                                             date('M d, Y', strtotime($contract['appointment_to']));
                                    ?></small></p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <p class="mb-0"><i class="fas fa-briefcase text-primary me-2"></i><strong>Designation</strong><br>
                                <span class="ms-4"><?php echo htmlspecialchars($contract['designation']); ?></span></p>
                            </div>
                            <div class="col-md-3">
                                <p class="mb-2"><i class="fas fa-money-bill-wave text-success me-2"></i><strong>Rate</strong>
                                    <span class="ms-2">₱<?php echo number_format($contract['rate'], 2); ?></span></p>
                                <p class="mb-0"><i class="fas fa-map-marked-alt text-primary me-2"></i><strong>Area</strong>
                                    <span class="ms-2"><?php echo $contract['assignment_area']; ?></span></p>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-warning btn-sm me-2 shadow-sm" onclick="editContract(<?php echo htmlspecialchars(json_encode($contract)); ?>)">
                                    <i class="fas fa-edit me-1"></i>Edit
                                </button>
                                <button class="btn btn-danger btn-sm shadow-sm" onclick="deleteContract(<?php echo $contract['contract_id']; ?>)">
                                    <i class="fas fa-trash-alt me-1"></i>Delete
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="alert alert-info shadow-sm">
                    <i class="fas fa-info-circle me-2"></i>No contract history found.
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Add Contract Modal -->
    <div class="modal fade" id="addContractModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Contract</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">
                        <input type="hidden" name="employee_id" value="<?php echo $profile['employee_id']; ?>">
                        <div class="mb-3">
                            <label>Appointment From</label>
                            <input type="date" name="appointment_from" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label>Appointment To</label>
                            <input type="date" name="appointment_to" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label>Designation</label>
                            <input type="text" name="designation" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label>Rate</label>
                            <input type="number" step="0.01" name="rate" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label>Assignment Area</label>
                             <input type="text" name="assignment_area" class="form-control" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary">Add Contract</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Contract Modal -->
    <div class="modal fade" id="editContractModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Contract</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update">
                        <input type="hidden" name="contract_id" id="edit_contract_id">
                        <div class="mb-3">
                            <label>Appointment From</label>
                            <input type="date" name="appointment_from" id="edit_appointment_from" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label>Appointment To</label>
                            <input type="date" name="appointment_to" id="edit_appointment_to" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label>Designation</label>
                            <input type="text" name="designation" id="edit_designation" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label>Rate</label>
                            <input type="number" step="0.01" name="rate" id="edit_rate" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label>Assignment Area</label>
                            <input type="text" name="assignment_area" id="edit_assignment_area" class="form-control" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary">Update Contract</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Contract Modal -->
    <div class="modal fade" id="deleteContractModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Delete Contract</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="contract_id" id="delete_contract_id">
                        <p>Are you sure you want to delete this contract?</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Delete</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editContract(contract) {
            document.getElementById('edit_contract_id').value = contract.contract_id;
            document.getElementById('edit_appointment_from').value = contract.appointment_from;
            document.getElementById('edit_appointment_to').value = contract.appointment_to;
            document.getElementById('edit_designation').value = contract.designation;
            document.getElementById('edit_rate').value = contract.rate;
            document.getElementById('edit_assignment_area').value = contract.assignment_area;
            new bootstrap.Modal(document.getElementById('editContractModal')).show();
        }

        function deleteContract(contractId) {
            document.getElementById('delete_contract_id').value = contractId;
            new bootstrap.Modal(document.getElementById('deleteContractModal')).show();
        }
    </script>
</body>
</html>
