<?php
require_once 'database.php';
require('fpdf/fpdf.php');

// Get current year
$currentYear = date('Y');

// Get selected year or use current year as default
$selectedYear = isset($_GET['year']) ? $_GET['year'] : $currentYear;

// Fetch account summary data with transaction details
$accountSummary = $conn->query("
    SELECT 
        a.account_code,
        a.account_description,
        a.account_amount as budget,
        COALESCE(SUM(td.amount), 0) as utilized,
        (a.account_amount - COALESCE(SUM(td.amount), 0)) as remaining,
        COUNT(DISTINCT td.transaction_id) as transaction_count
    FROM accounts a
    LEFT JOIN transaction_details td ON a.account_code = td.account_code
    LEFT JOIN transactions t ON td.transaction_id = t.transaction_id AND YEAR(t.transaction_date) = $selectedYear
    WHERE a.account_year = $selectedYear
    GROUP BY a.account_code, a.account_description, a.account_amount
    ORDER BY utilized DESC")->fetchAll();

// Fetch department expenditure data with employee details
$departmentExpenditure = $conn->query("
    SELECT 
        d.department_name,
        COUNT(DISTINCT e.id) as employee_count,
        COUNT(DISTINCT t.transaction_id) as transaction_count,
        COALESCE(SUM(td.amount), 0) as total_spent,
        MAX(t.transaction_date) as last_transaction_date
    FROM departments d
    LEFT JOIN employees e ON d.id = e.department_id
    LEFT JOIN transactions t ON e.id = t.employee_id
    LEFT JOIN transaction_details td ON t.transaction_id = td.transaction_id
    WHERE YEAR(t.transaction_date) = $selectedYear OR t.transaction_date IS NULL
    GROUP BY d.department_name
    ORDER BY total_spent DESC")->fetchAll();

// Calculate total budget and utilization
$totalBudget = 0;
$totalUtilized = 0;
$totalTransactions = 0;
foreach ($accountSummary as $account) {
    $totalBudget += $account['budget'];
    $totalUtilized += $account['utilized'];
    $totalTransactions += $account['transaction_count'];
}
$utilizationRate = ($totalBudget > 0) ? ($totalUtilized / $totalBudget) * 100 : 0;

// Get total number of active accounts
$totalAccounts = $conn->query("SELECT COUNT(*) FROM accounts WHERE account_year = $selectedYear")->fetchColumn();

// Get department statistics
$totalDepartments = $conn->query("SELECT COUNT(*) FROM departments")->fetchColumn();
$totalEmployees = $conn->query("SELECT COUNT(*) FROM employees")->fetchColumn();

// Create PDF document
$pdf = new FPDF('P', 'mm', 'A4');
$pdf->AddPage();

// Add header and logos
$pdf->Image('images/pgns.png', 40, 10, 25);
$pdf->Image('images/bdh.png', 145, 10, 25);

// Header section
$pdf->SetFont('Arial', '', 12);
$pdf->Cell(0, 6, 'Republic of the Philippines', 0, 1, 'C');
$pdf->Cell(0, 6, 'Province of Northern Samar', 0, 1, 'C');
$pdf->Cell(0, 6, 'Provincial Health Office', 0, 1, 'C');
$pdf->SetFont('Arial', 'B', 16);
$pdf->Cell(0, 8, 'BIRI DISTRICT HOSPITAL', 0, 1, 'C');
$pdf->SetFont('Arial', 'I', 11);
$pdf->Cell(0, 6, 'Biri Northern Samar', 0, 1, 'C');

// Report title
$pdf->SetFont('Arial', 'B', 14);
$pdf->Cell(0, 10, 'FINANCIAL AND ACCOUNT REPORT', 0, 1, 'C');
$pdf->SetFont('Arial', '', 11);
$pdf->Cell(0, 6, 'Fiscal Year ' . $selectedYear, 0, 1, 'C');
$pdf->Cell(0, 6, 'Generated on ' . date('F d, Y'), 0, 1, 'C');

// Summary section
$pdf->Ln(10);
$pdf->SetFont('Arial', 'B', 12);
$pdf->Cell(0, 8, 'OVERVIEW', 0, 1, 'L');
$pdf->SetFont('Arial', '', 10);

// Financial Statistics
$pdf->SetFillColor(245, 245, 245);
$pdf->SetFont('Arial', 'B', 11);
$pdf->Cell(0, 8, 'Financial Summary', 0, 1, 'L');
$pdf->SetFont('Arial', '', 10);

// Budget Information
$pdf->Cell(100, 7, 'Total Budget:', 1, 0, 'L', true);
$pdf->Cell(90, 7, 'PHP ' . number_format($totalBudget, 2), 1, 1, 'R', true);
$pdf->Cell(100, 7, 'Total Utilized:', 1, 0, 'L');
$pdf->Cell(90, 7, 'PHP ' . number_format($totalUtilized, 2), 1, 1, 'R');
$pdf->Cell(100, 7, 'Utilization Rate:', 1, 0, 'L', true);
$pdf->Cell(90, 7, number_format($utilizationRate, 1) . '%', 1, 1, 'R', true);
$pdf->Cell(100, 7, 'Total Transactions:', 1, 0, 'L');
$pdf->Cell(90, 7, number_format($totalTransactions), 1, 1, 'R');
$pdf->Ln(5);

// Organizational Statistics
$pdf->SetFont('Arial', 'B', 11);
$pdf->Cell(0, 8, 'Organizational Overview', 0, 1, 'L');
$pdf->SetFont('Arial', '', 10);

$pdf->Cell(100, 7, 'Active Accounts:', 1, 0, 'L', true);
$pdf->Cell(90, 7, number_format($totalAccounts), 1, 1, 'R', true);
$pdf->Cell(100, 7, 'Total Departments:', 1, 0, 'L');
$pdf->Cell(90, 7, number_format($totalDepartments), 1, 1, 'R');
$pdf->Cell(100, 7, 'Total Employees:', 1, 0, 'L', true);
$pdf->Cell(90, 7, number_format($totalEmployees), 1, 1, 'R', true);
$pdf->Ln(8);

// Account Details Section
$pdf->SetFont('Arial', 'B', 12);
$pdf->Cell(0, 8, 'ACCOUNT DETAILS', 0, 1, 'L');
$pdf->SetFont('Arial', '', 9);

// Account table headers
$pdf->SetFillColor(230, 230, 230);
$pdf->Cell(70, 8, 'Description', 1, 0, 'C', true);
$pdf->Cell(30, 8, 'Code', 1, 0, 'C', true);
$pdf->Cell(30, 8, 'Budget', 1, 0, 'C', true);
$pdf->Cell(30, 8, 'Utilized', 1, 0, 'C', true);
$pdf->Cell(30, 8, 'Remaining', 1, 1, 'C', true);

// Account details
foreach ($accountSummary as $account) {
    $pdf->MultiCell(70, 6, $account['account_description'], 1, 'L');
    $y = $pdf->GetY();
    $pdf->SetXY($pdf->GetX() + 70, $y - 6);
    $pdf->Cell(30, 6, $account['account_code'], 1, 0, 'L');
    $pdf->Cell(30, 6, number_format($account['budget'], 2), 1, 0, 'R');
    $pdf->Cell(30, 6, number_format($account['utilized'], 2), 1, 0, 'R');
    $pdf->Cell(30, 6, number_format($account['remaining'], 2), 1, 1, 'R');
}

// Department Expenditure Section
$pdf->Ln(5);
$pdf->SetFont('Arial', 'B', 11);
$pdf->Cell(0, 6, 'DEPARTMENT EXPENDITURE', 0, 1, 'L');
$pdf->SetFont('Arial', '', 8);

// Department table headers
$pdf->SetFillColor(230, 230, 230);
$pdf->Cell(60, 6, 'Department', 1, 0, 'C', true);
$pdf->Cell(35, 6, 'Employees', 1, 0, 'C', true);
$pdf->Cell(35, 6, 'Transactions', 1, 0, 'C', true);
$pdf->Cell(60, 6, 'Total Spent', 1, 1, 'C', true);

// Department details
foreach ($departmentExpenditure as $dept) {
    $pdf->Cell(60, 5, $dept['department_name'], 1, 0, 'L');
    $pdf->Cell(35, 5, $dept['employee_count'], 1, 0, 'C');
    $pdf->Cell(35, 5, $dept['transaction_count'], 1, 0, 'C');
    $pdf->Cell(60, 5, 'PHP ' . number_format($dept['total_spent'], 2), 1, 1, 'R');
}

// Authorization section
$pdf->Ln(5);
$pdf->SetFont('Arial', '', 10);
$pdf->Cell(95, 5, 'Prepared by:', 0, 0, 'L');
$pdf->Cell(95, 5, 'Approved by:', 0, 1, 'L');
$pdf->Ln(10);

$pdf->SetFont('Arial', 'B', 10);
$pdf->Cell(95, 4, 'EDUARDO O. SABANGAN, JR', 0, 0, 'L');
$pdf->Cell(95, 4, 'LUCILLE G. ROMINES, MD, FPCP', 0, 1, 'L');
$pdf->SetFont('Arial', 'I', 9);
$pdf->Cell(95, 4, 'Senior Bookkeeper', 0, 0, 'L');
$pdf->Cell(95, 4, 'Chief of Hospital', 0, 1, 'L');

// Generate PDF
$pdf->Output('Account Report_' . $currentYear . '.pdf', 'D');
