<?php
require_once 'database.php';

$successMessage = "";
$errorMessage = "";

// Add Department
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['add_department'])) {
    try {
        $department_name = trim($_POST['department_name']);
        if (empty($department_name)) {
            throw new Exception("Department name cannot be empty");
        }
        
        $query = "INSERT INTO departments (department_name) VALUES (:department_name)";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':department_name', $department_name);
        
        if ($stmt->execute()) {
            $successMessage = "Department added successfully!";
        }
    } catch(Exception $e) {
        $errorMessage = "Error adding department: " . $e->getMessage();
    }
}

// Update Department
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update_department'])) {
    try {
        $id = $_POST['id'];
        $department_name = trim($_POST['department_name']);
        
        if (empty($department_name)) {
            throw new Exception("Department name cannot be empty");
        }
        
        if (!is_numeric($id)) {
            throw new Exception("Invalid department ID");
        }
        
        $query = "UPDATE departments SET department_name = :department_name WHERE id = :id";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':department_name', $department_name);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        
        if ($stmt->execute()) {
            $successMessage = "Department updated successfully!";
        }
    } catch(Exception $e) {
        $errorMessage = "Error updating department: " . $e->getMessage();
    }
}

// Search Department
$searchQuery = isset($_POST['search']) ? trim($_POST['search']) : "";

try {
    $query = "SELECT * FROM departments WHERE department_name LIKE :search ORDER BY id DESC";
    $stmt = $conn->prepare($query);
    $searchParam = "%" . $searchQuery . "%";
    $stmt->bindParam(':search', $searchParam);
    $stmt->execute();
    $result = $stmt;
} catch(PDOException $e) {
    $errorMessage = "Error retrieving departments: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Department Management - BIRI District Hospital</title>
    <link rel="icon" type="image/x-icon" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00b4d8;
            --secondary-color: #0077b6;
            --accent-color: #48cae4;
            --success-color: #20c997;
            --warning-color: #ffd60a;
            --danger-color: #ff6b6b;
            --background-color: #f0f9ff;
            --card-color: #ffffff;
            --text-primary: #023e8a;
            --text-secondary: #0096c7;
        }

        body {
            background-color: var(--background-color);
            font-family: 'Poppins', sans-serif;
            color: var(--text-primary);
            background-image: linear-gradient(45deg, #f0f9ff 25%, #e9f2f9 25%, #e9f2f9 50%, #f0f9ff 50%, #f0f9ff 75%, #e9f2f9 75%, #e9f2f9 100%);
            background-size: 56.57px 56.57px;
        }
        
        .container {
            max-width: 1400px;
            padding: 2rem;
        }
        
        h2 {
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .card {
            border: none;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 180, 216, 0.15);
            padding: 1.5rem;
            background: var(--card-color);
            margin-bottom: 1.5rem;
            backdrop-filter: blur(10px);
            border-top: 3px solid var(--primary-color);
        }
        
        .content-wrapper {
            display: flex;
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .form-section {
            flex: 0 0 35%;
        }
        
        .table-section {
            flex: 0 0 65%;
        }
        
        .form-label {
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
        }
        
        .form-control, .form-select {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 0.75rem;
            transition: all 0.3s ease;
            background-color: rgba(255, 255, 255, 0.9);
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 180, 216, 0.2);
        }
        
        .btn {
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .btn-success {
            background-color: var(--success-color);
            border: none;
        }
        
        .btn-success:hover {
            background-color: #1db385;
            transform: translateY(-2px);
        }
        
        .table {
            margin: 0;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .table th {
            background-color: var(--primary-color);
            font-weight: 600;
            color: white;
            border: none;
            padding: 1rem;
        }
        
        .table td {
            vertical-align: middle;
            color: var(--text-primary);
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .btn-warning {
            background-color: var(--warning-color);
            border: none;
            color: var(--text-primary);
        }
        
        .btn-warning:hover {
            background-color: #fcc419;
            color: var(--text-primary);
            transform: translateY(-2px);
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        /* Digital elements */
        .digital-border {
            position: relative;
            overflow: hidden;
        }

        .digital-border::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            animation: borderFlow 2s linear infinite;
        }

        @keyframes borderFlow {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Pulse effect for icons */
        .fa-solid {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* Modern scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--background-color);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="digital-border"><i class="fa-solid fa-laptop-medical me-2"></i>Department Management System</h2>

        <?php if ($successMessage): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($successMessage); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
        <?php if ($errorMessage): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($errorMessage); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <!-- Search Form -->
        <div class="card digital-border">
            <form method="POST" class="mb-0">
                <div class="input-group">
                    <span class="input-group-text border-0 bg-light"><i class="fa-solid fa-microscope"></i></span>
                    <input type="text" name="search" class="form-control border-0 bg-light" placeholder="Search departments..." value="<?php echo htmlspecialchars($searchQuery); ?>" />
                    <button type="submit" class="btn btn-primary px-4">
                        <i class="fa-solid fa-search me-2"></i>Search
                    </button>
                </div>
            </form>
        </div>

        <div class="content-wrapper">
            <!-- Add/Edit Department Form -->
            <div class="form-section">
                <div class="card digital-border">
                    <form method="POST" id="departmentForm" class="needs-validation" novalidate>
                        <input type="hidden" name="id" id="departmentId" />
                        <div class="row g-4">
                            <div class="col-12">
                                <label for="department_name" class="form-label">
                                    <i class="fa-solid fa-hospital-user me-2"></i>Department Name
                                </label>
                                <input type="text" name="department_name" class="form-control" id="department_name" required />
                                <div class="invalid-feedback">Please enter the department name.</div>
                            </div>
                        </div>
                        <div class="mt-4 d-flex gap-3">
                            <button type="submit" name="add_department" id="submitButton" class="btn btn-success flex-grow-1">
                                <i class="fa-solid fa-plus-circle me-2"></i>Add Department
                            </button>
                            <a href="index.php" class="btn btn-primary">
                                <i class="fa-solid fa-home-alt me-2"></i>Homepage
                            </a>
                            <button type="button" id="cancelButton" class="btn btn-secondary d-none">
                                <i class="fa-solid fa-ban me-2"></i>Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Department List -->
            <div class="table-section">
                <div class="card digital-border table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th><i class="fa-solid fa-fingerprint me-2"></i>ID</th>
                                <th><i class="fa-solid fa-hospital me-2"></i>Department Name</th>
                                <th><i class="fa-solid fa-sliders me-2"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($row = $result->fetch(PDO::FETCH_ASSOC)) { ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($row['id']); ?></td>
                                    <td><?php echo htmlspecialchars($row['department_name']); ?></td>
                                    <td>
                                        <button onclick='editDepartment(<?php echo json_encode($row, JSON_HEX_APOS | JSON_HEX_QUOT); ?>)' class="btn btn-warning btn-sm">
                                            <i class="fa-solid fa-edit me-1"></i>Edit
                                        </button>
                                    </td>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Form validation
        (function () {
            'use strict'
            var forms = document.querySelectorAll('.needs-validation')
            Array.prototype.slice.call(forms).forEach(function (form) {
                form.addEventListener('submit', function (event) {
                    if (!form.checkValidity()) {
                        event.preventDefault()
                        event.stopPropagation()
                    }
                    form.classList.add('was-validated')
                }, false)
            })
        })()

        // Edit department function
        function editDepartment(department) {
            document.getElementById('departmentId').value = department.id;
            document.getElementById('department_name').value = department.department_name;

            // Change form button to Update
            const submitButton = document.getElementById('submitButton');
            submitButton.innerHTML = '<i class="fa-solid fa-sync-alt me-2"></i>Update Department';
            submitButton.name = 'update_department';

            // Show cancel button
            const cancelButton = document.getElementById('cancelButton');
            cancelButton.classList.remove('d-none');

            // Scroll to form with smooth animation
            document.querySelector('.form-section').scrollIntoView({ 
                behavior: 'smooth',
                block: 'start'
            });
        }

        // Cancel button handler
        document.getElementById('cancelButton').addEventListener('click', function() {
            document.getElementById('departmentForm').reset();
            document.getElementById('departmentId').value = '';
            
            // Reset form button to Add
            const submitButton = document.getElementById('submitButton');
            submitButton.innerHTML = '<i class="fa-solid fa-plus-circle me-2"></i>Add Department';
            submitButton.name = 'add_department';

            // Hide cancel button
            this.classList.add('d-none');

            // Remove validation classes
            document.getElementById('departmentForm').classList.remove('was-validated');
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
