<?php
require_once('../fpdf/fpdf.php');
require_once 'databasehdbo.php';

class PDF extends FPDF {
    function Header() {
        // Add header and logos
        if (file_exists('../images/pgns.png') && file_exists('../images/bdh.png')) {
            $this->Image('../images/pgns.png', 40, 10, 25);
            $this->Image('../images/bdh.png', 145, 10, 25);
        }

        // Modern header section with enhanced typography
        $this->SetFont('Arial', '', 12);
        $this->Cell(0, 6, 'Republic of the Philippines', 0, 1, 'C');
        $this->Cell(0, 6, 'Province of Northern Samar', 0, 1, 'C');
        $this->Cell(0, 6, 'Provincial Health Office', 0, 1, 'C');
        $this->SetFont('Arial', 'B', 16);
        $this->Cell(0, 8, 'BIRI DISTRICT HOSPITAL', 0, 1, 'C');
        $this->SetFont('Arial', 'I', 11);
        $this->Cell(0, 6, 'Biri Northern Samar', 0, 1, 'C');
        
        // Enhanced report title with subtle spacing
        $this->Ln(10);
        $this->SetFont('Arial', 'B', 14);
        $this->Cell(0, 10, 'Findings Status Report', 0, 1, 'C');
        $this->Ln(5);
    }
}

$pdf = new PDF();
$pdf->AddPage();
$pdf->SetFont('Arial', '', 12);

// Fetch findings data based on new table structure
$sql = "SELECT f.*, a.audit_date, a.audit_description 
        FROM findings f 
        LEFT JOIN audit a ON f.findings_audit_id = a.audit_id";
$stmt = $conn->prepare($sql);
$stmt->execute();
$findings = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Enhanced margins and styling
$leftMargin = 20;
$pdf->SetLeftMargin($leftMargin);
$pdf->SetFillColor(245, 245, 245);
$pdf->SetFont('Arial', 'B', 11);

foreach ($findings as $finding) {
    // Check if we need a page break with extra space for footer
    if ($pdf->GetY() > $pdf->GetPageHeight() - 60) {
        $pdf->AddPage();
    }

    // Finding number with enhanced styling
    $pdf->SetFont('Arial', 'B', 12);
    $pdf->Cell(0, 10, 'Finding #' . $finding['findings_id'], 0, 1, 'L');
    
    // Date with improved formatting
    $pdf->SetFont('Arial', 'I', 10);
    $pdf->Cell(0, 6, 'Date: ' . date('F d, Y', strtotime($finding['created_at'])), 0, 1, 'L');
    
    // Responsible Person
    $pdf->SetFont('Arial', '', 10);
    $pdf->Cell(0, 6, 'Responsible Person: ' . $finding['findings_responsible_person'], 0, 1, 'L');
    
    // Priority
    $pdf->Cell(0, 6, 'Priority: ' . $finding['findings_priority'], 0, 1, 'L');
    
    // Enhanced status styling with modern colors
    $pdf->SetFont('Arial', 'B', 10);
    $statusColor = [
        'Open' => [255, 204, 203],
        'In Progress' => [255, 243, 205],
        'Resolved' => [209, 231, 221],
        'Closed' => [200, 200, 200]
    ];
    $currentStatus = $finding['findings_status'];
    if (isset($statusColor[$currentStatus])) {
        $pdf->SetFillColor($statusColor[$currentStatus][0], $statusColor[$currentStatus][1], $statusColor[$currentStatus][2]);
    }
    $pdf->Cell(40, 8, 'Status: ' . $currentStatus, 0, 1, 'L', true);
    
    // Description with improved readability
    $pdf->SetFont('Arial', '', 11);
    $pdf->Ln(5);
    $pdf->MultiCell(0, 6, $finding['findings_description'], 0, 'L');
    
    // Remarks if available
    if (!empty($finding['findings_remarks'])) {
        $pdf->Ln(3);
        $pdf->SetFont('Arial', 'I', 10);
        $pdf->MultiCell(0, 6, 'Remarks: ' . $finding['findings_remarks'], 0, 'L');
    }
    
    // Completion date if available
    if (!empty($finding['findings_date_completed'])) {
        $pdf->Ln(3);
        $pdf->Cell(0, 6, 'Completed on: ' . date('F d, Y', strtotime($finding['findings_date_completed'])), 0, 1, 'L');
    }
    
    // Modern separator line
    $pdf->Ln(5);
    $pdf->SetDrawColor(200, 200, 200);
    $pdf->Line($leftMargin, $pdf->GetY(), $pdf->GetPageWidth() - $leftMargin, $pdf->GetY());
    $pdf->Ln(10);
}

// Authorization section
$pdf->Ln(15);
$pdf->SetFont('Arial', '', 11);
$pdf->Cell(95, 6, 'Prepared by:', 0, 0, 'L');
$pdf->Cell(95, 6, 'Noted by:', 0, 1, 'L');
$pdf->Ln(15);

$pdf->SetFont('Arial', 'B', 11);
$pdf->Cell(95, 5, 'CHRISTEL D. BRAVO', 0, 0, 'L');
$pdf->Cell(95, 5, 'LUCILLE G. ROMINES, MD, FPCP', 0, 1, 'L');
$pdf->SetFont('Arial', 'I', 10);
$pdf->Cell(95, 5, 'Administrative Officer IV', 0, 0, 'L');
$pdf->Cell(95, 5, 'Chief of Hospital', 0, 1, 'L');
$pdf->Ln(10);
// Footer section with page number and date
$pdf->SetFont('Arial', 'I', 10);
$pdf->SetY(-20);
$pdf->Cell(0, 10, 'Page ' . $pdf->PageNo() . ' of {nb}', 0, 0, 'C');
$pdf->Cell(0, 10, 'Generated on ' . date('F d, Y'), 0, 0, 'R');

$pdf->Output('I', 'findings_report.pdf');
