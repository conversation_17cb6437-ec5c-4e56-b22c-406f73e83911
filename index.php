<?php
require_once 'database.php';
session_start();
$department = $_SESSION['department'];
$usertype = $_SESSION['type'];
// Check if user is not logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Get employee name from database
$stmt = $conn->prepare("SELECT e.emp_name FROM employees e 
                       INNER JOIN users u ON u.emp_id = e.id 
                       WHERE u.user_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$employee = $stmt->fetch();
$_SESSION['emp_name'] = $employee['emp_name'] ?? 'Unknown Employee';

?>

<?php
// Check if department is Pharmacy Department
$isPharmacyDept = ($department === 'Pharmacy Department');
$isICTOffice = ($department === 'ICT Office');
$isCashierOffice=($department === 'Cashiers Office');
?>

<script>
// Execute when document is ready
document.addEventListener('DOMContentLoaded', function() {
    // Get the bookkeeping section element
    var bookkeepingSection = document.getElementById('section-bookkeeping');
    var jobOrderSection = document.getElementById('section-joborder');
    var cashierSection = document.getElementById('section-cashier');
    var processingSection = document.getElementById('section-processing');
     var pharmacySection = document.getElementById('section-pharmacy');
     var mainSection = document.getElementById('section-main');
    
    // Enable/disable based on department
     if (mainSection) {
        mainSection.style.display = <?php echo $isICTOffice ? "'block'" : "'none'"; ?>;
    }
    if (bookkeepingSection) {
        bookkeepingSection.style.display = <?php echo $isICTOffice ? "'block'" : "'none'"; ?>;
    }
    if (jobOrderSection) {
        jobOrderSection.style.display = <?php echo $isICTOffice ? "'block'" : "'none'"; ?>;
    }
    if (bookkeepingSection) {
        bookkeepingSection.style.display = <?php echo $isICTOffice ? "'block'" : "'none'"; ?>;
    }
     if (processingSection) {
        processingSection.style.display = <?php echo $isICTOffice ? "'block'" : "'none'"; ?>;
    }
      if (cashierSection) {
        cashierSection.style.display = <?php echo $isCashierOffice ? "'block'" : "'none'"; ?>;
    }
    // Allow both Cashier and Pharmacy Department to access pharmacy section
    if (pharmacySection) {
        pharmacySection.style.display = <?php echo ($isPharmacyDept || $isCashierOffice) ? "'block'" : "'none'"; ?>;
    }
    
    // If user is Administrator, show all sections
    if ('<?php echo $usertype; ?>' === 'Administrator') {
        if (bookkeepingSection) bookkeepingSection.style.display = 'block';
        if (jobOrderSection) jobOrderSection.style.display = 'block';
        if (cashierSection) cashierSection.style.display = 'block';
        if (processingSection) processingSection.style.display = 'block';
        if (pharmacySection) pharmacySection.style.display = 'block';
        if (mainSection) pharmacySection.style.display = 'block';
    }
});
</script>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Dashboard - BIRI District Hospital</title>
    <link rel="icon" type="image/x-icon" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="icon" type="image/png" href="images/bdh.png">
    <style>
        body {
            background-color: #e8f5fe;
            font-family: 'Inter', sans-serif;
            color: #005b9f;
            line-height: 1.6;
        }
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background: #ffffff;
            padding: 1.5rem;
            box-shadow: 2px 0 15px rgba(0,91,159,0.1);
            border-right: 3px solid #005b9f;
        }
        .sidebar-nav {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .sidebar-nav li {
            margin-bottom: 0.5rem;
        }
        .sidebar-nav a {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: #005b9f;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s;
        }
        .sidebar-nav a:hover {
            background: #e8f5fe;
            color: #003d6b;
            transform: translateX(5px);
        }
        .sidebar-nav i {
            margin-right: 0.75rem;
            width: 20px;
            text-align: center;
            color: #2196f3;
        }
        .main-content {
            margin-left: 250px;
            padding: 2rem;
        }
        .dashboard-header {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, #ffffff, #f8fdff);
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,91,159,0.1);
            border-left: 4px solid #2196f3;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: linear-gradient(135deg, #ffffff, #f8fdff);
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,91,159,0.1);
            border-top: 4px solid #2196f3;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-value {
            font-size: 2.2rem;
            font-weight: 700;
            color: #005b9f;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            color: #2196f3;
            font-size: 0.9rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .chart-container {
            background: linear-gradient(135deg, #ffffff, #f8fdff);
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,91,159,0.1);
            border-bottom: 4px solid #2196f3;
        }
        .side-widgets {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        .widget {
            background: linear-gradient(135deg, #ffffff, #f8fdff);
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,91,159,0.1);
            border-right: 4px solid #2196f3;
        }
        .clock {
            font-size: 2.2rem;
            font-weight: 700;
            color: #005b9f;
            text-align: center;
            text-shadow: 1px 1px 2px rgba(0,91,159,0.1);
        }
        .date {
            color: #2196f3;
            text-align: center;
            margin-top: 0.5rem;
            font-weight: 500;
            letter-spacing: 0.5px;
        }
    </style>
</head>
<body>
    <!-- Keep original sidebar -->
    <div class="sidebar">
        <h3 class="text-center mb-4"><i class="fa-solid fa-hospital me-2"></i>BIRI DISTRICT HOSPITAL</h3>
        <div class="text-center mb-4">
            <h5><i class="fa-solid fa-microchip me-2" style="color: #00b4d8"></i>HOSPITAL MANAGEMENT  SYSTEM</h5>
        </div>
        <!-- Navigation Controls -->
        <div class="navigation-controls mb-4">
            <div class="accordion" id="navigationAccordion">
                <!-- Bookkeeping Section -->
                 <div class="accordion-item border-0 mb-2" id="section-bookkeeping">
                    <div class="accordion-item border-0 mb-2">
                        <h2 class="accordion-header" id="bookkeepingHeader">
                            <button class="accordion-button nav-button primary-button" type="button" data-bs-toggle="collapse" data-bs-target="#bookkeepingCollapse" aria-expanded="true" aria-controls="bookkeepingCollapse">
                                <i class="fas fa-book me-2"></i>
                                <span>Bookkeeping</span>
                            </button>
                        </h2>
                        <div id="bookkeepingCollapse" class="accordion-collapse collapse show" aria-labelledby="bookkeepingHeader">
                            <div class="accordion-body p-0 mt-2">
                                <div class="list-group">
                                    <a href="employee.php" class="list-group-item list-group-item-action border-0 rounded-3 mb-1">
                                        <i class="fa-solid fa-users me-3 text-primary"></i>Employees
                                    </a>
                                    <a href="department.php" class="list-group-item list-group-item-action border-0 rounded-3 mb-1">
                                        <i class="fa-solid fa-building me-3 text-primary"></i>Departments
                                    </a>
                                    <a href="accounts.php" class="list-group-item list-group-item-action border-0 rounded-3 mb-1">
                                        <i class="fa-solid fa-money-bill-trend-up me-3 text-primary"></i>Accounts
                                    </a>
                                    <a href="transactions.php" class="list-group-item list-group-item-action border-0 rounded-3 mb-1">
                                        <i class="fa-solid fa-receipt me-3 text-primary"></i>Transactions
                                    </a>
                                    <a href="reports.php" class="list-group-item list-group-item-action border-0 rounded-3 mb-1">
                                        <i class="fa-solid fa-chart-line me-3 text-primary"></i>Reports
                                    </a>
                                    <a href="users.php" class="list-group-item list-group-item-action border-0 rounded-3">
                                        <i class="fa-solid fa-user me-3 text-primary"></i>Users
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Job Order Section -->
                 <div class="accordion-item border-0 mb-2" id="section-joborder">
                    <div class="accordion-item border-0">
                        <h2 class="accordion-header" id="jobOrderHeader">
                            <button class="accordion-button nav-button secondary-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#jobOrderCollapse" aria-expanded="false" aria-controls="jobOrderCollapse">
                                <i class="fas fa-file-invoice me-2"></i>
                                <span>Job Order</span>
                            </button>
                        </h2>
                        <div id="jobOrderCollapse" class="accordion-collapse collapse" aria-labelledby="jobOrderHeader">
                            <div class="accordion-body p-0 mt-2">
                                <div class="list-group">
                                    <a href="./jo/index.php" class="list-group-item list-group-item-action border-0 rounded-3">
                                        <i class="fa-solid fa-file-invoice me-3 text-primary"></i>Manage Job Order
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div id="jobOrderCollapse" class="accordion-collapse collapse" aria-labelledby="jobOrderHeader">
                            <div class="accordion-body p-0 mt-2">
                                <div class="list-group">
                                    <a href="./jo/cedula.php" class="list-group-item list-group-item-action border-0 rounded-3">
                                        <i class="fa-solid fa-id-card me-3 text-primary"></i>Cedula
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div id="jobOrderCollapse" class="accordion-collapse collapse" aria-labelledby="jobOrderHeader">
                            <div class="accordion-body p-0 mt-2">
                                <div class="list-group">
                                    <a href="./jo/phicsdaily_task.php" class="list-group-item list-group-item-action border-0 rounded-3">
                                        <i class="fa-solid fa-clipboard-list me-3 text-primary"></i>Daily Accomplishment
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                 </div>

                <!-- Cashier Section -->
                 <div class="accordion-item border-0 mb-2" id="section-cashier">
                    <div class="accordion-item border-0">
                        <h2 class="accordion-header" id="cashierHeader">
                            <button class="accordion-button nav-button secondary-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#cashierCollapse" aria-expanded="false" aria-controls="cashierCollapse">
                                <i class="fas fa-cash-register me-2"></i>
                                <span>Cashier</span>
                            </button>
                        </h2>
                        <div id="cashierCollapse" class="accordion-collapse collapse" aria-labelledby="cashierHeader">
                            <div class="accordion-body p-0 mt-2">
                                <div class="list-group">
                                    <a href="./cashier/discount.php" class="list-group-item list-group-item-action border-0 rounded-3">
                                        <i class="fa-solid fa-percent me-3 text-primary"></i>Manage Discount
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Processing Section -->
                 <div class="accordion-item border-0 mb-2" id="section-processing">
                    <div class="accordion-item border-0">
                        <h2 class="accordion-header" id="phicProcessingHeader">
                            <button class="accordion-button nav-button secondary-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#phicProcessingCollapse" aria-expanded="false" aria-controls="phicProcessingCollapse">
                                <i class="fas fa-file-medical me-2"></i>
                                <span>Processing</span>
                            </button>
                        </h2>
                        <div id="phicProcessingCollapse" class="accordion-collapse collapse" aria-labelledby="phicProcessingHeader">
                            <div class="accordion-body p-0 mt-2">
                                <div class="list-group">
                                    <a href="./processing/er/er.php" class="list-group-item list-group-item-action border-0 rounded-3 mb-1">
                                        <i class="fa-solid fa-truck-medical me-3 text-primary"></i>ER Claims
                                    </a>
                                    <a href="./processing/adm/newmem.php" class="list-group-item list-group-item-action border-0 rounded-3 mb-1">
                                        <i class="fa-solid fa-user-plus me-3 text-primary"></i>New Member
                                    </a>
                                    <a href="./processing/adm/correctiontran.php" class="list-group-item list-group-item-action border-0 rounded-3 mb-1">
                                        <i class="fa-solid fa-pen-to-square me-3 text-primary"></i>Correction
                                    </a>
                                    <a href="./processing/adm/pendingapp.php" class="list-group-item list-group-item-action border-0 rounded-3 mb-1">
                                        <i class="fa-solid fa-hospital-user me-3 text-primary"></i>Pending
                                    </a>
                                    <a href="./processing/idrec/newidreq.php" class="list-group-item list-group-item-action border-0 rounded-3">
                                        <i class="fa-solid fa-id-card me-3 text-primary"></i>New ID Request
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
               <!-- Pharmacy Section -->
                 <div class="accordion-item border-0 mb-2" id="section-pharmacy">
                            <div class="accordion-item border-0">
                                <h2 class="accordion-header" id="pharmacyHeader">
                                    <button class="accordion-button nav-button secondary-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#pharmacyCollapse" aria-expanded="false" aria-controls="pharmacyCollapse">
                                        <i class="fas fa-pills me-2"></i>
                                        <span>Pharmacy</span>
                                    </button>
                                </h2>
                                <div id="pharmacyCollapse" class="accordion-collapse collapse" aria-labelledby="pharmacyHeader">
                                    <div class="accordion-body p-0 mt-2">
                                        <div class="list-group">
                                            <a href="./pharmacy/pharmacydashboard.php" class="list-group-item list-group-item-action border-0 rounded-3 mb-1">
                                                <i class="fa-solid fa-pills me-3 text-primary"></i>Pharmacy
                                            </a>
                                            <a href="./pharmacy/payment.php" class="list-group-item list-group-item-action border-0 rounded-3 mb-1">
                                                <i class="fa-solid fa-money-bill me-3 text-primary"></i>Payment
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <style>
                            .accordion-button {
                                width: 100%;
                                padding: 1rem;
                                border: none;
                                border-radius: 8px !important;
                                font-weight: 600;
                                transition: all 0.3s ease;
                            }
                            .accordion-button:not(.collapsed) {
                                background: linear-gradient(135deg, #2196f3, #1976d2);
                                color: white;
                                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                            }
                            .accordion-button.collapsed {
                                background: #f8f9fa;
                                color: #2196f3;
                                border: 2px solid #e9ecef;
                            }
                            .accordion-button:hover {
                                transform: translateY(-2px);
                            }
                            .accordion-button::after {
                                margin-left: auto;
                            }
                            .list-group-item {
                                padding: 0.75rem 1rem;
                                transition: all 0.3s ease;
                            }
                            .list-group-item:hover {
                                background-color: #e8f5fe;
                                transform: translateX(5px);
                            }
                        </style>
                    </div>
                </div>
            </div>
    

    <div class="main-content">
        <div class="dashboard-header">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <h1 class="mb-0">
                        <i class="fa-solid fa-hospital me-3"></i>Hospital Dashboard
                    </h1>
                </div>
                <div class="d-flex align-items-center">
                    <div class="user-profile-card me-3 p-2 bg-white rounded-4 shadow-sm">
                        <div class="d-flex align-items-center">
                            <div class="avatar-circle me-2">
                                <i class="fa-solid fa-user-circle fa-2x text-primary"></i>
                            </div>
                            <div class="profile-info">
                                <small class="text-primary fw-semibold">
                                    <i class="fa-solid fa-id-badge me-1"></i>
                                    <?php echo htmlspecialchars($_SESSION['user_id']); ?>
                                </small>
                                <h6 class="fw-bold mb-0"><?php echo htmlspecialchars($_SESSION['emp_name']); ?></h6>
                                <small class="text-muted d-block"><?php echo htmlspecialchars($department); ?></small>
                            </div>
                        </div>
                    </div>
                    <a href="login.php" class="btn btn-outline-danger">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </div>
            </div>
        </div>
        <div class="accordion-item border-0 mb-2" id="section-main">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-9">
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value" id="totalAllocation">0</div>
                                <div class="stat-label">Total Allocations (<?php echo date('Y'); ?>)</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="totalUtilization">0</div>
                                <div class="stat-label">Total Utilization (<?php echo date('Y'); ?>)</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="remainingBalance">0</div>
                                <div class="stat-label">Remaining Balance</div>
                            </div>
                        </div>
                        
                        <div class="chart-container">
                            <h3 class="mb-4">Account Overview</h3>
                            <canvas id="accountChart" height="100"></canvas>
                        </div>
                    </div>
                    
                    <div class="col-lg-3">
                        <div class="side-widgets">
                            <div class="widget">
                                <div class="clock" id="clock">00:00:00</div>
                                <div class="date" id="date">Loading...</div>
                            </div>
                            <div class="widget">
                                <h5 class="mb-3">Calendar</h5>
                                <div id="calendar"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    
    <script>
        // Clock and Date
        function updateTime() {
            const now = new Date();
            document.getElementById('clock').textContent = now.toLocaleTimeString();
            document.getElementById('date').textContent = now.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }
        setInterval(updateTime, 1000);
        updateTime();

        // Initialize Calendar with modern styling
        flatpickr("#calendar", {
            inline: true,
            dateFormat: "Y-m-d",
            theme: "material_blue",
            animate: true,
            monthSelectorType: "static",
            disableMobile: true,
            showMonths: 1,
            nextArrow: '<i class="fas fa-chevron-right"></i>',
            prevArrow: '<i class="fas fa-chevron-left"></i>',
            enableTime: false,
            time_24hr: true,
            position: "auto",
            locale: {
                firstDayOfWeek: 1
            }
        });

        // Wait for data to be loaded before creating the chart
        Promise.all([
            fetch('fetch_total_allocation.php').then(response => response.json()),
            fetch('fetch_total_utilization.php').then(response => response.json())
        ])
        .then(([allocationData, utilizationData]) => {
            const totalAllocation = allocationData.totalAllocation;
            const totalUtilization = utilizationData.totalUtilization;
            const remainingBalance = totalAllocation - totalUtilization;

            // Update the stats
            document.getElementById('totalAllocation').textContent = '₱' + totalAllocation.toLocaleString('en-PH', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
            document.getElementById('totalUtilization').textContent = '₱' + totalUtilization.toLocaleString('en-PH', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
            document.getElementById('remainingBalance').textContent = '₱' + remainingBalance.toLocaleString('en-PH', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });

            // Create the chart
            const ctx = document.getElementById('accountChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Total Allocation', 'Total Utilization', 'Remaining Balance'],
                    datasets: [{
                        label: 'Amount',
                        data: [totalAllocation, totalUtilization, remainingBalance],
                        borderColor: '#4f46e5',
                        backgroundColor: 'rgba(79, 70, 229, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return '₱' + context.raw.toLocaleString('en-PH', {
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2
                                    });
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '₱' + value.toLocaleString('en-PH');
                                }
                            }
                        }
                    }
                }
            });
        })
        .catch(error => {
            console.error('Error loading data:', error);
            document.getElementById('accountChart').innerHTML = 
                '<div class="text-center text-danger">Failed to load chart data. Please try refreshing the page.</div>';
        });
    </script>
</body>
</html>
