<?php
/**
 * Document Management System
 * 
 * This script displays and manages downloadable documents.
 * Security measures implemented:
 * - Directory traversal prevention
 * - PHP file access restriction
 * - File readability checks
 */

// Configuration
$documentsDir = '../documents/';

// Validate directory access
if (!is_dir($documentsDir) || !is_readable($documentsDir)) {
    echo '<div class="alert alert-danger shadow-sm">
            <i class="fas fa-exclamation-triangle"></i> 
            Document directory is currently inaccessible. Please contact administrator.
          </div>';
    return;
}

// Fetch and filter available documents
$files = array_filter(
    scandir($documentsDir),
    fn($file) => $file !== '.' && 
                 $file !== '..' && 
                 !str_ends_with(strtolower($file), '.php')
);

?>

<style>
:root {
    --primary-gradient: linear-gradient(135deg, #2563eb, #3b82f6);
    --surface-color: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-radius: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.document-container {
    max-width: 1200px;
    margin: 3rem auto;
    padding: 0 1.5rem;
}

.page-header {
    background: var(--primary-gradient);
    border-radius: var(--border-radius);
    padding: 3.5rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: url('data:image/svg+xml,<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h100v100H0z" fill="none"/><circle cx="50" cy="50" r="40" stroke="rgba(255,255,255,0.1)" stroke-width="2" fill="none"/></svg>') repeat;
    opacity: 0.1;
}

.page-header h2 {
    font-size: 2.75rem;
    font-weight: 800;
    letter-spacing: -0.03em;
    margin-bottom: 1rem;
}

.document-card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.document-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.document-table th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.1em;
    padding: 1.25rem;
    background: #f8fafc;
    color: var(--text-secondary);
}

.document-table td {
    padding: 1.5rem;
    transition: var(--transition);
}

.document-table tr:hover td {
    background: #f1f5f9;
}

.document-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
}

.download-btn {
    background: #2563eb;
    color: white;
    border: none;
    border-radius: 12px;
    padding: 0.875rem 1.75rem;
    font-weight: 600;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.download-btn:hover {
    background: #1d4ed8;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(37, 99, 235, 0.2);
}

.guidelines-card {
    background: #f8fafc;
    border-radius: var(--border-radius);
    margin-top: 2rem;
    padding: 2rem;
}

.guidelines-card .card-title {
    color: var(--text-primary);
    font-weight: 700;
    font-size: 1.25rem;
    margin-bottom: 2rem;
}

.guideline-item {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 1rem;
    padding: 1rem;
    background: var(--surface-color);
    border-radius: 12px;
    margin-bottom: 1rem;
    transition: var(--transition);
}

.guideline-item:hover {
    transform: translateX(8px);
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

.guideline-item i {
    color: #2563eb;
    font-size: 1.25rem;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(37, 99, 235, 0.1);
    border-radius: 8px;
}

.empty-state {
    padding: 6rem 2rem;
    text-align: center;
    color: var(--text-secondary);
}

.file-icon {
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(37, 99, 235, 0.1);
    border-radius: 12px;
    color: #2563eb;
    margin-right: 1rem;
    transition: var(--transition);
}

tr:hover .file-icon {
    transform: scale(1.1);
}
</style>

<?php
// PHP code remains the same...
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hospital Documents Repository</title>
</head>
<body>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<div class="document-container">
    <!-- Page Header -->
    <div class="page-header text-white" style="background: linear-gradient(135deg, #15803d, #16a34a);">
        <h2>
            <i class="fas fa-leaf me-3"></i>
            Hospital Documents Repository
        </h2>
        <p class="mb-0 fs-5">
            Access and download medical documentation securely
        </p>
    </div>

    <!-- Document listing card -->
    <div class="document-grid">
        <?php if (!empty($files)): ?>
            <?php foreach ($files as $file): ?>
                <?php $filePath = $documentsDir . $file; ?>
                <div class="document-item">
                    <div class="document-icon">
                        <i class="fas fa-file-medical fa-2x"></i>
                    </div>
                    <div class="document-info">
                        <h3 class="document-title"><?= htmlspecialchars($file) ?></h3>
                        <div class="document-actions">
                            <?php if (file_exists($filePath) && is_readable($filePath)): ?>
                                <a href="<?= $documentsDir . urlencode($file) ?>" 
                                   class="btn-download" 
                                   download>
                                    <i class="fas fa-download"></i>
                                    Download Document
                                </a>
                            <?php else: ?>
                                <div class="access-restricted">
                                    <i class="fas fa-lock"></i>
                                    Access Restricted
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="no-documents">
                <i class="fas fa-folder-open"></i>
                <h3>No Documents Available</h3>
                <p>Check back later for updated documentation</p>
            </div>
        <?php endif; ?>
    </div>

    <style>
    .document-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
        padding: 2rem;
    }

    .document-item {
        background: white;
        border-radius: 20px;
        padding: 1.5rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }

    .document-item:hover {
        transform: translateY(-5px);
    }

    .document-icon {
        background: #e0ffd9;
        color: #15803d;
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
    }

    .document-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: #166534;
    }

    .btn-download {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: #16a34a;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 500;
        transition: background 0.3s ease;
    }

    .btn-download:hover {
        background: #15803d;
    }

    .access-restricted {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        color: #b91c1c;
        font-weight: 500;
    }

    .no-documents {
        grid-column: 1 / -1;
        text-align: center;
        padding: 4rem 2rem;
        color: #166534;
    }

    .no-documents i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #15803d;
    }

    .no-documents h3 {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
        color: #166534;
    }
    </style>

    <!-- Usage Instructions -->
    <div class="guidelines-card">
        <h5 class="card-title">
            <i class="fas fa-info-circle me-2"></i>
            Access Guidelines
        </h5>
        <div class="guidelines-content">
            <div class="guideline-item">
                <i class="fas fa-shield-alt"></i>
                <span>
                    Enterprise-grade security and document verification
                </span>
            </div>
            <div class="guideline-item">
                <i class="fas fa-bolt"></i>
                <span>
                    Instant download with smart caching
                </span>
            </div>
            <div class="guideline-item">
                <i class="fas fa-clock"></i>
                <span>
                    Round-the-clock technical assistance
                </span>
            </div>
        </div>
    </div>
</div>
