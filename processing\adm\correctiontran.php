<?php
require_once '../../database.php';

// INSERT
if (isset($_POST['submit'])) {
    $sql = "INSERT INTO memcorrectiontracker (phicnum, memlast, memfirst, memmid, memext, membday, 
            memgender, updatefrom, updateto, dsubmitted, trasmittalnum, status) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $_POST['phicnum'], $_POST['memlast'], $_POST['memfirst'], $_POST['memmid'], $_POST['memext'],
        $_POST['membday'], $_POST['memgender'], $_POST['updatefrom'], $_POST['updateto'],
        $_POST['dsubmitted'], $_POST['trasmittalnum'], $_POST['status']
    ]);
}

// UPDATE
if (isset($_POST['update'])) {
    // Handle empty date fields
    $membday = !empty($_POST['membday']) ? $_POST['membday'] : null;
    $dsubmitted = !empty($_POST['dsubmitted']) ? $_POST['dsubmitted'] : null;
    
    $sql = "UPDATE memcorrectiontracker SET 
            phicnum=?, memlast=?, memfirst=?, memmid=?, memext=?, membday=?,
            memgender=?, updatefrom=?, updateto=?, dsubmitted=?, trasmittalnum=?, status=?
            WHERE id=?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $_POST['phicnum'], $_POST['memlast'], $_POST['memfirst'], $_POST['memmid'], $_POST['memext'],
        $membday, $_POST['memgender'], $_POST['updatefrom'], $_POST['updateto'], $dsubmitted,
        $_POST['trasmittalnum'], $_POST['status'], $_POST['id']
    ]);
}

// SEARCH
$search = isset($_GET['search']) ? $_GET['search'] : '';
$sql = "SELECT * FROM memcorrectiontracker WHERE 
        phicnum LIKE ? OR memlast LIKE ? OR memfirst LIKE ?";
$stmt = $conn->prepare($sql);
$searchTerm = "%$search%";
$stmt->execute([$searchTerm, $searchTerm, $searchTerm]);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Correction Tracker</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 20px auto; }
        .table-responsive { margin-top: 20px; }
        .modal-lg { max-width: 800px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card shadow-lg rounded-4 border-0">
            <div class="card-header bg-gradient bg-primary bg-opacity-25 border-0 py-4 d-flex justify-content-between align-items-center">
                <h3 class="mb-0 text-primary">
                    <i class="fas fa-edit me-2"></i>Member Correction Tracker
                </h3>
                <div class="d-flex align-items-center">
                    <button type="button" class="btn btn-primary btn-lg rounded-3 shadow-sm" data-bs-toggle="modal" data-bs-target="#memberModal">
                        <i class="fas fa-plus-circle me-2"></i>New Correction
                    </button>
                    <div class="ms-2">
                        <a href="../../index.php" class="btn btn-outline-primary btn-lg rounded-3 shadow-sm">
                            <i class="fas fa-home me-2"></i>Homepage
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                <form method="GET" class="mb-4">
                    <div class="input-group input-group-lg shadow-sm rounded-3 overflow-hidden">
                        <span class="input-group-text border-0 bg-light">
                            <i class="fas fa-search text-primary"></i>
                        </span>
                        <input type="text" class="form-control border-0 bg-light py-3" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Search by PHIC number or name...">
                        <button class="btn btn-primary px-4" type="submit">
                            <i class="fas fa-search-plus me-2"></i>Search
                        </button>
                    </div>
                </form>

                <div class="table-responsive rounded-3 border">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="bg-light">
                            <tr>
                                <th><i class="fas fa-id-card me-2"></i>PHIC Number</th>
                                <th><i class="fas fa-user me-2"></i>Member Name</th>
                                <th><i class="fas fa-exchange-alt me-2"></i>Update Details</th>
                                <th><i class="fas fa-calendar-alt me-2"></i>Date Submitted</th>
                                <th><i class="fas fa-file-alt me-2"></i>Transmittal</th>
                                <th><i class="fas fa-chart-line me-2"></i>Status</th>
                                <th><i class="fas fa-cog me-2"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($results as $row): ?>
                            <tr>
                                <td class="fw-bold text-primary"><?php echo htmlspecialchars($row['phicnum']); ?></td>
                                <td><?php echo htmlspecialchars($row['memlast'] . ', ' . $row['memfirst'] . ' ' . $row['memmid'] . ' ' . $row['memext']); ?></td>
                                <td>
                                    <small class="d-block text-muted">
                                        <i class="fas fa-arrow-right me-1"></i>From: <?php echo htmlspecialchars($row['updatefrom']); ?>
                                    </small>
                                    <small class="d-block text-primary">
                                        <i class="fas fa-arrow-right me-1"></i>To: <?php echo htmlspecialchars($row['updateto']); ?>
                                    </small>
                                </td>
                                <td><?php echo htmlspecialchars($row['dsubmitted']); ?></td>
                                <td><?php echo htmlspecialchars($row['trasmittalnum']); ?></td>
                                <td>
                                    <span class="badge rounded-pill bg-<?php 
                                        echo $row['status'] == 'Pending' ? 'secondary' : 
                                            ($row['status'] == 'Submitted' ? 'warning' : 
                                            ($row['status'] == 'Completed' ? 'success' : 'secondary')); 
                                    ?> px-3 py-2">
                                        <i class="fas fa-<?php 
                                            echo $row['status'] == 'Pending' ? 'hourglass' : 
                                                ($row['status'] == 'Submitted' ? 'paper-plane' : 
                                                ($row['status'] == 'Completed' ? 'check' : 'question')); 
                                        ?> me-1"></i>
                                        <?php echo htmlspecialchars($row['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-info btn-sm rounded-3" onclick="editMember(<?php echo htmlspecialchars(json_encode($row)); ?>)">
                                        <i class="fas fa-pen-to-square me-1"></i> Edit
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Add/Edit -->
    <div class="modal fade" id="memberModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="modalTitle">
                        <i class="fas fa-plus-circle me-2"></i>New Correction
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="memberForm" method="POST" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <input type="hidden" name="id" id="member_id">
                        
                        <!-- Member Information -->
                        <div class="card mb-4 border-primary">
                            <div class="card-header bg-primary bg-opacity-10">
                                <i class="fas fa-user me-2"></i>Member Information
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-12">
                                        <label class="form-label">
                                            <i class="fas fa-id-card me-2"></i>PHIC Number
                                        </label>
                                        <input type="text" class="form-control" name="phicnum" id="phicnum" required>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Last Name</label>
                                        <input type="text" class="form-control" name="memlast" id="memlast" required>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">First Name</label>
                                        <input type="text" class="form-control" name="memfirst" id="memfirst" required>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Middle Name</label>
                                        <input type="text" class="form-control" name="memmid" id="memmid">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Extension</label>
                                        <input type="text" class="form-control" name="memext" id="memext">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">
                                            <i class="fas fa-calendar me-2"></i>Birth Date
                                        </label>
                                        <input type="date" class="form-control" name="membday" id="membday">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">
                                            <i class="fas fa-venus-mars me-2"></i>Gender
                                        </label>
                                        <select class="form-select" name="memgender" id="memgender" required>
                                            <option value="">Select Gender</option>
                                            <option value="Male">Male</option>
                                            <option value="Female">Female</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Correction Details -->
                        <div class="card mb-4 border-info">
                            <div class="card-header bg-info bg-opacity-10">
                                <i class="fas fa-exchange-alt me-2"></i>Correction Details
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">Update From</label>
                                        <textarea class="form-control" name="updatefrom" id="updatefrom" rows="3" required></textarea>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Update To</label>
                                        <textarea class="form-control" name="updateto" id="updateto" rows="3" required></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Transaction Details -->
                        <div class="card mb-4 border-success">
                            <div class="card-header bg-success bg-opacity-10">
                                <i class="fas fa-file-alt me-2"></i>Transaction Details
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">
                                            <i class="fas fa-calendar-alt me-2"></i>Date Submitted
                                        </label>
                                        <input type="date" class="form-control" name="dsubmitted" id="dsubmitted">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">
                                            <i class="fas fa-file-invoice me-2"></i>Transmittal Number
                                        </label>
                                        <input type="text" class="form-control" name="trasmittalnum" id="trasmittalnum">
                                    </div>
                                    <div class="col-md-12">
                                        <label class="form-label">
                                            <i class="fas fa-tasks me-2"></i>Status
                                        </label>
                                        <select class="form-select" name="status" id="status" required>
                                            <option value="Pending" class="text-secondary">Pending</option>
                                            <option value="Submitted" class="text-warning">Submitted</option>
                                            <option value="Completed" class="text-success">Completed</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Close
                        </button>
                        <button type="submit" class="btn btn-primary" id="submitBtn" name="submit">
                            <i class="fas fa-save me-2"></i>Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editMember(data) {
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-edit me-2"></i>Edit Correction';
            document.getElementById('member_id').value = data.id;
            
            // Fill form fields
            document.getElementById('phicnum').value = data.phicnum;
            document.getElementById('memlast').value = data.memlast;
            document.getElementById('memfirst').value = data.memfirst;
            document.getElementById('memmid').value = data.memmid;
            document.getElementById('memext').value = data.memext;
            document.getElementById('membday').value = data.membday;
            document.getElementById('memgender').value = data.memgender;
            document.getElementById('updatefrom').value = data.updatefrom;
            document.getElementById('updateto').value = data.updateto;
            document.getElementById('dsubmitted').value = data.dsubmitted;
            document.getElementById('trasmittalnum').value = data.trasmittalnum;
            document.getElementById('status').value = data.status;
            
            // Change submit button
            document.getElementById('submitBtn').name = 'update';
            document.getElementById('submitBtn').innerHTML = '<i class="fas fa-save me-2"></i>Update Changes';
            
            // Show modal
            new bootstrap.Modal(document.getElementById('memberModal')).show();
        }

        // Reset form when modal is closed
        document.getElementById('memberModal').addEventListener('hidden.bs.modal', function () {
            document.getElementById('memberForm').reset();
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-plus-circle me-2"></i>New Correction';
            document.getElementById('submitBtn').name = 'submit';
            document.getElementById('submitBtn').innerHTML = '<i class="fas fa-save me-2"></i>Save Changes';
        });
    </script>
</body>
</html>
