<?php
require_once '../database.php';
require_once '../fpdf/fpdf.php';

// Get selected month and year or default to current month
$selectedMonth = isset($_GET['month']) ? $_GET['month'] : date('Y-m');
$startDate = $selectedMonth . '-01';
$endDate = date('Y-m-t', strtotime($startDate));

// Get previous month
$prevMonth = date('Y-m', strtotime($startDate . ' -1 month'));
$prevMonthStart = $prevMonth . '-01';
$prevMonthEnd = date('Y-m-t', strtotime($prevMonthStart));

// Enhanced SQL with proper aggregation and better performance (same as working mur.php)
$sql = "
WITH ItemsWithActivity AS (
    -- Get all items that have any activity (stock, transactions, or returns)
    SELECT DISTINCT i.itemid, i.generaldescription, i.unitmeasure
    FROM items i
    WHERE EXISTS (
        SELECT 1 FROM pharmacy_stock_ledger psl WHERE psl.itemid = i.itemid
    ) OR EXISTS (
        SELECT 1 FROM pharmatransaction_details ptd
        JOIN pharmatransactions pt ON ptd.transaction_id = pt.transaction_id
        WHERE ptd.itemid = i.itemid AND pt.transaction_date BETWEEN ? AND ?
    ) OR EXISTS (
        SELECT 1 FROM return_to_supplier r WHERE r.itemid = i.itemid
    )
),
BeginningBalance AS (
    -- Calculate beginning balance (everything before start date)
    SELECT
        iwa.itemid,
        (COALESCE(received.total_received, 0) -
         COALESCE(dispensed.total_dispensed, 0) -
         COALESCE(returned.total_returned, 0)) as beginning_qty
    FROM ItemsWithActivity iwa
    LEFT JOIN (
        SELECT itemid, SUM(qty_received) as total_received
        FROM pharmacy_stock_ledger
        WHERE date_received < ?
        GROUP BY itemid
    ) received ON iwa.itemid = received.itemid
    LEFT JOIN (
        SELECT ptd.itemid, SUM(ptd.quantity) as total_dispensed
        FROM pharmatransaction_details ptd
        JOIN pharmatransactions pt ON ptd.transaction_id = pt.transaction_id
        WHERE pt.transaction_date < ?
        GROUP BY ptd.itemid
    ) dispensed ON iwa.itemid = dispensed.itemid
    LEFT JOIN (
        SELECT itemid, SUM(quantity_returned) as total_returned
        FROM return_to_supplier
        WHERE date_returned < ?
        GROUP BY itemid
    ) returned ON iwa.itemid = returned.itemid
),
MonthlyReceived AS (
    -- All deliveries/receipts for the month aggregated by itemid
    SELECT
        iwa.itemid,
        COALESCE(SUM(psl.qty_received), 0) as received_qty,
        COUNT(DISTINCT psl.date_received) as delivery_count,
        GROUP_CONCAT(DISTINCT DATE_FORMAT(psl.date_received, '%m/%d') ORDER BY psl.date_received SEPARATOR ', ') as delivery_dates,
        GROUP_CONCAT(DISTINCT psl.supplier ORDER BY psl.supplier SEPARATOR ', ') as suppliers
    FROM ItemsWithActivity iwa
    LEFT JOIN pharmacy_stock_ledger psl ON iwa.itemid = psl.itemid
        AND psl.date_received BETWEEN ? AND ?
    GROUP BY iwa.itemid
),
MonthlyDispensed AS (
    -- All dispensing/utilization for the month aggregated by itemid
    SELECT
        iwa.itemid,
        COALESCE(SUM(ptd.quantity), 0) as dispensed_qty,
        COUNT(DISTINCT pt.transaction_id) as transaction_count,
        COUNT(DISTINCT DATE(pt.transaction_date)) as active_days,
        MIN(DATE(pt.transaction_date)) as first_dispensed,
        MAX(DATE(pt.transaction_date)) as last_dispensed
    FROM ItemsWithActivity iwa
    LEFT JOIN pharmatransaction_details ptd ON iwa.itemid = ptd.itemid
    LEFT JOIN pharmatransactions pt ON ptd.transaction_id = pt.transaction_id
        AND pt.transaction_date BETWEEN ? AND ?
    GROUP BY iwa.itemid
),
MonthlyReturned AS (
    -- All returns for the month aggregated by itemid
    SELECT
        iwa.itemid,
        COALESCE(SUM(r.quantity_returned), 0) as returned_qty,
        COUNT(DISTINCT r.return_id) as return_count,
        GROUP_CONCAT(DISTINCT r.return_reason ORDER BY r.date_returned SEPARATOR '; ') as return_reasons
    FROM ItemsWithActivity iwa
    LEFT JOIN return_to_supplier r ON iwa.itemid = r.itemid
        AND r.date_returned BETWEEN ? AND ?
    GROUP BY iwa.itemid
)
SELECT
    iwa.itemid,
    iwa.generaldescription as item_name,
    iwa.unitmeasure,
    COALESCE(stock_info.item_no, 0) as item_no,
    COALESCE(stock_info.cris_no, '') as cris_no,
    COALESCE(stock_info.avg_unit_cost, 0) as unit_cost,

    -- Beginning balance data
    COALESCE(bb.beginning_qty, 0) as beginning_qty,

    -- Monthly received data with details
    COALESCE(mr.received_qty, 0) as received_qty,
    COALESCE(mr.delivery_count, 0) as delivery_count,
    mr.delivery_dates,
    mr.suppliers,

    -- Monthly dispensed data with analytics
    COALESCE(md_disp.dispensed_qty, 0) as dispensed_qty,
    COALESCE(md_disp.transaction_count, 0) as transaction_count,
    COALESCE(md_disp.active_days, 0) as active_days,
    md_disp.first_dispensed,
    md_disp.last_dispensed,

    -- Monthly returned data
    COALESCE(mr_ret.returned_qty, 0) as returned_qty,
    COALESCE(mr_ret.return_count, 0) as return_count,
    mr_ret.return_reasons,

    -- Calculated ending balance
    (COALESCE(bb.beginning_qty, 0) +
     COALESCE(mr.received_qty, 0) -
     COALESCE(md_disp.dispensed_qty, 0) -
     COALESCE(mr_ret.returned_qty, 0)) as ending_qty

FROM ItemsWithActivity iwa
LEFT JOIN (
    SELECT
        itemid,
        MIN(item_no) as item_no,
        GROUP_CONCAT(DISTINCT cris_no SEPARATOR ', ') as cris_no,
        AVG(unit_cost) as avg_unit_cost
    FROM pharmacy_stock_ledger
    GROUP BY itemid
) stock_info ON iwa.itemid = stock_info.itemid
LEFT JOIN BeginningBalance bb ON iwa.itemid = bb.itemid
LEFT JOIN MonthlyReceived mr ON iwa.itemid = mr.itemid
LEFT JOIN MonthlyDispensed md_disp ON iwa.itemid = md_disp.itemid
LEFT JOIN MonthlyReturned mr_ret ON iwa.itemid = mr_ret.itemid
WHERE (bb.beginning_qty > 0 OR mr.received_qty > 0 OR md_disp.dispensed_qty > 0 OR mr_ret.returned_qty > 0)
ORDER BY stock_info.item_no ASC, iwa.generaldescription ASC
";

// Prepare and execute the query
$stmt = $conn->prepare($sql);
try {
    $params = [
        $startDate, $endDate, // ItemsWithActivity - transaction date range check
        $startDate,         // BeginningBalance - psl.date_received < ?
        $startDate,         // BeginningBalance - pt.transaction_date < ?
        $startDate,         // BeginningBalance - r.date_returned < ?
        $startDate, $endDate, // MonthlyReceived - date range
        $startDate, $endDate, // MonthlyDispensed - date range
        $startDate, $endDate  // MonthlyReturned - date range
    ];
    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Remove duplicates if any exist (comprehensive deduplication)
    $uniqueResults = [];
    $seenItems = [];

    foreach ($results as $row) {
        // Create a unique key based on itemid and item name to catch any duplicates
        $uniqueKey = $row['itemid'] . '|' . $row['item_name'];

        if (!isset($seenItems[$uniqueKey])) {
            $uniqueResults[] = $row;
            $seenItems[$uniqueKey] = true;
        }
    }
    unset($row); // 🔥 This is crucial to prevent reference issues

    $results = $uniqueResults;
} catch (PDOException $e) {
    error_log("SQL Error in MUR Report: " . $e->getMessage());
    die("An error occurred while generating the report. Please check the error log.");
}

// Calculate values and enhanced metrics (same as working mur.php)
foreach ($results as &$row) {
    // Calculate monetary values
    $row['beginning_value'] = $row['beginning_qty'] * $row['unit_cost'];
    $row['received_value'] = $row['received_qty'] * $row['unit_cost'];
    $row['dispensed_value'] = $row['dispensed_qty'] * $row['unit_cost'];
    $row['returned_value'] = $row['returned_qty'] * $row['unit_cost'];
    $row['ending_value'] = $row['ending_qty'] * $row['unit_cost'];

    // Calculate utilization metrics
    $total_available = $row['beginning_qty'] + $row['received_qty'];
    $row['utilization_rate'] = $total_available > 0 ?
        round(($row['dispensed_qty'] / $total_available) * 100, 2) : 0;

    // Calculate turnover rate
    $row['turnover_rate'] = $row['received_qty'] > 0 ?
        round($row['dispensed_qty'] / $row['received_qty'], 2) : 0;

    // Days of supply calculation
    $days_in_month = date('t', strtotime($startDate));
    $daily_usage = $row['active_days'] > 0 ? $row['dispensed_qty'] / $row['active_days'] : 0;
    $row['days_supply'] = $daily_usage > 0 ? round($row['ending_qty'] / $daily_usage, 0) : 'N/A';

    // Stock status assessment
    if ($row['ending_qty'] <= 0) {
        $row['stock_status'] = 'Out of Stock';
        $row['status_class'] = 'danger';
    } elseif ($row['days_supply'] !== 'N/A' && $row['days_supply'] < 7) {
        $row['stock_status'] = 'Critical Low';
        $row['status_class'] = 'danger';
    } elseif ($row['days_supply'] !== 'N/A' && $row['days_supply'] < 14) {
        $row['stock_status'] = 'Low Stock';
        $row['status_class'] = 'warning';
    } else {
        $row['stock_status'] = 'Adequate';
        $row['status_class'] = 'success';
    }

    // Store all calculations in balances array for backward compatibility
    $row['balances'] = [
        'beginning' => ['qty' => $row['beginning_qty'], 'unit_cost' => $row['unit_cost'], 'total' => $row['beginning_value']],
        'received' => ['qty' => $row['received_qty'], 'unit_cost' => $row['unit_cost'], 'total' => $row['received_value']],
        'dispensed' => ['qty' => $row['dispensed_qty'], 'unit_cost' => $row['unit_cost'], 'total' => $row['dispensed_value']],
        'returned' => ['qty' => $row['returned_qty'], 'unit_cost' => $row['unit_cost'], 'total' => $row['returned_value']],
        'ending' => ['qty' => $row['ending_qty'], 'unit_cost' => $row['unit_cost'], 'total' => $row['ending_value']]
    ];
}
unset($row); // 🔥 This is crucial to prevent reference issues


class PDF extends FPDF {
    function Header() {
        // Add logos
        $this->Image('../images/pgns.png', 90, 10, 25);
        $this->Image('../images/bdh.png', 215, 10, 25);

        // Add title and header information
        $this->SetFont('Arial', '', 9);
        $this->Cell(0, 4, 'Republic of the Philippines', 0, 1, 'C');
        $this->Cell(0, 4, 'PROVINCIAL GOVERNMENT OF NORTHERN SAMAR', 0, 1, 'C');
        $this->Cell(0, 4, 'PROVINCIAL HEALTH OFFICE', 0, 1, 'C');
        $this->Cell(0, 4, 'PRO-HEALTH CONSIGNMENT MANAGEMENT COMMITTEE (CMC)', 0, 1, 'C');
        
         $this->Ln(5);
        $this->SetFont('Arial', 'B', 12);
        $this->Cell(0, 4, 'MONTHLY UTILIZATIONS REPORT', 0, 1, 'C');
          $this->SetFont('Arial', 'B', 8);
        $this->Cell(0, 4, 'DRUGS, MEDICINES AND MEDICAL SUPPLIES UNDER CONSIGNMENT', 0, 1, 'C');
        $this->Cell(0, 4, 'BIRI DISTRICT HOSPITAL', 0, 1, 'C');
        
        $this->SetFont('Arial', '', 10);
        $this->Cell(0, 6, 'For the month of ' . date('F Y', strtotime($GLOBALS['selectedMonth'])), 0, 1, 'C');
        $this->Ln(5);
    }
}

// safe_balance function removed - no longer needed with new data structure

// Balances are already calculated in the previous section


// Initialize PDF
$pdf = new PDF('L', 'mm', array(215.9, 330.2));
$pdf->AddPage();
$pdf->SetMargins(10, 10, 10);
$pdf->SetAutoPageBreak(true, 10);

// Header colors
$headerBlue = array(200, 220, 255);
$headerPink = array(255, 200, 220);

// Define data keys and their printable labels
$groupKeys = ['beginning', 'received', 'dispensed', 'returned', 'ending'];
$groupLabels = [
    'beginning' => 'Beginning Balance',
    'received' => 'Replenishments',
    'dispensed' => 'Utilizations',
    'returned' => 'Returns to Warehouse',
    'ending' => 'Ending Balance'
];

// Table Headers
$pdf->SetFillColor(...$headerBlue);
$pdf->SetFont('Arial', 'B', 9);
$pdf->Cell(10, 10, 'No.', 1, 0, 'C', true);
$pdf->Cell(60, 10, 'Item Name (CRIS No.)', 1, 0, 'C', true);
$pdf->Cell(15, 10, 'Unit', 1, 0, 'C', true);

$pdf->SetFillColor(...$headerPink);
foreach ($groupKeys as $key) {
    $pdf->Cell(45, 5, $groupLabels[$key], 1, 0, 'C', true);
}
$pdf->Ln();

// Subheaders
$pdf->SetFillColor(...$headerBlue);
$pdf->Cell(85, 5, '', 0, 0);
for ($i = 0; $i < count($groupKeys); $i++) {
    $pdf->Cell(15, 5, 'Qty', 1, 0, 'C', true);
    $pdf->Cell(15, 5, 'Unit Cost', 1, 0, 'C', true);
    $pdf->Cell(15, 5, 'Total', 1, 0, 'C', true);
}
$pdf->Ln();

// Initialize grand totals
$grand_totals = [];
foreach ($groupKeys as $key) {
    $grand_totals[$key] = ['qty' => 0, 'cost' => 0, 'total' => 0];
}

// Data Rows
$counter = 1;
$rowColor = [245, 245, 245];

foreach ($results as $row) {
    // Handle page break
    if ($pdf->GetY() > 180) {
        $pdf->AddPage();

        // Reprint headers
        $pdf->SetFillColor(...$headerBlue);
        $pdf->SetFont('Arial', 'B', 9);
        $pdf->Cell(10, 10, 'No.', 1, 0, 'C', true);
        $pdf->Cell(60, 10, 'Item Name (CRIS No.)', 1, 0, 'C', true);
        $pdf->Cell(15, 10, 'Unit', 1, 0, 'C', true);

        $pdf->SetFillColor(...$headerPink);
        foreach ($groupKeys as $key) {
            $pdf->Cell(45, 5, $groupLabels[$key], 1, 0, 'C', true);
        }
        $pdf->Ln();

        // Subheaders
        $pdf->SetFillColor(...$headerBlue);
        $pdf->Cell(85, 5, '', 0, 0);
        for ($i = 0; $i < count($groupKeys); $i++) {
            $pdf->Cell(15, 5, 'Qty', 1, 0, 'C', true);
            $pdf->Cell(15, 5, 'Unit Cost', 1, 0, 'C', true);
            $pdf->Cell(15, 5, 'Total', 1, 0, 'C', true);
        }
        $pdf->Ln();
    }

    // Row output
    $pdf->SetFillColor(...$rowColor);
    $pdf->SetFont('Arial', '', 8);

    $pdf->Cell(10, 5, $row['item_no'], 1, 0, 'C', ($counter % 2 == 0));
    $pdf->Cell(60, 5, $row['item_name'] . ' (' . $row['cris_no'] . ')', 1, 0, 'L', ($counter % 2 == 0));
    $pdf->Cell(15, 5, $row['unitmeasure'], 1, 0, 'C', ($counter % 2 == 0));

  $groupKeys = ['beginning', 'received', 'dispensed', 'returned', 'ending'];

foreach ($groupKeys as $group) {
    $balance = $row['balances'][$group] ?? ['qty' => 0, 'unit_cost' => 0, 'total' => 0];

    // 🛡 Ensure valid array structure and map unit_cost to cost for PDF compatibility
    if (is_array($balance)) {
        // Map unit_cost to cost for backward compatibility
        $balance['cost'] = $balance['unit_cost'] ?? $balance['cost'] ?? 0;
    } else {
        $balance = ['qty' => 0, 'cost' => 0, 'total' => 0];
    }

    // Recalculate ending balance explicitly if this is the "ending" group
    if ($group === 'ending') {
        $beginning_qty = $row['balances']['beginning']['qty'] ?? 0;
        $received_qty  = $row['balances']['received']['qty'] ?? 0;
        $dispensed_qty = $row['balances']['dispensed']['qty'] ?? 0;
        $returned_qty  = $row['balances']['returned']['qty'] ?? 0;

        $ending_qty = $beginning_qty + $received_qty - $dispensed_qty - $returned_qty;
        $ending_cost = $row['unit_cost'] ?? 0; // ✅ Using unit_cost from new structure
        $ending_total = $ending_qty * $ending_cost;

        $balance = [
            'qty' => $ending_qty,
            'cost' => $ending_cost,
            'total' => $ending_total
        ];
    }

    // Ensure all values are numeric to prevent null warnings
    $balance['qty'] = floatval($balance['qty'] ?? 0);
    $balance['cost'] = floatval($balance['cost'] ?? 0);
    $balance['total'] = floatval($balance['total'] ?? 0);

    // Accumulate grand totals safely
    $grand_totals[$group]['qty'] += $balance['qty'];
    $grand_totals[$group]['cost'] += $balance['cost'];
    $grand_totals[$group]['total'] += $balance['total'];

    // Output to PDF
    $pdf->Cell(15, 5, number_format($balance['qty'], 2), 1, 0, 'R', ($counter % 2 == 0));
    $pdf->Cell(15, 5, number_format($balance['cost'], 2), 1, 0, 'R', ($counter % 2 == 0));
    $pdf->Cell(15, 5, number_format($balance['total'], 2), 1, 0, 'R', ($counter % 2 == 0));
}


    $pdf->Ln();
    $counter++;
}

// Grand Totals Row
$pdf->SetFillColor(220, 220, 220);
$pdf->SetFont('Arial', 'B', 9);
$pdf->Cell(10, 6, '', 1, 0, 'C', true);
$pdf->Cell(60, 6, 'GRAND TOTAL', 1, 0, 'C', true);
$pdf->Cell(15, 6, '', 1, 0, 'C', true);

foreach ($groupKeys as $group) {
    $pdf->Cell(15, 6, number_format($grand_totals[$group]['qty'], 2), 1, 0, 'R', true);
    $pdf->Cell(15, 6, '', 1, 0, 'R', true); // You can skip cost if needed
    $pdf->Cell(15, 6, number_format($grand_totals[$group]['total'], 2), 1, 0, 'R', true);
}
$pdf->Ln();

// Signatories
$pdf->Ln(20);
$pdf->SetFont('Arial', '', 10);
$pdf->Cell(110, 6, 'Prepared by:', 0, 0, 'L');
$pdf->Cell(110, 6, 'Reviewed by:', 0, 0, 'L');
$pdf->Cell(110, 6, 'Noted by:', 0, 1, 'L');

$pdf->Ln(15);
$pdf->SetFont('Arial', 'B', 10);
$pdf->Cell(110, 5, 'RONNIE B. CELIS', 0, 0, 'L');
$pdf->Cell(110, 5, 'CHARLIE JANE LEBECO', 0, 0, 'L');
$pdf->Cell(110, 5, 'LUCILLE G. ROMINES, MD, FPCP', 0, 1, 'L');

$pdf->SetFont('Arial', 'I', 10);
$pdf->Cell(110, 5, 'BDH, Pharmacist', 0, 0, 'L');
$pdf->Cell(110, 5, 'CMC, Accountant', 0, 0, 'L');
$pdf->Cell(110, 5, 'CHIEF OF HOSPITAL', 0, 1, 'L');

// Output PDF
$pdf->Output('I', 'Monthly_Utilization_Report.pdf');
