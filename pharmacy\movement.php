<?php
require_once '../database.php';

// Get selected month or default to current month
$selectedMonth = isset($_GET['month']) ? $_GET['month'] . '-01' : date('Y-m-01');
$selectedMonthEnd = date('Y-m-t', strtotime($selectedMonth));

// Enhanced query to get item movement categories with frequency analysis
$sql = "WITH ItemMovement AS (
    SELECT
        i.itemid,
        i.generaldescription,
        i.unitmeasure,
        pc.categorydesc,
        SUM(ptd.quantity) as total_quantity,
        COUNT(DISTINCT pt.transaction_id) as transaction_count,
        COUNT(DISTINCT DATE(pt.transaction_date)) as active_days,
        AVG(ptd.quantity) as avg_quantity_per_transaction,
        DATEDIFF(?, ?) + 1 as total_days_in_period,
        -- Calculate frequency metrics
        COUNT(DISTINCT pt.transaction_id) / (DATEDIFF(?, ?) + 1) as transactions_per_day,
        COUNT(DISTINCT DATE(pt.transaction_date)) / (DATEDIFF(?, ?) + 1) as usage_consistency,
        SUM(ptd.quantity) / COUNT(DISTINCT DATE(pt.transaction_date)) as avg_daily_usage,
        -- Calculate weekly frequency
        COUNT(DISTINCT pt.transaction_id) / CEIL((DATEDIFF(?, ?) + 1) / 7) as transactions_per_week,
        COUNT(DISTINCT DATE(pt.transaction_date)) / CEIL((DATEDIFF(?, ?) + 1) / 7) as active_days_per_week
    FROM items i
    LEFT JOIN pharmatransaction_details ptd ON i.itemid = ptd.itemid
    LEFT JOIN pharmatransactions pt ON ptd.transaction_id = pt.transaction_id
    LEFT JOIN pharmacategory pc ON i.category = pc.categoryid
    WHERE pt.transaction_date BETWEEN ? AND ?
    GROUP BY i.itemid, i.generaldescription, i.unitmeasure, pc.categorydesc
    HAVING COUNT(DISTINCT pt.transaction_id) > 0
),
ClassifiedMovement AS (
    SELECT *,
        CASE
            -- Fast Moving: High frequency (≥3 times/week) OR high volume (≥100 units) OR high consistency (≥40% days active)
            WHEN transactions_per_week >= 3 OR total_quantity >= 100 OR usage_consistency >= 0.4 THEN 'Fast Moving'
            -- Moderate Moving: Medium frequency (1-3 times/week) OR medium volume (25-99 units) OR medium consistency (15-40% days active)
            WHEN (transactions_per_week >= 1 AND transactions_per_week < 3) OR
                 (total_quantity >= 25 AND total_quantity < 100) OR
                 (usage_consistency >= 0.15 AND usage_consistency < 0.4) THEN 'Moderate Moving'
            -- Slow Moving: Low frequency (<1 time/week) AND low volume (<25 units) AND low consistency (<15% days active)
            ELSE 'Slow Moving'
        END as movement_category
    FROM ItemMovement
)
SELECT * FROM ClassifiedMovement ORDER BY transactions_per_week DESC, total_quantity DESC";

$stmt = $conn->prepare($sql);
// Execute with all required parameters for the enhanced query
$stmt->execute([
    $selectedMonthEnd, $selectedMonth,    // For DATEDIFF calculations
    $selectedMonthEnd, $selectedMonth,    // For transactions_per_day calculation
    $selectedMonthEnd, $selectedMonth,    // For usage_consistency calculation
    $selectedMonthEnd, $selectedMonth,    // For transactions_per_week calculation
    $selectedMonthEnd, $selectedMonth,    // For active_days_per_week calculation
    $selectedMonth, $selectedMonthEnd     // For WHERE clause
]);
$itemMovements = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Group items by movement category
$movementCategories = [
    'Fast Moving' => [],
    'Moderate Moving' => [],
    'Slow Moving' => []
];

foreach ($itemMovements as $item) {
    $movementCategories[$item['movement_category']][] = $item;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Item Movement Report</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>Monthly Item Movement Report
                    </h4>
                    <div class="d-flex align-items-center">
                        <form class="me-3" method="GET">
                            <div class="input-group">
                                <input type="month" class="form-control" name="month" 
                                       value="<?php echo date('Y-m', strtotime($selectedMonth)); ?>"
                                       onchange="this.form.submit()">
                            </div>
                        </form>
                        <a href="movement_analytics.php" class="btn btn-success me-2">
                            <i class="fas fa-chart-bar me-2"></i>Advanced Analytics
                        </a>
                        <a href="../pharmacy/pharmacydashboard.php" class="btn btn-light btn-lg rounded-3">
                            <i class="fas fa-home me-2"></i>Homepage
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Classification Summary -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="alert alert-light border">
                            <h5 class="mb-3"><i class="fas fa-info-circle me-2"></i>Enhanced Movement Classification</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card border-success">
                                        <div class="card-body text-center">
                                            <i class="fas fa-bolt text-success fa-2x mb-2"></i>
                                            <h6 class="text-success">Fast Moving</h6>
                                            <small>≥3 times/week OR ≥100 units OR ≥40% consistency</small>
                                            <div class="mt-2">
                                                <span class="badge bg-success fs-6"><?php echo count($movementCategories['Fast Moving']); ?> items</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card border-warning">
                                        <div class="card-body text-center">
                                            <i class="fas fa-arrow-right text-warning fa-2x mb-2"></i>
                                            <h6 class="text-warning">Moderate Moving</h6>
                                            <small>1-3 times/week OR 25-99 units OR 15-40% consistency</small>
                                            <div class="mt-2">
                                                <span class="badge bg-warning fs-6"><?php echo count($movementCategories['Moderate Moving']); ?> items</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card border-danger">
                                        <div class="card-body text-center">
                                            <i class="fas fa-turtle text-danger fa-2x mb-2"></i>
                                            <h6 class="text-danger">Slow Moving</h6>
                                            <small><1 time/week AND <25 units AND <15% consistency</small>
                                            <div class="mt-2">
                                                <span class="badge bg-danger fs-6"><?php echo count($movementCategories['Slow Moving']); ?> items</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <ul class="nav nav-tabs mb-4" id="movementTabs" role="tablist">
                    <?php $first = true; foreach ($movementCategories as $category => $items): ?>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link <?php echo $first ? 'active' : ''; ?>" 
                                id="<?php echo str_replace(' ', '-', strtolower($category)); ?>-tab" 
                                data-bs-toggle="tab" 
                                data-bs-target="#<?php echo str_replace(' ', '-', strtolower($category)); ?>" 
                                type="button" role="tab">
                            <i class="fas <?php echo $category === 'Fast Moving' ? 'fa-bolt' : 
                                                ($category === 'Moderate Moving' ? 'fa-arrow-right' : 'fa-turtle'); ?> me-2"></i>
                            <?php echo $category; ?>
                        </button>
                    </li>
                    <?php $first = false; endforeach; ?>
                </ul>

                <div class="tab-content" id="movementTabContent">
                    <?php $first = true; foreach ($movementCategories as $category => $items): ?>
                    <div class="tab-pane fade <?php echo $first ? 'show active' : ''; ?>" 
                         id="<?php echo str_replace(' ', '-', strtolower($category)); ?>" 
                         role="tabpanel">
                        <div class="alert alert-info">
                            <?php
                            switch($category) {
                                case 'Fast Moving':
                                    echo '<strong>High Frequency Items:</strong> These items are used ≥3 times per week, have high volume (≥100 units), or are used consistently (≥40% of days). They require frequent restocking and priority inventory monitoring.';
                                    break;
                                case 'Moderate Moving':
                                    echo '<strong>Medium Frequency Items:</strong> These items are used 1-3 times per week, have medium volume (25-99 units), or moderate consistency (15-40% of days). They maintain steady demand and require regular inventory checks.';
                                    break;
                                case 'Slow Moving':
                                    echo '<strong>Low Frequency Items:</strong> These items are used <1 time per week, have low volume (<25 units), and low consistency (<15% of days). Consider reviewing stock levels and procurement strategy.';
                                    break;
                            }
                            ?>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover border">
                                <thead class="table-light">
                                    <tr>
                                        <th>Item Description</th>
                                        <th>Category</th>
                                        <th>Unit</th>
                                        <th>Total Qty</th>
                                        <th>Transactions</th>
                                        <th>Freq/Week</th>
                                        <th>Active Days</th>
                                        <th>Consistency</th>
                                        <th>Avg Daily</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($items as $item): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($item['generaldescription']); ?></td>
                                        <td><span class="badge bg-info"><?php echo htmlspecialchars($item['categorydesc']); ?></span></td>
                                        <td><?php echo htmlspecialchars($item['unitmeasure']); ?></td>
                                        <td class="fw-bold"><?php echo number_format($item['total_quantity']); ?></td>
                                        <td><?php echo number_format($item['transaction_count']); ?></td>
                                        <td>
                                            <span class="badge <?php echo $item['transactions_per_week'] >= 3 ? 'bg-success' :
                                                                    ($item['transactions_per_week'] >= 1 ? 'bg-warning' : 'bg-danger'); ?>">
                                                <?php echo number_format($item['transactions_per_week'], 1); ?>
                                            </span>
                                        </td>
                                        <td><?php echo number_format($item['active_days']); ?>/<?php echo number_format($item['total_days_in_period']); ?></td>
                                        <td>
                                            <span class="badge <?php echo $item['usage_consistency'] >= 0.4 ? 'bg-success' :
                                                                    ($item['usage_consistency'] >= 0.15 ? 'bg-warning' : 'bg-danger'); ?>">
                                                <?php echo number_format($item['usage_consistency'] * 100, 1); ?>%
                                            </span>
                                        </td>
                                        <td><?php echo number_format($item['avg_daily_usage'], 1); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <?php $first = false; endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
