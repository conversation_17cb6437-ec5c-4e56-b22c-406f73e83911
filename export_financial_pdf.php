<?php
require_once 'database.php';
require('fpdf/fpdf.php');

// Get current year, month, and set default values
$currentYear = date('Y');
$currentMonth = date('m');

// Get selected year or use current year as default
$selectedYear = isset($_GET['year']) ? $_GET['year'] : $currentYear;
$selectedMonth = isset($_GET['month']) ? $_GET['month'] : $currentMonth;

// Check if there are any allocations for the selected year
$checkAllocation = $conn->query("
    SELECT COUNT(*) as count 
    FROM accounts 
    WHERE account_year = $selectedYear")->fetch();

if ($checkAllocation['count'] == 0) {
    die("No budget allocations found for year $selectedYear");
}

// Fetch financial summary data with monthly breakdown
$financialSummary = $conn->query("
    SELECT 
        a.account_code,
        a.account_description,
        a.account_amount as budget,
        COALESCE(SUM(td.amount), 0) as utilized,
        (a.account_amount - COALESCE(SUM(td.amount), 0)) as remaining,
        MONTH(t.transaction_date) as month,
        COALESCE(SUM(CASE WHEN MONTH(t.transaction_date) = MONTH(CURRENT_DATE) THEN td.amount ELSE 0 END), 0) as current_month_spent
    FROM accounts a
    LEFT JOIN transaction_details td ON a.account_code = td.account_code
    LEFT JOIN transactions t ON td.transaction_id = t.transaction_id
    WHERE a.account_year = $selectedYear AND MONTH(t.transaction_date) = $selectedMonth
    GROUP BY a.account_code, a.account_description, a.account_amount, MONTH(t.transaction_date)
    ORDER BY a.account_code, month")->fetchAll(PDO::FETCH_GROUP);

// Calculate enhanced totals and validate data
$totalBudget = 0;
$totalUtilized = 0;
$totalRemaining = 0;
$monthlySpending = array_fill(1, 12, 0);
$currentMonthSpent = 0;

if (empty($financialSummary)) {
    error_log("Warning: No financial data found for year $selectedYear month $selectedMonth");
}

foreach ($financialSummary as $accountData) {
    if (!isset($accountData[0]['budget'])) {
        error_log("Warning: Missing budget data for an account");
        continue;
    }
    $totalBudget += $accountData[0]['budget'];
    $totalUtilized += array_sum(array_column($accountData, 'utilized'));
    foreach ($accountData as $record) {
        if (isset($record['month'])) {
            $monthlySpending[$record['month']] += $record['utilized'];
        }
        if (isset($record['current_month_spent'])) {
            $currentMonthSpent += $record['current_month_spent'];
        }
    }
}

// Validate totals
if ($totalBudget <= 0) {
    error_log("Warning: Total budget is zero or negative: $totalBudget");
}

$totalRemaining = $totalBudget - $totalUtilized;
$utilizationRate = ($totalBudget > 0) ? ($totalUtilized / $totalBudget * 100) : 0;
$monthlyBudget = $totalBudget / 12;
$monthlyUtilizationRate = ($monthlyBudget > 0) ? ($currentMonthSpent / $monthlyBudget * 100) : 0;

// Rest of the code remains the same...

// Fetch department expenditure with year-to-date comparison
$departmentExpenditure = $conn->query("
    SELECT 
        d.department_name,
        COALESCE(SUM(td.amount), 0) as total_spent,
        COALESCE(SUM(CASE WHEN YEAR(t.transaction_date) = $currentYear - 1 THEN td.amount ELSE 0 END), 0) as last_year_spent,
        COUNT(DISTINCT t.transaction_id) as transaction_count
    FROM departments d
    LEFT JOIN employees e ON d.id = e.department_id
    LEFT JOIN transactions t ON e.id = t.employee_id
    LEFT JOIN transaction_details td ON t.transaction_id = td.transaction_id
    WHERE YEAR(t.transaction_date) >= $currentYear - 1
    GROUP BY d.id, d.department_name
    ORDER BY total_spent DESC")->fetchAll();

// Fetch monthly trends
$monthlyTrends = $conn->query("
    SELECT 
        MONTH(t.transaction_date) as month,
        COALESCE(SUM(td.amount), 0) as monthly_total
    FROM transactions t
    JOIN transaction_details td ON t.transaction_id = td.transaction_id
    WHERE YEAR(t.transaction_date) = $currentYear
    GROUP BY MONTH(t.transaction_date)
    ORDER BY month")->fetchAll();

// Fetch top spending categories
$topCategories = $conn->query("
    SELECT 
        a.account_description,
        COALESCE(SUM(td.amount), 0) as total_spent
    FROM accounts a
    LEFT JOIN transaction_details td ON a.account_code = td.account_code
    LEFT JOIN transactions t ON td.transaction_id = t.transaction_id
    WHERE YEAR(t.transaction_date) = $currentYear
    GROUP BY a.account_code, a.account_description
    ORDER BY total_spent DESC
    LIMIT 5")->fetchAll();

// Create PDF document
$pdf = new FPDF('P', 'mm', 'A4');
$pdf->AddPage();

// Add logos
$pdf->Image('images/pgns.png', 50, 10, 20);
$pdf->Image('images/bdh.png', 140, 10, 20);

// Header section
$pdf->SetFont('Arial', '', 12);
$pdf->Cell(0, 6, 'Republic of the Philippines', 0, 1, 'C');
$pdf->Cell(0, 6, 'Province of Northern Samar', 0, 1, 'C');
$pdf->Cell(0, 6, 'Provincial Health Office', 0, 1, 'C');
$pdf->SetFont('Arial', 'B', 16);
$pdf->Cell(0, 8, 'BIRI DISTRICT HOSPITAL', 0, 1, 'C');
$pdf->SetFont('Arial', 'I', 11);
$pdf->Cell(0, 6, 'Biri Northern Samar', 0, 1, 'C');

// Report title
$pdf->SetFont('Arial', 'B', 14);
$pdf->Cell(0, 10, 'FINANCIAL REPORT', 0, 1, 'C');
$pdf->SetFont('Arial', '', 11);
$pdf->Cell(0, 6, 'For ' . date('F Y', mktime(0, 0, 0, $selectedMonth, 1, $selectedYear)), 0, 1, 'C');
$pdf->Ln(10);

// Executive Summary Section
$pdf->SetFillColor(240, 240, 240);
$pdf->SetFont('Arial', 'B', 12);
$pdf->Cell(0, 8, 'EXECUTIVE SUMMARY', 0, 1, 'L');
$pdf->SetFont('Arial', '', 10);

// Key Metrics Table
$pdf->SetFillColor(245, 245, 245);
$pdf->Cell(100, 8, 'Total Budget Allocation:', 1, 0, 'L', true);
$pdf->Cell(90, 8, 'PHP ' . number_format($totalBudget, 2), 1, 1, 'R', true);
$pdf->Cell(100, 8, 'Total Utilized Amount:', 1, 0, 'L');
$pdf->Cell(90, 8, 'PHP ' . number_format($totalUtilized, 2), 1, 1, 'R');
$pdf->Cell(100, 8, 'Remaining Balance:', 1, 0, 'L', true);
$pdf->Cell(90, 8, 'PHP ' . number_format($totalRemaining, 2), 1, 1, 'R', true);
$pdf->Cell(100, 8, 'Budget Utilization Rate:', 1, 0, 'L');
$pdf->Cell(90, 8, number_format($utilizationRate, 1) . '%', 1, 1, 'R');

$pdf->Ln(10);

// Monthly Analysis Section
$pdf->SetFont('Arial', 'B', 12);
$pdf->Cell(0, 8, 'MONTHLY ANALYSIS', 0, 1, 'L');
$pdf->SetFont('Arial', '', 10);

// Create monthly spending chart
$pdf->Cell(60, 8, 'Month', 1, 0, 'C', true);
$pdf->Cell(65, 8, 'Amount Spent', 1, 0, 'C', true);
$pdf->Cell(65, 8, 'Percentage of Budget', 1, 1, 'C', true);

foreach ($monthlyTrends as $trend) {
    $monthName = date('F', mktime(0, 0, 0, $trend['month'], 1));
    $percentage = ($monthlyBudget > 0) ? ($trend['monthly_total'] / $monthlyBudget * 100) : 0;
    
    $pdf->Cell(60, 8, $monthName, 1, 0, 'L');
    $pdf->Cell(65, 8, 'PHP ' . number_format($trend['monthly_total'], 2), 1, 0, 'R');
    $pdf->Cell(65, 8, number_format($percentage, 1) . '%', 1, 1, 'R');
}

$pdf->Ln(10);

// Top Spending Categories
$pdf->SetFont('Arial', 'B', 12);
$pdf->Cell(0, 8, 'TOP SPENDING CATEGORIES', 0, 1, 'L');
$pdf->SetFont('Arial', '', 10);

$pdf->Cell(120, 8, 'Category', 1, 0, 'C', true);
$pdf->Cell(70, 8, 'Total Spent', 1, 1, 'C', true);

foreach ($topCategories as $category) {
    $pdf->Cell(120, 8, $category['account_description'], 1, 0, 'L');
    $pdf->Cell(70, 8, 'PHP ' . number_format($category['total_spent'], 2), 1, 1, 'R');
}

// Department Expenditure
$pdf->AddPage();
$pdf->SetFont('Arial', 'B', 12);
$pdf->Cell(0, 8, 'DEPARTMENT EXPENDITURE ANALYSIS', 0, 1, 'L');
$pdf->SetFont('Arial', '', 10);

$pdf->Cell(70, 8, 'Department', 1, 0, 'C', true);
$pdf->Cell(40, 8, 'Total Spent', 1, 0, 'C', true);
$pdf->Cell(40, 8, 'Last Year', 1, 0, 'C', true);
$pdf->Cell(40, 8, 'Transactions', 1, 1, 'C', true);

foreach ($departmentExpenditure as $dept) {
    $pdf->Cell(70, 8, $dept['department_name'], 1, 0, 'L');
    $pdf->Cell(40, 8, number_format($dept['total_spent'], 2), 1, 0, 'R');
    $pdf->Cell(40, 8, number_format($dept['last_year_spent'], 2), 1, 0, 'R');
    $pdf->Cell(40, 8, $dept['transaction_count'], 1, 1, 'C');
}

// Footer
$pdf->SetY(-15);
$pdf->SetFont('Arial', 'I', 8);
$pdf->Cell(0, 10, 'Page ' . $pdf->PageNo() . ' of {nb}', 0, 0, 'C');

// Output PDF
$pdf->Output('Financial_Report_' . $selectedYear . '_' . $selectedMonth . '.pdf', 'I');
