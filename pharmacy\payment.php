<?php
require_once '../database.php';
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// Create payment table if not exists
$sql = "CREATE TABLE IF NOT EXISTS `payment` (
    `payment_id` INT(10) NOT NULL AUTO_INCREMENT,
    `transaction_id` INT(10) NOT NULL,
    `payment_type` ENUM('Cash', 'Credit', 'Philhealth') NOT NULL DEFAULT 'Cash',
    `amount_paid` DECIMAL(10,2) NOT NULL,
    `payment_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `employee_name` VARCHAR(255) NULL DEFAULT NULL,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`payment_id`),
    INDEX `transaction_id` (`transaction_id`),
    CONSTRAINT `payment_ibfk_1` FOREIGN KEY (`transaction_id`) 
        REFERENCES `pharmatransactions` (`transaction_id`) 
        ON UPDATE CASCADE ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
$conn->exec($sql);

// Check if there's an error parameter in the URL
if (isset($_GET['error']) && $_GET['error'] == 1) {
    echo '<div class="alert alert-danger" role="alert">
            Error processing payment. Please try again.
          </div>';
}

// Handle POST request for payment processing
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Get form data
        $transactionId = $_POST['transaction_id'];
        $paymentType = $_POST['payment_type'];
        $amountPaid = $_POST['amount'];
        $employeeName = isset($_POST['employee_name']) ? $_POST['employee_name'] : null;

        // Check if payment already exists for this transaction
        $checkStmt = $conn->prepare("SELECT payment_id FROM payment WHERE transaction_id = ?");
        $checkStmt->execute([$transactionId]);
        $existingPayment = $checkStmt->fetch();

        if ($existingPayment) {
            // Update existing payment
            $sql = "UPDATE payment SET 
                    payment_type = ?,
                    amount_paid = ?,
                    employee_name = ?,
                    updated_at = CURRENT_TIMESTAMP
                    WHERE transaction_id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$paymentType, $amountPaid, $employeeName, $transactionId]);
        } else {
            // Insert new payment
            $sql = "INSERT INTO payment 
                    (transaction_id, payment_type, amount_paid, employee_name)
                    VALUES (?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$transactionId, $paymentType, $amountPaid, $employeeName]);
        }

        // Redirect to success page or show success message
        header("Location: payment.php?ref=" . $_POST['transaction_reference'] . "&success=1");
        exit();

    } catch (PDOException $e) {
        // Handle error
        error_log("Payment Error: " . $e->getMessage());
        header("Location: payment.php?ref=" . $_POST['transaction_reference'] . "&error=1");
        exit();
    }
}
$transaction = null;
$items = null;

if(isset($_GET['ref']) && !empty($_GET['ref'])) {
    $ref = trim($_GET['ref']);
    
    // Get transaction and patient details
    $stmt = $conn->prepare("SELECT t.*, p.patientname, p.patient_type 
                           FROM pharmatransactions t 
                           JOIN patient p ON t.patientid = p.patientid 
                           WHERE t.transaction_reference = ?");
    $stmt->execute([$ref]);
    $transaction = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($transaction) {
        // Get transaction items with details (updated to use itemid directly)
        $sql = "SELECT ptd.transaction_id, ptd.itemid,
                ptd.quantity, ptd.unit_price,
                ptd.subtotal, ptd.original_price,
                ptd.discount_applied, i.generaldescription, i.unitmeasure,
                (SELECT psl.selling_price FROM pharmacy_stock_ledger psl WHERE psl.itemid = ptd.itemid ORDER BY psl.expiry_date ASC LIMIT 1) as selling_price,
                (SELECT psl.expiry_date FROM pharmacy_stock_ledger psl WHERE psl.itemid = ptd.itemid ORDER BY psl.expiry_date ASC LIMIT 1) as expiry_date,
                (SELECT psl.lot_no FROM pharmacy_stock_ledger psl WHERE psl.itemid = ptd.itemid ORDER BY psl.expiry_date ASC LIMIT 1) as lot_no
                FROM pharmatransaction_details ptd
                INNER JOIN pharmatransactions pt ON pt.transaction_id = ptd.transaction_id
                INNER JOIN items i ON ptd.itemid = i.itemid
                WHERE pt.transaction_reference = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$ref]);
        $items = $stmt->fetchAll();

        // Calculate totals
        $total_amount = array_sum(array_column($items, 'subtotal'));
        $total_discount = 0;
        foreach($items as $item) {
            if($item['discount_applied'] > 0) {
                $total_discount += ($item['selling_price'] * $item['quantity']) - $item['subtotal'];
            }
        }

        $transaction['total_amount'] = $total_amount;
        $transaction['total_discount'] = $total_discount;
        $transaction['final_amount'] = $total_amount;

        // Check payment status based on payment type
        $checkPaymentStmt = $conn->prepare("SELECT payment_id, payment_type FROM payment WHERE transaction_id = ?");
        $checkPaymentStmt->execute([$transaction['transaction_id']]);
        $paymentInfo = $checkPaymentStmt->fetch();
        
        if ($paymentInfo) {
            // Mark as paid if it's a Cash or PhilHealth payment
            $transaction['status'] = ($paymentInfo['payment_type'] === 'Cash' || $paymentInfo['payment_type'] === 'Philhealth') ? 'PAID' : 'UNPAID';
        } else {
            $transaction['status'] = 'UNPAID';
        }
    }
}

// Load all unpaid transactions
$unpaidTransactions = [];
if (!isset($_GET['ref'])) {
    $sql = "SELECT pt.transaction_reference, pt.transaction_id, p.patientname, p.patient_type,
            pt.ward, pt.transaction_date, 
            (SELECT SUM(subtotal) FROM pharmatransaction_details WHERE transaction_id = pt.transaction_id) as total_amount
            FROM pharmatransactions pt
            LEFT JOIN payment pay ON pt.transaction_id = pay.transaction_id AND (pay.payment_type = 'Cash' OR pay.payment_type = 'Philhealth')
            JOIN patient p ON pt.patientid = p.patientid
            WHERE pay.payment_id IS NULL
            OR (pay.payment_id IS NOT NULL AND pay.payment_type = 'Credit' AND NOT EXISTS (
                SELECT 1 FROM payment p2 
                WHERE p2.transaction_id = pt.transaction_id 
                AND (p2.payment_type = 'Cash' OR p2.payment_type = 'Philhealth')
            ))
            ORDER BY pt.transaction_date DESC";
    
    $stmt = $conn->query($sql);
    $unpaidTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get transaction details for each unpaid transaction (updated to use itemid directly)
   foreach ($unpaidTransactions as &$trans) {
    $sql = "SELECT ptd.transaction_id, ptd.itemid,
            ptd.quantity, ptd.unit_price, ptd.subtotal, ptd.original_price,
            ptd.discount_applied, i.generaldescription, i.unitmeasure,
            (SELECT psl.selling_price FROM pharmacy_stock_ledger psl WHERE psl.itemid = ptd.itemid ORDER BY psl.expiry_date ASC LIMIT 1) as selling_price
            FROM pharmatransaction_details ptd
            INNER JOIN items i ON ptd.itemid = i.itemid
            WHERE ptd.transaction_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$trans['transaction_id']]);
    $trans['items'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
}
unset($trans); // 🔥 This is crucial
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Processing - BIRI District Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            height: 100vh;
            overflow-y: hidden;
        }
        .main-container {
            height: calc(100vh - 2rem);
            display: flex;
        }
        .left-panel {
            width: 30%;
            padding: 1rem;
            overflow-y: auto;
        }
        .right-panel {
            width: 70%;
            padding: 1rem;
            overflow-y: auto;
        }
        .transaction-items {
            height: calc(100vh - 300px);
            overflow-y: auto;
        }
        .amount-card {
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        /* Modern scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--background-color);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="left-panel">
            <!-- Search Form -->
            <form method="GET" class="mb-4">
                <div class="input-group">
                    <span class="input-group-text bg-light">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" name="ref" class="form-control form-control-lg fw-bold" 
                           placeholder="Enter Transaction Reference" 
                           style="font-size: 1.2rem; font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;" 
                           value="<?php echo isset($_GET['ref']) ? $_GET['ref'] : ''; ?>" required>
                    <button type="submit" class="btn btn-primary btn-lg rounded-3 shadow-sm px-4">
                        Search
                    </button>
                    <a href="../pharmacy/pharmacydashboard.php" class="btn btn-outline-primary btn-lg rounded-3 shadow-sm ms-2">
                        <i class="fas fa-home me-2"></i>Homepage
                    </a>
                </div>

                <?php if (!isset($_GET['ref'])): ?>
                <div class="table-responsive mt-4">
                    <table class="table table-hover">
                        <thead class="bg-light">
                            <tr>
                                <th>Reference</th>
                                <th>Patient</th>
                                <th>Ward</th>
                                <th>Amount</th>
                                <th>Date</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $shown_references = array();
                            foreach ($unpaidTransactions as $trans):
                                if (!in_array($trans['transaction_reference'], $shown_references)):
                                    $shown_references[] = $trans['transaction_reference'];
                            ?>
                            <tr>
                                <td><?php echo $trans['transaction_reference']; ?></td>
                                <td>
                                    <?php echo $trans['patientname']; ?>
                                    <span class="badge bg-info-subtle text-info"><?php echo $trans['patient_type']; ?></span>
                                </td>
                                <td><?php echo $trans['ward']; ?></td>
                                <td>₱<?php echo number_format($trans['total_amount'] ?? 0, 2); ?></td>
                                <td><?php echo date('M d, Y', strtotime($trans['transaction_date'])); ?></td>
                                <td>
                                    <a href="?ref=<?php echo $trans['transaction_reference']; ?>" 
                                       class="btn btn-sm btn-primary">
                                        <i class="fas fa-arrow-right"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php 
                                endif;
                            endforeach; 
                            ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </form>

            <?php if($transaction): ?>
            <!-- Patient Information -->
            <div class="card mb-4 shadow-sm">
                <?php
                // Check payment status based on payment type. Mark as paid if it's a Cash or PhilHealth payment.
                if ($transaction) {
                    $checkPaymentStmt = $conn->prepare("SELECT payment_id, payment_type, employee_name FROM payment WHERE transaction_id = ?");
                    $checkPaymentStmt->execute([$transaction['transaction_id']]);
                    $paymentExists = $checkPaymentStmt->fetch();

                    if ($paymentExists && ($paymentExists['payment_type'] === 'Cash' || $paymentExists['payment_type'] === 'Philhealth')) {
                        $transaction['status'] = 'PAID';
                        $cardBorderClass = 'border-success';
                    } else {
                        $transaction['status'] = 'UNPAID';
                        $cardBorderClass = 'border-danger';
                    }
                }
                ?>
                <div class="card-body <?php echo $cardBorderClass; ?> rounded-3">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="card-title mb-0">
                            <i class="fas fa-user-circle me-2 text-primary fa-lg"></i>
                            <span class="text-primary fw-bold">Patient Information</span>
                        </h4>
                        <span class="badge fs-6 px-4 py-2 rounded-pill <?php echo $paymentExists ? 'bg-success-subtle text-success' : 'bg-danger-subtle text-danger'; ?>">
                            <i class="fas <?php echo $paymentExists ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> me-2"></i>
                            <?php echo $transaction['status']; ?>
                        </span>
                    </div>
                    <div class="row g-2">
                        <div class="col-md-6">
                            <small class="text-muted">Patient Name</small>
                            <div><strong><?php echo $transaction['patientname']; ?></strong></div>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">Patient Type</small>
                            <div><span class="badge bg-info-subtle"><?php echo $transaction['patient_type']; ?></span></div>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">Ward</small>
                            <div><strong><?php echo $transaction['ward']; ?></strong></div>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">Reference</small>
                            <div><span class="badge bg-secondary-subtle"><?php echo $transaction['transaction_reference']; ?></span></div>
                        </div>
                        <div class="col-12">
                            <small class="text-muted">Credit Payee</small>
                            <div><strong><?php echo $paymentExists ? $paymentExists['employee_name'] : 'N/A'; ?></strong></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Form -->
            <form method="POST" id="paymentForm" class="needs-validation" novalidate>
                <input type="hidden" name="transaction_reference" value="<?php echo $transaction['transaction_reference']; ?>">
                <input type="hidden" name="transaction_id" value="<?php echo $transaction['transaction_id']; ?>">
                
                <div class="card mb-4 shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-money-bill-wave me-2"></i>Payment Details</h5>
                        
                        <!-- Payment Type Selection -->
                        <div class="form-group mb-3">
                            <label class="form-label fw-bold">Payment Type</label>
                            <select name="payment_type" class="form-select" required>
                                <option value="">Select Payment Type</option>
                                <option value="Cash" <?php echo ($paymentExists && $paymentExists['payment_type'] == 'Cash') ? 'selected' : ''; ?>>Cash Payment</option>
                                <option value="Credit" <?php echo ($paymentExists && $paymentExists['payment_type'] == 'Credit') ? 'selected' : ''; ?>>Credit (Hospital Employees Only)</option>
                                <option value="Philhealth" <?php echo ($paymentExists && $paymentExists['payment_type'] == 'Philhealth') ? 'selected' : ''; ?>>Philhealth Insurance</option>
                            </select>
                            <div class="invalid-feedback">Please select a payment type.</div>
                        </div>

                        <!-- Employee Name Field (for Credit) -->
                        <div class="form-group mb-3" id="employeeNameField">
                            <label class="form-label fw-bold">Employee Name</label>
                            <input type="text" name="employee_name" class="form-control" 
                                   placeholder="Enter complete employee name"
                                   value="<?php echo $paymentExists ? $paymentExists['employee_name'] : ''; ?>">
                            <div class="invalid-feedback">Employee name is required for credit payments.</div>
                        </div>
                        <script>
                            document.addEventListener('DOMContentLoaded', function() {
                                const paymentTypeSelect = document.querySelector('select[name="payment_type"]');
                                const employeeNameField = document.getElementById('employeeNameField');
                                
                                function toggleEmployeeField() {
                                    employeeNameField.style.display = paymentTypeSelect.value === 'Credit' ? 'block' : 'none';
                                }
                                
                                paymentTypeSelect.addEventListener('change', toggleEmployeeField);
                                toggleEmployeeField(); // Initial state
                            });
                        </script>

                        <!-- Payment Amount -->
                        <div class="form-group mb-4">
                            <label class="form-label fw-bold">Amount to Pay</label>
                            <div class="input-group">
                                <span class="input-group-text">₱</span>
                                <input type="number" step="0.01" name="amount" 
                                       class="form-control" required
                                       value="<?php echo $transaction['final_amount']; ?>"
                                       min="<?php echo $transaction['final_amount']; ?>">
                                <div class="invalid-feedback">Please enter a valid amount.</div>
                            </div>
                        </div>

                        <!-- Payment Summary Card -->
                        <div class="amount-card mb-4">
                            <?php
                            $total_original = array_sum(array_map(function($item) {
                                return $item['selling_price'] * $item['quantity'];
                            }, $items));
                            ?>
                            <div class="d-flex justify-content-between mb-2">
                                <span class="fw-bold">Original Total:</span>
                                <span>₱<?php echo number_format($total_original, 2); ?></span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span class="fw-bold">Total Discount:</span>
                                <span class="text-danger">-₱<?php echo number_format($transaction['total_discount'], 2); ?></span>
                            </div>
                            <hr class="my-2">
                            <div class="d-flex justify-content-between">
                                <span class="fw-bold">Final Amount:</span>
                                <span class="total-amount fw-bold">₱<?php echo number_format($transaction['final_amount'], 2); ?></span>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-success btn-lg flex-grow-1">
                                <i class="fas fa-check-circle me-2"></i>Process Payment
                            </button>
                            <a href="transaction_details.php?id=<?php echo $transaction['transaction_id']; ?>" class="btn btn-outline-primary btn-lg flex-grow-1">
                                <i class="fas fa-arrow-left me-2"></i>Back to Details
                            </a>
                        </div>
                    </div>
                </div>
            </form>
            <?php endif; ?>
        </div>

        <div class="right-panel bg-white rounded-4 shadow-lg">
            <?php if($transaction): ?>
            <div class="card border-0 h-100">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5 class="card-title fw-bold m-0">
                            <i class="fas fa-receipt me-2 text-primary"></i>Transaction Details
                        </h5>
                        <span class="badge bg-primary-subtle text-primary px-3 py-2 rounded-pill">
                            <i class="fas fa-shopping-cart me-1"></i><?php echo count($items); ?> Items
                        </span>
                    </div>
                    <div class="transaction-items custom-scrollbar">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle" id="itemsTable">
                                <thead class="bg-light">
                                    <tr>
                                        <th class="fw-semibold">Item Description</th>
                                        <th class="text-end fw-semibold">Qty</th>
                                        <th class="text-end fw-semibold">Original Price</th>
                                        <th class="text-end fw-semibold">Discount</th>
                                        <th class="text-end fw-semibold">Subtotal</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($items as $item): ?>
                                    <tr class="border-bottom">
                                        <td class="py-3"><?php echo $item['generaldescription']; ?></td>
                                        <td class="text-end py-3"><?php echo $item['quantity']; ?></td>
                                        <td class="text-end py-3">₱<?php echo number_format($item['selling_price'], 2); ?></td>
                                        <td class="text-end py-3">
                                            <?php if($item['discount_applied'] > 0): ?>
                                                <span class="text-danger">-₱<?php echo number_format($item['discount_applied'], 2); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">—</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-end py-3 fw-semibold">₱<?php echo number_format($item['subtotal'], 2); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <?php else: ?>
                <?php if(isset($_GET['ref'])): ?>
                <div class="alert alert-warning border-0 shadow-sm rounded-4 p-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-circle fa-2x me-3 text-warning"></i>
                        <div>
                            <h5 class="alert-heading mb-1">No Transaction Found</h5>
                            <p class="mb-0">The transaction reference you provided could not be found in our records.</p>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#itemsTable').DataTable({
                "paging": false,
                "searching": false,
                "info": false
            });

            $('#paymentForm').on('submit', function(e) {
                const amountInput = parseFloat($('input[name="amount"]').val());
                const finalAmount = <?php echo isset($transaction['final_amount']) ? $transaction['final_amount'] : 0; ?>;
                
                if (amountInput < finalAmount) {
                    e.preventDefault();
                    alert('Payment amount cannot be less than the final amount!');
                }
            });
        });
    </script>
</body>
</html>
