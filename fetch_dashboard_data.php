<?php
require_once 'database.php';

try {
    // Get basic counts - ensure integer values
    $employeeCount = (int)$conn->query("SELECT COUNT(*) FROM employees")->fetchColumn();
    $departmentCount = (int)$conn->query("SELECT COUNT(*) FROM departments")->fetchColumn();
    $accountCount = (int)$conn->query("SELECT COUNT(*) FROM accounts")->fetchColumn();

    // Get total utilization per account code for the chart
    $stmt = $conn->query("
        SELECT 
            a.account_code,
            a.account_description,
            COALESCE(SUM(td.amount), 0) as total_utilization
        FROM accounts a
        LEFT JOIN transaction_details td ON a.account_code = td.account_code
        GROUP BY a.account_code, a.account_description
        ORDER BY a.account_code ASC
    ");
    $utilizationData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Format utilization data for the chart
    $labels = [];
    $values = [];
    foreach ($utilizationData as $data) {
        $labels[] = $data['account_code'] . ' - ' . $data['account_description'];
        $values[] = floatval($data['total_utilization']);
    }

    // Get allocation vs utilization data
    $stmt = $conn->query("
        SELECT 
            a.account_code,
            a.account_description,
            COALESCE(a.amount, 0) as allocated_amount,
            COALESCE(SUM(td.amount), 0) as utilized_amount
        FROM accounts a
        LEFT JOIN transaction_details td ON a.account_code = td.account_code
        GROUP BY a.account_code, a.account_description, a.amount
    ");
    $accountUtilization = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Ensure numeric values in account utilization
    foreach ($accountUtilization as &$account) {
        $account['allocated_amount'] = floatval($account['allocated_amount']);
        $account['utilized_amount'] = floatval($account['utilized_amount']);
    }

    // Prepare response data
    $response = [
        'employeeCount' => $employeeCount,
        'departmentCount' => $departmentCount,
        'accountCount' => $accountCount,
        'accountUtilization' => $accountUtilization,
        'revenueData' => [
            'labels' => $labels,
            'values' => $values
        ]
    ];

    header('Content-Type: application/json');
    echo json_encode($response);
} catch(PDOException $e) {
    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode(['error' => $e->getMessage()]);
}
