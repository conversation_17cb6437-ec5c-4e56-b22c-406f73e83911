<?php
require_once '../database.php';

// Function to check if medication requires monitoring and create monitoring record
function checkAndCreateMonitoring($itemid, $patientid, $transaction_id, $doctorid) {
    global $conn;
    
    try {
        // Check if this medication has monitoring rules
        $rule_sql = "SELECT mmr.rule_id, mmr.frequency_days, mmr.age_restrictions, 
                           mmc.category_name, mmc.risk_level, mmc.beers_criteria
                    FROM medication_monitoring_rules mmr
                    JOIN medication_monitoring_categories mmc ON mmr.category_id = mmc.category_id
                    JOIN items i ON (i.generaldescription LIKE CONCAT('%', mmr.medication_name, '%') 
                                   OR i.generaldescription LIKE CONCAT('%', mmr.generic_name, '%'))
                    WHERE i.itemid = ? AND mmr.is_active = 1";
        
        $rule_stmt = $conn->prepare($rule_sql);
        $rule_stmt->execute([$itemid]);
        $rules = $rule_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($rules)) {
            return false; // No monitoring required
        }
        
        // Get patient information
        $patient_sql = "SELECT patientid, patientname, birthdate, patient_type FROM patient WHERE patientid = ?";
        $patient_stmt = $conn->prepare($patient_sql);
        $patient_stmt->execute([$patientid]);
        $patient = $patient_stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$patient) {
            return false;
        }
        
        // Calculate patient age
        $age = 0;
        if ($patient['birthdate']) {
            $birthDate = new DateTime($patient['birthdate']);
            $today = new DateTime();
            $age = $birthDate->diff($today)->y;
        }
        
        foreach ($rules as $rule) {
            // Check age restrictions
            $age_restrictions = json_decode($rule['age_restrictions'], true);
            $requires_monitoring = true;
            $priority_level = 'Medium';
            
            // Determine priority based on age and medication category
            if ($rule['beers_criteria'] && $age >= 65) {
                $priority_level = 'High';
                $alert_message = "Beers Criteria medication prescribed to patient aged $age years";
                createAlert($patientid, null, 'Beers_Criteria', 'High', $alert_message);
            }
            
            if ($age >= 75) {
                $priority_level = 'High';
            } elseif ($age >= 65) {
                $priority_level = 'Medium';
            }
            
            if ($rule['risk_level'] === 'Critical') {
                $priority_level = 'Critical';
            } elseif ($rule['risk_level'] === 'High') {
                $priority_level = 'High';
            }
            
            // Check if monitoring already exists for this patient and medication
            $existing_sql = "SELECT monitoring_id FROM patient_medication_monitoring 
                           WHERE patientid = ? AND itemid = ? AND rule_id = ? 
                           AND monitoring_status = 'Active'";
            $existing_stmt = $conn->prepare($existing_sql);
            $existing_stmt->execute([$patientid, $itemid, $rule['rule_id']]);
            
            if ($existing_stmt->fetch()) {
                continue; // Monitoring already exists
            }
            
            // Calculate next monitoring date
            $next_monitoring_date = date('Y-m-d', strtotime('+' . $rule['frequency_days'] . ' days'));
            
            // Create monitoring record
            $monitoring_sql = "INSERT INTO patient_medication_monitoring 
                             (patientid, itemid, rule_id, transaction_id, start_date, next_monitoring_date, 
                              monitoring_status, priority_level, prescribing_doctor, monitoring_notes) 
                             VALUES (?, ?, ?, ?, CURDATE(), ?, 'Active', ?, ?, ?)";
            
            $monitoring_stmt = $conn->prepare($monitoring_sql);
            $monitoring_notes = "Automatic monitoring initiated for " . $rule['category_name'] . " medication";
            
            $monitoring_stmt->execute([
                $patientid,
                $itemid,
                $rule['rule_id'],
                $transaction_id,
                $next_monitoring_date,
                $priority_level,
                $doctorid,
                $monitoring_notes
            ]);
            
            $monitoring_id = $conn->lastInsertId();
            
            // Create initial monitoring alert
            $alert_message = "Monitoring required for high-risk medication. Next monitoring due: " . 
                           date('M d, Y', strtotime($next_monitoring_date));
            
            createAlert($patientid, $monitoring_id, 'Due', $priority_level, $alert_message);
            
            // Log the monitoring creation
            error_log("Medication monitoring created: Patient ID $patientid, Item ID $itemid, Rule ID {$rule['rule_id']}");
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error in checkAndCreateMonitoring: " . $e->getMessage());
        return false;
    }
}

// Function to create alerts
function createAlert($patientid, $monitoring_id, $alert_type, $priority, $message) {
    global $conn;
    
    try {
        $alert_sql = "INSERT INTO medication_monitoring_alerts 
                     (monitoring_id, patientid, alert_type, alert_priority, alert_message, auto_generated) 
                     VALUES (?, ?, ?, ?, ?, 1)";
        
        $alert_stmt = $conn->prepare($alert_sql);
        $alert_stmt->execute([$monitoring_id, $patientid, $alert_type, $priority, $message]);
        
    } catch (Exception $e) {
        error_log("Error creating alert: " . $e->getMessage());
    }
}

// Function to check for drug interactions
function checkDrugInteractions($patientid, $new_itemid) {
    global $conn;
    
    try {
        // Get patient's current active medications
        $current_meds_sql = "SELECT DISTINCT i.generaldescription, i.itemid
                           FROM patient_medication_monitoring pmm
                           JOIN items i ON pmm.itemid = i.itemid
                           WHERE pmm.patientid = ? AND pmm.monitoring_status = 'Active'";
        
        $current_stmt = $conn->prepare($current_meds_sql);
        $current_stmt->execute([$patientid]);
        $current_meds = $current_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get new medication details
        $new_med_sql = "SELECT generaldescription FROM items WHERE itemid = ?";
        $new_med_stmt = $conn->prepare($new_med_sql);
        $new_med_stmt->execute([$new_itemid]);
        $new_med = $new_med_stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$new_med) {
            return;
        }
        
        // Check for known interactions (simplified - in practice, use a comprehensive drug interaction database)
        $interactions = [
            ['drug1' => 'Digoxin', 'drug2' => 'Amiodarone', 'severity' => 'High', 
             'description' => 'Amiodarone increases digoxin levels significantly'],
            ['drug1' => 'Warfarin', 'drug2' => 'Phenytoin', 'severity' => 'Medium', 
             'description' => 'Phenytoin may affect warfarin metabolism'],
            ['drug1' => 'Theophylline', 'drug2' => 'Ciprofloxacin', 'severity' => 'High', 
             'description' => 'Ciprofloxacin increases theophylline levels'],
            ['drug1' => 'Cyclosporine', 'drug2' => 'Ketoconazole', 'severity' => 'High', 
             'description' => 'Ketoconazole increases cyclosporine levels']
        ];
        
        foreach ($current_meds as $current_med) {
            foreach ($interactions as $interaction) {
                $interaction_found = false;
                $interaction_description = '';
                
                if ((stripos($current_med['generaldescription'], $interaction['drug1']) !== false && 
                     stripos($new_med['generaldescription'], $interaction['drug2']) !== false) ||
                    (stripos($current_med['generaldescription'], $interaction['drug2']) !== false && 
                     stripos($new_med['generaldescription'], $interaction['drug1']) !== false)) {
                    
                    $interaction_found = true;
                    $interaction_description = $interaction['description'];
                    $severity = $interaction['severity'];
                }
                
                if ($interaction_found) {
                    $alert_message = "Drug interaction detected: {$current_med['generaldescription']} and {$new_med['generaldescription']}. {$interaction_description}";
                    $priority = $severity === 'High' ? 'Critical' : 'High';
                    
                    createAlert($patientid, null, 'Interaction', $priority, $alert_message);
                }
            }
        }
        
    } catch (Exception $e) {
        error_log("Error checking drug interactions: " . $e->getMessage());
    }
}

// Function to update overdue monitoring status
function updateOverdueMonitoring() {
    global $conn;
    
    try {
        // Update overdue monitoring records
        $overdue_sql = "UPDATE patient_medication_monitoring 
                       SET monitoring_status = 'Overdue' 
                       WHERE monitoring_status = 'Active' 
                       AND next_monitoring_date < CURDATE()";
        
        $conn->exec($overdue_sql);
        
        // Create alerts for newly overdue monitoring
        $new_overdue_sql = "SELECT pmm.monitoring_id, pmm.patientid, pmm.next_monitoring_date,
                                  i.generaldescription as medication_name
                           FROM patient_medication_monitoring pmm
                           JOIN items i ON pmm.itemid = i.itemid
                           WHERE pmm.monitoring_status = 'Overdue'
                           AND pmm.monitoring_id NOT IN (
                               SELECT monitoring_id FROM medication_monitoring_alerts 
                               WHERE alert_type = 'Overdue' AND is_resolved = 0
                           )";
        
        $new_overdue_stmt = $conn->query($new_overdue_sql);
        $overdue_records = $new_overdue_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($overdue_records as $record) {
            $days_overdue = floor((strtotime('now') - strtotime($record['next_monitoring_date'])) / (60 * 60 * 24));
            $alert_message = "Monitoring for {$record['medication_name']} is $days_overdue days overdue";
            
            createAlert($record['patientid'], $record['monitoring_id'], 'Overdue', 'High', $alert_message);
        }
        
    } catch (Exception $e) {
        error_log("Error updating overdue monitoring: " . $e->getMessage());
    }
}

// Function to be called when a transaction is completed
function processTransactionForMonitoring($transaction_id) {
    global $conn;
    
    try {
        // Get transaction details
        $transaction_sql = "SELECT pt.patientid, ptd.itemid, ptd.doctorid
                          FROM pharmatransactions pt
                          JOIN pharmatransaction_details ptd ON pt.transaction_id = ptd.transaction_id
                          WHERE pt.transaction_id = ?";
        
        $transaction_stmt = $conn->prepare($transaction_sql);
        $transaction_stmt->execute([$transaction_id]);
        $transaction_details = $transaction_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($transaction_details as $detail) {
            // Check if monitoring is required for this medication
            checkAndCreateMonitoring($detail['itemid'], $detail['patientid'], $transaction_id, $detail['doctorid']);
            
            // Check for drug interactions
            checkDrugInteractions($detail['patientid'], $detail['itemid']);
        }
        
    } catch (Exception $e) {
        error_log("Error processing transaction for monitoring: " . $e->getMessage());
    }
}

// If called directly, update overdue monitoring
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    updateOverdueMonitoring();
    echo "Overdue monitoring status updated.\n";
}
?>
