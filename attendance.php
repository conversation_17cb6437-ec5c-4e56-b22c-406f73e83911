<?php
require_once 'database.php';

// Create employees table if not exists
try {
    $sql = "CREATE TABLE IF NOT EXISTS bio_employees (
        id INT AUTO_INCREMENT PRIMARY KEY,
        employee_id VARCHAR(50) UNIQUE,
        fullname VARCHAR(100),
        birthdate DATE,
        gender CHAR(1),
        contact <PERSON><PERSON>HA<PERSON>(20),
        email VARCHAR(100),
        department VARCHAR(50),
        position VARCHAR(100),
        employment_date DATE,
        photo LONGTEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $conn->exec($sql);

    // Process form submission
    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        $response = array();
        
        try {
            if (isset($_POST['add_employee'])) {
                // Validate required fields
                $required_fields = ['employee_id', 'fullname', 'birthdate', 'gender', 'contact', 'email', 'department', 'position', 'employment_date'];
                foreach ($required_fields as $field) {
                    if (empty($_POST[$field])) {
                        throw new Exception("All fields are required");
                    }
                }

                // Validate email format
                if (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
                    throw new Exception("Invalid email format");
                }

                // Check if employee_id already exists
                $check_stmt = $conn->prepare("SELECT COUNT(*) FROM bio_employees WHERE employee_id = ?");
                $check_stmt->execute([$_POST['employee_id']]);
                if ($check_stmt->fetchColumn() > 0) {
                    throw new Exception("Employee ID already exists");
                }

                $stmt = $conn->prepare("INSERT INTO bio_employees (employee_id, fullname, birthdate, gender, contact, email, department, position, employment_date, photo) 
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([
                    trim($_POST['employee_id']),
                    trim($_POST['fullname']),
                    $_POST['birthdate'],
                    $_POST['gender'],
                    trim($_POST['contact']),
                    trim($_POST['email']),
                    trim($_POST['department']),
                    trim($_POST['position']),
                    $_POST['employment_date'],
                    $_POST['photo'] ?? null
                ]);
                $response = array('success' => true, 'message' => 'Employee added successfully!');
            } elseif (isset($_POST['update_employee'])) {
                // Validate required fields for update
                $required_fields = ['employee_id', 'fullname', 'birthdate', 'gender', 'contact', 'email', 'department', 'position', 'employment_date'];
                foreach ($required_fields as $field) {
                    if (empty($_POST[$field])) {
                        throw new Exception("All fields are required");
                    }
                }

                // Validate email format
                if (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
                    throw new Exception("Invalid email format");
                }

                $stmt = $conn->prepare("UPDATE bio_employees SET 
                                    fullname = ?, birthdate = ?, gender = ?, 
                                    contact = ?, email = ?, department = ?, 
                                    position = ?, employment_date = ?, photo = ? 
                                    WHERE employee_id = ?");
                $stmt->execute([
                    trim($_POST['fullname']),
                    $_POST['birthdate'],
                    $_POST['gender'],
                    trim($_POST['contact']),
                    trim($_POST['email']),
                    trim($_POST['department']),
                    trim($_POST['position']),
                    $_POST['employment_date'],
                    $_POST['photo'] ?? null,
                    $_POST['employee_id']
                ]);
                
                if ($stmt->rowCount() === 0) {
                    throw new Exception("No changes were made or employee not found");
                }
                
                $response = array('success' => true, 'message' => 'Employee updated successfully!');
            }
        } catch(Exception $e) {
            $response = array('success' => false, 'message' => $e->getMessage());
        }
        
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }

    // Handle search request
    if (isset($_GET['search'])) {
        try {
            $search = '%' . trim($_GET['search']) . '%';
            $stmt = $conn->prepare("SELECT * FROM bio_employees 
                                  WHERE employee_id LIKE ? 
                                  OR fullname LIKE ? 
                                  OR department LIKE ? 
                                  OR position LIKE ?
                                  ORDER BY fullname ASC");
            $stmt->execute([$search, $search, $search, $search]);
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'data' => $results]);
            exit;
        } catch(PDOException $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
            exit;
        }
    }

} catch(PDOException $e) {
    header('Content-Type: application/json');
    echo json_encode(array('success' => false, 'message' => "Connection failed: " . $e->getMessage()));
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employee Attendance Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .table {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
        }
        .table thead {
            background-color: #0d6efd;
            color: white;
        }
        .btn-primary {
            background-color: #0d6efd;
            border: none;
            padding: 8px 20px;
            border-radius: 8px;
        }
        .btn-primary:hover {
            background-color: #0b5ed7;
        }
        .form-control {
            border-radius: 8px;
            border: 1px solid #ced4da;
            padding: 10px;
        }
        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
        .modal-content {
            border-radius: 15px;
            border: none;
        }
        .modal-header {
            background-color: #0d6efd;
            color: white;
            border-radius: 15px 15px 0 0;
        }
        .btn-close {
            filter: brightness(0) invert(1);
        }
        .photo-capture-container {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .camera-buttons {
            margin-top: 10px;
        }
        .camera-buttons button {
            margin: 0 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card mb-4">
            <div class="card-body">
                <h2 class="card-title text-primary mb-4">
                    <i class="fas fa-users me-2"></i>Employee Management
                </h2>
                
                <!-- Search Form -->
                <div class="row mb-4 align-items-center">
                    <div class="col">
                        <div class="input-group">
                            <span class="input-group-text bg-white">
                                <i class="fas fa-search text-primary"></i>
                            </span>
                            <input type="text" id="searchInput" class="form-control" 
                                   placeholder="Search employees by name, department, or position...">
                        </div>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addEmployeeModal">
                            <i class="fas fa-plus me-2"></i>Add Employee
                        </button>
                        <button class="btn btn-success ms-2" data-bs-toggle="modal" data-bs-target="#captureAttendanceModal">
                            <i class="fas fa-clock me-2"></i>Capture Attendance
                        </button>
                    </div>
                </div>
                <!-- Attendance Capture Modal -->
                <div class="modal fade" id="captureAttendanceModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-success text-white">
                                <h5 class="modal-title">
                                    <i class="fas fa-clock me-2"></i>Capture Attendance
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="attendanceForm" class="row g-3">
                                    <div class="col-12">
                                        <div class="input-group mb-3">
                                            <span class="input-group-text">
                                                <i class="fas fa-barcode"></i>
                                            </span>
                                            <input type="text" id="employeeIdInput" class="form-control" 
                                                   placeholder="Scan Employee ID or enter manually..." autofocus>
                                        </div>
                                    </div>
                                    
                                    <div class="col-12">
                                        <div class="photo-capture-container text-center">
                                            <video id="attendance-video" width="320" height="240" 
                                                   class="img-fluid mb-2 rounded" style="display:none;"></video>
                                            <canvas id="attendance-canvas" width="320" height="240" 
                                                    class="img-fluid mb-2 rounded" style="display:none;"></canvas>
                                            <img id="attendance-photo" src="" 
                                                 class="img-fluid mb-2 rounded" style="display:none;">
                                            <input type="hidden" name="attendance_photo" id="attendance-photo-data">
                                            
                                            <div class="camera-buttons">
                                                <button type="button" class="btn btn-primary" id="start-camera">
                                                    <i class="fas fa-camera me-2"></i>Start Camera
                                                </button>
                                                <button type="button" class="btn btn-success" 
                                                        id="capture-attendance-photo" style="display:none;">
                                                    <i class="fas fa-camera-retro me-2"></i>Capture
                                                </button>
                                                <button type="button" class="btn btn-danger" 
                                                        id="retake-attendance-photo" style="display:none;">
                                                    <i class="fas fa-redo me-2"></i>Retake
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12 text-center">
                                        <button type="submit" class="btn btn-success btn-lg" id="saveAttendanceBtn" disabled>
                                            <i class="fas fa-save me-2"></i>Save Attendance
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Employee Table -->
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Employee ID</th>
                                <th>Full Name</th>
                                <th>Department</th>
                                <th>Position</th>
                                <th>Contact</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="employeeTableBody">
                            <!-- Table content will be dynamically populated -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Add Employee Modal -->
        <div class="modal fade" id="addEmployeeModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-user-plus me-2"></i>Add New Employee
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addEmployeeForm" class="row g-3">
                            <input type="hidden" name="add_employee" value="1">
                            
                            <div class="col-md-6">
                                <label class="form-label">Employee ID</label>
                                <input type="text" name="employee_id" class="form-control" required>
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label">Full Name</label>
                                <input type="text" name="fullname" class="form-control" required>
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label">Birthdate</label>
                                <input type="date" name="birthdate" class="form-control" required>
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label">Gender</label>
                                <select name="gender" class="form-select" required>
                                    <option value="">Select Gender</option>
                                    <option value="M">Male</option>
                                    <option value="F">Female</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label">Contact</label>
                                <input type="text" name="contact" class="form-control" required>
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label">Email</label>
                                <input type="email" name="email" class="form-control" required>
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label">Department</label>
                                <input type="text" name="department" class="form-control" required>
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label">Position</label>
                                <input type="text" name="position" class="form-control" required>
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label">Employment Date</label>
                                <input type="date" name="employment_date" class="form-control" required>
                            </div>
                            
                            <div class="col-12">
                                <label class="form-label">Photo Capture</label>
                                <div class="photo-capture-container text-center">
                                    <video id="video" width="320" height="240" class="img-fluid mb-2 rounded" style="display:none;"></video>
                                    <canvas id="canvas" width="320" height="240" class="img-fluid mb-2 rounded" style="display:none;"></canvas>
                                    <img id="photo-preview" src="" class="img-fluid mb-2 rounded" style="display:none;">
                                    <input type="hidden" name="photo" id="photo-data">
                                    <div class="camera-buttons">
                                        <button type="button" class="btn btn-primary" id="start-camera">
                                            <i class="fas fa-camera me-2"></i>Start Camera
                                        </button>
                                        <button type="button" class="btn btn-success" id="capture-photo" style="display:none;">
                                            <i class="fas fa-camera-retro me-2"></i>Capture
                                        </button>
                                        <button type="button" class="btn btn-danger" id="retake-photo" style="display:none;">
                                            <i class="fas fa-redo me-2"></i>Retake
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-12 text-end">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Employee
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Employee Modal -->
        <div class="modal fade" id="editEmployeeModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-user-edit me-2"></i>Edit Employee
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="editEmployeeForm" class="row g-3">
                            <input type="hidden" name="update_employee" value="1">
                            <input type="hidden" name="employee_id" id="edit_employee_id">
                            
                            <!-- Similar form fields as Add Employee Modal -->
                            <div class="col-12">
                                <label class="form-label">Photo</label>
                                <div class="photo-capture-container text-center">
                                    <video id="edit-video" width="320" height="240" class="img-fluid mb-2 rounded" style="display:none;"></video>
                                    <canvas id="edit-canvas" width="320" height="240" class="img-fluid mb-2 rounded" style="display:none;"></canvas>
                                    <img id="edit-photo-preview" src="" class="img-fluid mb-2 rounded">
                                    <input type="hidden" name="photo" id="edit-photo-data">
                                    <div class="camera-buttons">
                                        <button type="button" class="btn btn-primary" id="edit-start-camera">
                                            <i class="fas fa-camera me-2"></i>Update Photo
                                        </button>
                                        <button type="button" class="btn btn-success" id="edit-capture-photo" style="display:none;">
                                            <i class="fas fa-camera-retro me-2"></i>Capture
                                        </button>
                                        <button type="button" class="btn btn-danger" id="edit-retake-photo" style="display:none;">
                                            <i class="fas fa-redo me-2"></i>Retake
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-12 text-end">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Update Employee
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        $(document).ready(function() {
            let stream = null;
            
            // Camera functions for Add Employee
            $('#start-camera').click(function() {
                if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                    navigator.mediaDevices.getUserMedia({ video: true })
                        .then(function(mediaStream) {
                            stream = mediaStream;
                            const video = document.querySelector('#video');
                            video.srcObject = mediaStream;
                            video.play();
                            $('#video').show();
                            $('#capture-photo').show();
                            $('#start-camera').hide();
                        })
                        .catch(function(err) {
                            Swal.fire({
                                icon: 'error',
                                title: 'Camera Error',
                                text: 'Failed to access camera: ' + err.message
                            });
                        });
                }
            });

            $('#capture-photo').click(function() {
                const canvas = document.querySelector('#canvas');
                const video = document.querySelector('#video');
                canvas.getContext('2d').drawImage(video, 0, 0, canvas.width, canvas.height);
                const imageData = canvas.toDataURL('image/jpeg');
                $('#photo-data').val(imageData);
                $('#photo-preview').attr('src', imageData).show();
                $('#video').hide();
                $('#canvas').hide();
                $('#capture-photo').hide();
                $('#retake-photo').show();
                if (stream) {
                    stream.getTracks().forEach(track => track.stop());
                }
            });

            $('#retake-photo').click(function() {
                $('#start-camera').show();
                $('#photo-preview').hide();
                $('#retake-photo').hide();
                $('#photo-data').val('');
            });

            // Camera functions for Edit Employee
            $('#edit-start-camera').click(function() {
                if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                    navigator.mediaDevices.getUserMedia({ video: true })
                        .then(function(mediaStream) {
                            stream = mediaStream;
                            const video = document.querySelector('#edit-video');
                            video.srcObject = mediaStream;
                            video.play();
                            $('#edit-video').show();
                            $('#edit-capture-photo').show();
                            $('#edit-start-camera').hide();
                            $('#edit-photo-preview').hide();
                        })
                        .catch(function(err) {
                            Swal.fire({
                                icon: 'error',
                                title: 'Camera Error',
                                text: 'Failed to access camera: ' + err.message
                            });
                        });
                }
            });

            $('#edit-capture-photo').click(function() {
                const canvas = document.querySelector('#edit-canvas');
                const video = document.querySelector('#edit-video');
                canvas.getContext('2d').drawImage(video, 0, 0, canvas.width, canvas.height);
                const imageData = canvas.toDataURL('image/jpeg');
                $('#edit-photo-data').val(imageData);
                $('#edit-photo-preview').attr('src', imageData).show();
                $('#edit-video').hide();
                $('#edit-canvas').hide();
                $('#edit-capture-photo').hide();
                $('#edit-retake-photo').show();
                if (stream) {
                    stream.getTracks().forEach(track => track.stop());
                }
            });

            $('#edit-retake-photo').click(function() {
                $('#edit-start-camera').show();
                $('#edit-photo-preview').hide();
                $('#edit-retake-photo').hide();
                $('#edit-photo-data').val('');
            });

            // Search functionality with debounce
            let searchTimeout;
            $('#searchInput').on('input', function() {
                clearTimeout(searchTimeout);
                const searchTerm = $(this).val();
                
                searchTimeout = setTimeout(function() {
                    if (searchTerm.length > 0) {
                        $.get('attendance.php', { search: searchTerm }, function(response) {
                            if (response.success) {
                                updateEmployeeTable(response.data);
                            }
                        });
                    }
                }, 300);
            });

            // Form submission handlers with improved UX
            $('#addEmployeeForm').on('submit', function(e) {
                e.preventDefault();
                Swal.fire({
                    title: 'Saving...',
                    text: 'Please wait while we save the employee data',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                        $.post('attendance.php', $(this).serialize())
                            .done(function(response) {
                                if (response.success) {
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Success!',
                                        text: response.message
                                    }).then(() => {
                                        $('#addEmployeeModal').modal('hide');
                                        location.reload();
                                    });
                                } else {
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Error!',
                                        text: response.message
                                    });
                                }
                            })
                            .fail(function() {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error!',
                                    text: 'Failed to save employee data'
                                });
                            });
                    }
                });
            });

            $('#editEmployeeForm').on('submit', function(e) {
                e.preventDefault();
                Swal.fire({
                    title: 'Updating...',
                    text: 'Please wait while we update the employee data',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                        $.post('attendance.php', $(this).serialize())
                            .done(function(response) {
                                if (response.success) {
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Success!',
                                        text: response.message
                                    }).then(() => {
                                        $('#editEmployeeModal').modal('hide');
                                        location.reload();
                                    });
                                } else {
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Error!',
                                        text: response.message
                                    });
                                }
                            })
                            .fail(function() {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error!',
                                    text: 'Failed to update employee data'
                                });
                            });
                    }
                });
            });

            function updateEmployeeTable(employees) {
                const tbody = $('#employeeTableBody');
                tbody.empty();
                if (employees.length === 0) {
                    tbody.append(`
                        <tr>
                            <td colspan="6" class="text-center text-muted py-4">
                                <i class="fas fa-search fa-3x mb-3"></i>
                                <p class="mb-0">No employees found matching your search criteria</p>
                            </td>
                        </tr>
                    `);
                } else {
                    employees.forEach(function(employee) {
                        tbody.append(`
                            <tr>
                                <td>${employee.employee_id}</td>
                                <td>${employee.fullname}</td>
                                <td>${employee.department}</td>
                                <td>${employee.position}</td>
                                <td>${employee.contact}</td>
                                <td>
                                    <button class="btn btn-warning btn-sm edit-employee" 
                                            data-employee='${JSON.stringify(employee)}'>
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                </td>
                            </tr>
                        `);
                    });
                }
            }
        });
    </script>
</body>
</html>
