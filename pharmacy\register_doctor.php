<?php
require_once '../database.php';

// Create table if not exists
$sql = "CREATE TABLE IF NOT EXISTS doctors (
    doctorid INT AUTO_INCREMENT PRIMARY KEY,
    doctorname VARCHAR(100) NOT NULL,
    alias VARCHAR(50),
    contactnumber VARCHAR(20) NOT NULL,
    ishospitaldoctor ENUM('Yes', 'No') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";
try {
    $conn->exec($sql);
} catch(PDOException $e) {
    die("Error creating table: " . $e->getMessage());
}

// INSERT
if (isset($_POST['submit'])) {
    try {
        $sql = "INSERT INTO doctors (doctorname, alias, contactnumber, ishospitaldoctor) 
                VALUES (?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $_POST['doctorname'],
            $_POST['alias'],
            $_POST['contactnumber'],
            $_POST['ishospitaldoctor']
        ]);
        header("Location: " . $_SERVER['PHP_SELF']);
        exit();
    } catch (Exception $e) {
        die("Error: " . $e->getMessage());
    }
}

// UPDATE
if (isset($_POST['update'])) {
    try {
        $sql = "UPDATE doctors SET 
                doctorname = ?,
                alias = ?,
                contactnumber = ?,
                ishospitaldoctor = ?
                WHERE doctorid = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $_POST['doctorname'],
            $_POST['alias'],
            $_POST['contactnumber'],
            $_POST['ishospitaldoctor'],
            $_POST['doctorid']
        ]);
        header("Location: " . $_SERVER['PHP_SELF']);
        exit();
    } catch (Exception $e) {
        die("Error: " . $e->getMessage());
    }
}

// DELETE
if (isset($_POST['delete'])) {
    $sql = "DELETE FROM doctors WHERE doctorid = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$_POST['doctorid']]);
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

// SEARCH
$search = isset($_GET['search']) ? $_GET['search'] : '';
$sql = "SELECT * FROM doctors 
        WHERE doctorname LIKE ? 
        OR alias LIKE ?
        OR contactnumber LIKE ? 
        ORDER BY created_at DESC";
$stmt = $conn->prepare($sql);
$searchTerm = "%$search%";
$stmt->execute([$searchTerm, $searchTerm, $searchTerm]);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Doctor Registration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 20px auto; }
    </style>
</head>
<body>
    <!-- Add/Edit Doctor Modal -->
    <div class="modal fade" id="doctorModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="modalTitle">Add New Doctor</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="doctorid" id="doctorid">
                        <div class="mb-3">
                            <label class="form-label">Doctor Name</label>
                            <input type="text" class="form-control" name="doctorname" id="doctorname" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Alias</label>
                            <input type="text" class="form-control" name="alias" id="alias">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Contact Number</label>
                            <input type="text" class="form-control" name="contactnumber" id="contactnumber" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Hospital Doctor?</label>
                            <select name="ishospitaldoctor" id="ishospitaldoctor" class="form-select" required>
                                <option value="Yes">Yes</option>
                                <option value="No">No</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary" name="submit" id="submitBtn">Add Doctor</button>
                        <button type="submit" class="btn btn-warning" name="update" id="updateBtn" style="display: none;">Update Doctor</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    Are you sure you want to delete this doctor?
                </div>
                <div class="modal-footer">
                    <form method="POST">
                        <input type="hidden" name="doctorid" id="deleteDoctorid">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="delete" class="btn btn-danger">Delete</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="card shadow-lg rounded-4 border-0">
            <div class="card-header bg-gradient bg-primary bg-opacity-25 border-0 py-4">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="mb-0 text-primary">
                        <i class="fas fa-user-md me-2"></i>Doctor Registration
                    </h3>
                    <div>
                        <button type="button" class="btn btn-primary btn-lg rounded-3 shadow-sm" data-bs-toggle="modal" data-bs-target="#doctorModal">
                            <i class="fas fa-plus me-2"></i>New Doctor
                        </button>
                        <a href="../pharmacy/pharmacydashboard.php" class="btn btn-outline-primary btn-lg rounded-3 shadow-sm ms-2">
                            <i class="fas fa-home me-2"></i>Homepage
                        </a>
                    </div>
                </div>
            </div>

            <div class="card-body p-4">
                <!-- Search Form -->
                <form method="GET" class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text border-0 bg-light">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control border-0 bg-light" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Search doctors...">
                        <button class="btn btn-primary" type="submit">Search</button>
                    </div>
                </form>

                <!-- Doctors Table -->
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="bg-light">
                            <tr>
                                <th><i class="fas fa-hashtag me-2"></i>ID</th>
                                <th><i class="fas fa-user-md me-2"></i>Doctor Name</th>
                                <th><i class="fas fa-tag me-2"></i>Alias</th>
                                <th><i class="fas fa-phone me-2"></i>Contact Number</th>
                                <th><i class="fas fa-hospital me-2"></i>Hospital Doctor</th>
                                <th><i class="fas fa-calendar me-2"></i>Created At</th>
                                <th><i class="fas fa-cog me-2"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($results as $row): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($row['doctorid']); ?></td>
                                <td><?php echo htmlspecialchars($row['doctorname']); ?></td>
                                <td><?php echo htmlspecialchars($row['alias']); ?></td>
                                <td><?php echo htmlspecialchars($row['contactnumber']); ?></td>
                                <td><?php echo htmlspecialchars($row['ishospitaldoctor']); ?></td>
                                <td><?php echo date('M d, Y', strtotime($row['created_at'])); ?></td>
                                <td>
                                    <button class="btn btn-warning btn-sm" onclick="editDoctor(<?php echo htmlspecialchars(json_encode($row)); ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger btn-sm" onclick="deleteDoctor(<?php echo $row['doctorid']; ?>)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    const doctorModal = new bootstrap.Modal(document.getElementById('doctorModal'));
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));

    function editDoctor(doctor) {
        document.getElementById('modalTitle').textContent = 'Edit Doctor';
        document.getElementById('doctorid').value = doctor.doctorid;
        document.getElementById('doctorname').value = doctor.doctorname;
        document.getElementById('alias').value = doctor.alias;
        document.getElementById('contactnumber').value = doctor.contactnumber;
        document.getElementById('ishospitaldoctor').value = doctor.ishospitaldoctor;
        document.getElementById('submitBtn').style.display = 'none';
        document.getElementById('updateBtn').style.display = 'block';
        doctorModal.show();
    }

    function deleteDoctor(doctorid) {
        document.getElementById('deleteDoctorid').value = doctorid;
        deleteModal.show();
    }

    // Reset modal when opening for new doctor
    document.querySelector('[data-bs-target="#doctorModal"]').addEventListener('click', () => {
        document.getElementById('modalTitle').textContent = 'Add New Doctor';
        document.getElementById('doctorid').value = '';
        document.getElementById('submitBtn').style.display = 'block';
        document.getElementById('updateBtn').style.display = 'none';
        document.querySelector('#doctorModal form').reset();
    });
    </script>
</body>
</html>
