<?php
require_once '../database.php';

// Create table if not exists
$sql = "CREATE TABLE IF NOT EXISTS pharmacategory (
    categoryid INT AUTO_INCREMENT PRIMARY KEY,
    categorydesc VARCHAR(255) NOT NULL,
    categoryremarks TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

try {
    $conn->exec($sql);
} catch(PDOException $e) {
    die("Error creating table: " . $e->getMessage());
}


// INSERT
if (isset($_POST['submit'])) {
    $sql = "INSERT INTO pharmacategory (categorydesc, categoryremarks) VALUES (?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $_POST['categorydesc'],
        $_POST['categoryremarks']
    ]);
}

// UPDATE
if (isset($_POST['update'])) {
    $sql = "UPDATE pharmacategory SET categorydesc=?, categoryremarks=? WHERE categoryid=?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $_POST['categorydesc'],
        $_POST['categoryremarks'],
        $_POST['categoryid']
    ]);
}

// SEARCH
$search = isset($_GET['search']) ? $_GET['search'] : '';
$sql = "SELECT * FROM pharmacategory WHERE 
        categorydesc LIKE ? OR categoryremarks LIKE ? 
        ORDER BY created_at DESC";
$stmt = $conn->prepare($sql);
$searchTerm = "%$search%";
$stmt->execute([$searchTerm, $searchTerm]);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pharmacy Categories</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 20px auto; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card shadow-lg rounded-4 border-0">
            <div class="card-header bg-gradient bg-primary bg-opacity-25 border-0 py-4 d-flex justify-content-between align-items-center">
                <h3 class="mb-0 text-primary">
                    <i class="fas fa-pills me-2"></i>Pharmacy Categories
                </h3>
                <div class="d-flex align-items-center">
                    <button type="button" class="btn btn-primary btn-lg rounded-3 shadow-sm" data-bs-toggle="modal" data-bs-target="#categoryModal">
                        <i class="fas fa-plus me-2"></i>New Category
                    </button>
                    <div class="ms-2">
                        <a href="../pharmacy/items.php" class="btn btn-outline-primary btn-lg rounded-3 shadow-sm">
                            <i class="fas fa-pills me-2"></i>Items
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                <!-- Search Form -->
                <form method="GET" class="mb-4">
                    <div class="input-group input-group-lg shadow-sm rounded-3 overflow-hidden">
                        <span class="input-group-text border-0 bg-light">
                            <i class="fas fa-search text-primary"></i>
                        </span>
                        <input type="text" class="form-control border-0 bg-light py-3" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Search categories...">
                        <button class="btn btn-primary px-4" type="submit">
                            <i class="fas fa-search-plus me-2"></i>Search
                        </button>
                    </div>
                </form>

                <!-- Categories Table -->
                <div class="table-responsive rounded-3 border">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="bg-light">
                            <tr>
                                <th><i class="fas fa-hashtag me-2"></i>ID</th>
                                <th><i class="fas fa-tag me-2"></i>Description</th>
                                <th><i class="fas fa-comment me-2"></i>Remarks</th>
                                <th><i class="fas fa-calendar me-2"></i>Created At</th>
                                <th><i class="fas fa-cog me-2"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($results as $row): ?>
                            <tr>
                                <td class="fw-bold text-primary"><?php echo htmlspecialchars($row['categoryid']); ?></td>
                                <td><?php echo htmlspecialchars($row['categorydesc']); ?></td>
                                <td><?php echo htmlspecialchars($row['categoryremarks']); ?></td>
                                <td><?php echo date('M d, Y', strtotime($row['created_at'])); ?></td>
                                <td>
                                    <button class="btn btn-warning btn-sm rounded-3" onclick="editCategory(<?php echo htmlspecialchars(json_encode($row)); ?>)">
                                        <i class="fas fa-edit me-1"></i>Edit
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Add/Edit -->
    <div class="modal fade" id="categoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="modalTitle">
                        <i class="fas fa-plus-circle me-2"></i>New Category
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="categoryForm" method="POST">
                    <input type="hidden" name="categoryid" id="categoryid">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-tag me-2"></i>Category Description
                            </label>
                            <input type="text" class="form-control" name="categorydesc" id="categorydesc" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-comment me-2"></i>Remarks
                            </label>
                            <textarea class="form-control" name="categoryremarks" id="categoryremarks" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Close
                        </button>
                        <button type="submit" class="btn btn-primary" id="submitBtn" name="submit">
                            <i class="fas fa-save me-2"></i>Save Category
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editCategory(data) {
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-edit me-2"></i>Edit Category';
            document.getElementById('categoryid').value = data.categoryid;
            document.getElementById('categorydesc').value = data.categorydesc;
            document.getElementById('categoryremarks').value = data.categoryremarks;
            
            document.getElementById('submitBtn').name = 'update';
            document.getElementById('submitBtn').innerHTML = '<i class="fas fa-save me-2"></i>Update Category';
            
            new bootstrap.Modal(document.getElementById('categoryModal')).show();
        }

        document.getElementById('categoryModal').addEventListener('hidden.bs.modal', function () {
            document.getElementById('categoryForm').reset();
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-plus-circle me-2"></i>New Category';
            document.getElementById('submitBtn').name = 'submit';
            document.getElementById('submitBtn').innerHTML = '<i class="fas fa-save me-2"></i>Save Category';
        });
    </script>
</body>
</html>
