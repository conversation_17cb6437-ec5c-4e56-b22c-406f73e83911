<?php
require_once '../database.php';
session_start();
$department = $_SESSION['department'];
$usertype = $_SESSION['type'];
// Check if user is not logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}
// Get employee name from database
$stmt = $conn->prepare("SELECT e.emp_name FROM employees e 
                       INNER JOIN users u ON u.emp_id = e.id 
                       WHERE u.user_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$employee = $stmt->fetch();
$_SESSION['emp_name'] = $employee['emp_name'] ?? 'Unknown Employee';


// Create table if not exists
$sql = "CREATE TABLE IF NOT EXISTS return_to_supplier (
    return_id INT AUTO_INCREMENT PRIMARY KEY,
    itemid INT NOT NULL,
    supplier VARCHAR(100) NOT NULL,
    lot_no VARCHAR(50),
    quantity_returned INT NOT NULL,
    unit_cost DECIMAL(10,2),
    return_reason TEXT NOT NULL,
    reference_doc VARCHAR(100),
    date_returned TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    rbid INT,
    FOREIGN KEY (itemid) REFERENCES items(itemid),
    FOREIGN KEY (rbid) REFERENCES returnbatch(rbid)
)";
try {
    $conn->exec($sql);
} catch(PDOException $e) {
    die("Error creating table: " . $e->getMessage());
}

// INSERT
if (isset($_POST['submit'])) {
    try {
        $sql = "INSERT INTO return_to_supplier (itemid, supplier, lot_no, quantity_returned, unit_cost, return_reason, reference_doc, rbid) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $_POST['itemid'],
            $_POST['supplier'],
            $_POST['lot_no'],
            $_POST['quantity_returned'],
            $_POST['unit_cost'],
            $_POST['return_reason'],
            $_POST['reference_doc'],
            $_GET['rbid']
        ]);
        header('Location: ' . $_SERVER['PHP_SELF'] . '?rbid=' . $_GET['rbid'] . '&success=1');
        exit;
    } catch(PDOException $e) {
        die("Error inserting data: " . $e->getMessage());
    }
}

// UPDATE
if (isset($_POST['update'])) {
    try {
        $sql = "UPDATE return_to_supplier 
                SET itemid = ?, 
                    supplier = ?, 
                    lot_no = ?, 
                    quantity_returned = ?, 
                    unit_cost = ?, 
                    return_reason = ?, 
                    reference_doc = ?,
                    rbid = ? 
                WHERE return_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $_POST['itemid'],
            $_POST['supplier'],
            $_POST['lot_no'],
            $_POST['quantity_returned'],
            $_POST['unit_cost'],
            $_POST['return_reason'],
            $_POST['reference_doc'],
            $_GET['rbid'],
            $_POST['return_id']
        ]);
        header('Location: ' . $_SERVER['PHP_SELF'] . '?rbid=' . $_GET['rbid'] . '&success=2');
        exit;
    } catch(PDOException $e) {
        die("Error updating data: " . $e->getMessage());
    }
}

$rbid = isset($_GET['rbid']) ? $_GET['rbid'] : '';

$sql = "SELECT r.*, rb.rbid, i.generaldescription, rb.remarks, rb.date_returned as batch_date,
               u.username, e.emp_name
        FROM return_to_supplier r 
        JOIN items i ON r.itemid = i.itemid 
        JOIN returnbatch rb ON r.rbid = rb.rbid
        LEFT JOIN users u ON rb.user_id = u.user_id
        LEFT JOIN employees e ON u.emp_id = e.id
        WHERE r.rbid = ?";

$stmt = $conn->prepare($sql);
$stmt->execute([$rbid]);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get items for dropdown
$itemsStmt = $conn->query("SELECT i.itemid, i.generaldescription, psl.lot_no, psl.supplier, psl.unit_cost 
                          FROM items i 
                          INNER JOIN pharmacy_stock_ledger psl ON i.itemid = psl.itemid 
                          ORDER BY i.generaldescription");
$items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Return to Supplier Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background-color: #f5f8ff;
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
        }
        .container { max-width: 1400px; margin: 25px auto; }
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 4px 25px rgba(0,0,0,0.1);
        }
        .card-header {
            background: linear-gradient(145deg, #2563eb, #1e40af);
            border-radius: 20px 20px 0 0;
            padding: 1.75rem;
        }
        .btn {
            border-radius: 10px;
            padding: 0.6rem 1.2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .table {
            margin-bottom: 0;
        }
        .table th {
            background-color: #f1f5f9;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
        }
        .table td {
            padding: 1.2rem 1rem;
            vertical-align: middle;
        }
        .form-control, .form-select {
            border-radius: 10px;
            padding: 0.8rem;
            border: 1px solid #e2e8f0;
        }
        .form-control:focus, .form-select:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .input-group {
            border-radius: 10px;
            overflow: hidden;
        }
        .modal-content {
            border-radius: 20px;
            border: none;
        }
        .modal-header {
            background: linear-gradient(145deg, #2563eb, #1e40af);
            color: white;
            border-radius: 20px 20px 0 0;
        }
        .badge {
            padding: 0.5em 1em;
            border-radius: 6px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h2 class="text-white mb-0 fw-bold">Return to Supplier Management</h2>
                    <div class="d-flex gap-2">
                        <a href="pharmacydashboard.php" class="btn btn-light fw-semibold"><i class="fas fa-home me-2"></i>Home</a>
                        <button class="btn btn-light fw-semibold" data-bs-toggle="modal" data-bs-target="#itemModal"><i class="fas fa-plus me-2"></i>Add Return</button>
                    </div>
                </div>
                <div class="d-flex align-items-center bg-white bg-opacity-10 rounded-3 p-2">
                    <i class="fas fa-barcode text-white me-2"></i>
                    <div class="d-flex justify-content-between align-items-center w-100">
                        <div>
                            <small class="text-white-50">Pharmacy Return Reference</small>
                            <h6 class="text-white mb-0"><i class="fas fa-prescription-bottle-alt me-1"></i><?php echo $rbid = isset($_GET['rbid']) ? $rbid : 'Not Assigned'; ?></h6>
                        </div>
                        <a href="print_return.php?rbid=<?php echo $rbid; ?>" target="_blank" class="btn btn-light fw-semibold">
                            <i class="fas fa-print me-2"></i>Print Transmittal
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                <!-- Table -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th><i class="fas fa-box me-2 text-primary"></i>Item</th>
                                <th><i class="fas fa-building me-2 text-primary"></i>Supplier</th>
                                <th><i class="fas fa-hashtag me-2 text-primary"></i>Lot No.</th>
                                <th><i class="fas fa-sort-numeric-up me-2 text-primary"></i>Quantity</th>
                                <th><i class="fas fa-dollar-sign me-2 text-primary"></i>Unit Cost</th>
                                <th><i class="fas fa-comment me-2 text-primary"></i>Reason</th>
                                <th><i class="fas fa-file me-2 text-primary"></i>Reference</th>
                                <th><i class="fas fa-tag me-2 text-primary"></i>Batch Ref</th>
                                <th><i class="fas fa-calendar me-2 text-primary"></i>Date</th>
                                <th><i class="fas fa-cog me-2 text-primary"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($results as $row): ?>
                            <tr>
                                <td class="fw-semibold"><?php echo htmlspecialchars($row['generaldescription']); ?></td>
                                <td><?php echo htmlspecialchars($row['supplier']); ?></td>
                                <td><span class="badge bg-light text-dark"><?php echo htmlspecialchars($row['lot_no']); ?></span></td>
                                <td><?php echo htmlspecialchars($row['quantity_returned']); ?></td>
                                <td><?php echo number_format($row['unit_cost'], 2); ?></td>
                                <td><?php echo htmlspecialchars($row['return_reason']); ?></td>
                                <td><?php echo htmlspecialchars($row['reference_doc']); ?></td>
                                <td><span class="badge bg-info"><?php echo htmlspecialchars($row['rbid'] ?? 'N/A'); ?></span></td>
                                <td><?php echo date('M d, Y', strtotime($row['date_returned'])); ?></td>
                                <td>
                                    <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#itemModal" onclick="editItem(<?php echo htmlspecialchars(json_encode($row)); ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Modal -->
    <div class="modal fade" id="itemModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">Add Return Item</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-4">
                    <form id="itemForm" method="POST">
                        <input type="hidden" name="return_id" id="return_id">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label text-primary fw-semibold">Item</label>
                                <select class="form-select shadow-sm" name="itemid" id="itemid" required>
                                    <option value="">Select Item</option>
                                    <?php foreach($items as $item): ?>
                                    <option value="<?php echo $item['itemid']; ?>" 
                                            data-supplier="<?php echo htmlspecialchars($item['supplier']); ?>"
                                            data-lot="<?php echo htmlspecialchars($item['lot_no']); ?>"
                                            data-cost="<?php echo htmlspecialchars($item['unit_cost']); ?>">
                                        <?php echo htmlspecialchars($item['generaldescription']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-primary fw-semibold">Supplier</label>
                                <input type="text" class="form-control shadow-sm" name="supplier" id="supplier" required>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label text-primary fw-semibold">Lot No.</label>
                                <input type="text" class="form-control shadow-sm" name="lot_no" id="lot_no">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label text-primary fw-semibold">Quantity Returned</label>
                                <input type="number" class="form-control shadow-sm" name="quantity_returned" id="quantity_returned" required>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label text-primary fw-semibold">Unit Cost</label>
                                <input type="number" step="0.01" class="form-control shadow-sm" name="unit_cost" id="unit_cost" required>
                            </div>
                            <div class="col-md-12">
                                <label class="form-label text-primary fw-semibold">Return Reason</label>
                                <textarea class="form-control shadow-sm" name="return_reason" id="return_reason" rows="3" required></textarea>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-primary fw-semibold">Reference Document</label>
                                <input type="text" class="form-control shadow-sm" name="reference_doc" id="reference_doc">
                            </div>
                        </div>
                        <div class="text-end mt-4">
                            <button type="button" class="btn btn-light me-2" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary" name="submit" id="submitBtn">
                                <i class="fas fa-save me-2"></i>Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const itemModal = new bootstrap.Modal(document.getElementById('itemModal'));
        
        document.getElementById('itemid').addEventListener('change', function() {
            const selected = this.options[this.selectedIndex];
            document.getElementById('supplier').value = selected.dataset.supplier || '';
            document.getElementById('lot_no').value = selected.dataset.lot || '';
            document.getElementById('unit_cost').value = selected.dataset.cost || '';
        });

        function editItem(item) {
            document.getElementById('return_id').value = item.return_id;
            document.getElementById('itemid').value = item.itemid;
            document.getElementById('supplier').value = item.supplier;
            document.getElementById('lot_no').value = item.lot_no;
            document.getElementById('quantity_returned').value = item.quantity_returned;
            document.getElementById('unit_cost').value = item.unit_cost;
            document.getElementById('return_reason').value = item.return_reason;
            document.getElementById('reference_doc').value = item.reference_doc;
            
            document.getElementById('modalTitle').textContent = 'Edit Return Item';
            document.getElementById('submitBtn').name = 'update';
            itemModal.show();
        }

        <?php if (isset($_GET['success'])): ?>
        Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: <?php echo $_GET['success'] == 1 ? "'Item added successfully!'" : "'Item updated successfully!'"; ?>,
            timer: 2000,
            showConfirmButton: false
        });
        <?php endif; ?>
    </script>
</body>
</html>
