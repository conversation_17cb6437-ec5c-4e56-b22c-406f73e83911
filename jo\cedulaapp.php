<?php
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json');

require_once '../database.php';

try {
    $sql = "SELECT c.*, p.jo_name 
            FROM cedula c 
            JOIN jo_profile p ON c.profile_id = p.profile_id";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    
    $cedula_records = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode($cedula_records);
} catch(PDOException $e) {
    echo json_encode(['error' => $e->getMessage()]);
}
$conn = null;
?>

