<?php
require_once '../database.php';

// Create ACPN table if it doesn't exist
$sql = "CREATE TABLE IF NOT EXISTS ACPN (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pabn_no VARCHAR(20),
    series_no VARCHAR(20),
    member_pin VARCHAR(20),
    patient_name VARCHAR(100),
    physician_name TEXT,
    confinement_start DATE,
    confinement_end DATE,
    caserate1_code VARCHAR(20),
    caserate1_gross DECIMAL(10,2),
    caserate2_code VARCHAR(20),
    caserate2_gross DECIMAL(10,2),
    others_code VARCHAR(20),
    others_gross DECIMAL(10,2),
    total_gross DECIMAL(10,2),
    total_wtax DECIMAL(10,2),
    total_hci DECIMAL(10,2),
    total_pf DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

try {
    $conn->exec($sql);
    // echo "ACPN table created successfully";
} catch(PDOException $e) {
    echo "Error creating table: " . $e->getMessage();
}


// INSERT
if (isset($_POST['submit'])) {
    try {
        $sql = "INSERT INTO ACPN (pabn_no, series_no, member_pin, patient_name, physician_name, 
                confinement_start, confinement_end, caserate1_code, caserate1_gross, 
                caserate2_code, caserate2_gross, others_code, others_gross, 
                total_gross, total_wtax, total_hci, total_pf) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        
        // Convert physician_name array to string if needed
        $physician_name = isset($_POST['physician_name']) ? implode(', ', $_POST['physician_name']) : '';
        
        $stmt->execute([
            $_POST['pabn_no'],
            $_POST['series_no'],
            $_POST['member_pin'],
            $_POST['patient_name'],
            $physician_name,
            $_POST['confinement_start'],
            $_POST['confinement_end'],
            $_POST['caserate1_code'],
            $_POST['caserate1_gross'],
            $_POST['caserate2_code'] ?: 'NONE',
            $_POST['caserate2_gross'] ?: 0,
            $_POST['others_code'] ?: 'NONE',
            $_POST['others_gross'] ?: 0,
            $_POST['total_gross'],
            $_POST['total_wtax'] ?: 0,
            $_POST['total_hci'],
            $_POST['total_pf']
        ]);
        header("Location: " . $_SERVER['PHP_SELF']);
        exit();
    } catch(PDOException $e) {
        die("Error inserting data: " . $e->getMessage());
    }
}

// UPDATE
if (isset($_POST['update'])) {
    try {
        $sql = "UPDATE ACPN SET 
                pabn_no = ?, series_no = ?, member_pin = ?, patient_name = ?, 
                physician_name = ?, confinement_start = ?, confinement_end = ?, 
                caserate1_code = ?, caserate1_gross = ?, caserate2_code = ?, 
                caserate2_gross = ?, others_code = ?, others_gross = ?, 
                total_gross = ?, total_wtax = ?, total_hci = ?, total_pf = ? 
                WHERE id = ?";
        $stmt = $conn->prepare($sql);

        // Define available doctors
        $allDoctors = [
            "DR. LUCILLE G. ROMINES, MD, FPCP",
            "DR. JONATHAN H. GO, MD"
        ];
        
        // Get selected physicians from POST or existing record
        $selectedPhysicians = isset($_POST['physician_name']) ? $_POST['physician_name'] : 
            (isset($row['physician_name']) ? explode(', ', $row['physician_name']) : []);
        
        $stmt->execute([
            $_POST['pabn_no'],
            $_POST['series_no'],
            $_POST['member_pin'],
            $_POST['patient_name'],
            $_POST['physician_name'] ?? '',
            $_POST['confinement_start'],
            $_POST['confinement_end'],
            $_POST['caserate1_code'],
            $_POST['caserate1_gross'],
            $_POST['caserate2_code'] ?: 'NONE',
            $_POST['caserate2_gross'] ?: 0,
            $_POST['others_code'] ?: 'NONE',
            $_POST['others_gross'] ?: 0,
            $_POST['total_gross'],
            $_POST['total_wtax'] ?: 0,
            $_POST['total_hci'],
            $_POST['total_pf'],
            $_POST['id']
        ]);
        header("Location: " . $_SERVER['PHP_SELF']);
        exit();
    } catch(PDOException $e) {
        die("Error updating data: " . $e->getMessage());
    }
}

// SEARCH
$search = isset($_GET['search']) ? $_GET['search'] : '';
$sql = "SELECT * FROM ACPN WHERE 
        pabn_no LIKE ? OR 
        series_no LIKE ? OR 
        member_pin LIKE ? OR 
        patient_name LIKE ? OR 
        physician_name LIKE ?
        ORDER BY created_at DESC";
$stmt = $conn->prepare($sql);
$searchTerm = "%$search%";
$stmt->execute([$searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm]);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ACPN Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container-fluid { padding: 20px; }
        .card { box-shadow: 0 0 15px rgba(0,0,0,0.1); }
        .input-form { height: calc(100vh - 40px); overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Left Side - Input Form -->
            <div class="col-md-4">
                <div class="card input-form">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0"><i class="fas fa-plus-circle me-2"></i>New ACPN Record</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="needs-validation" novalidate>
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label class="form-label">PABN No.</label>
                                    <input type="text" class="form-control" name="pabn_no" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Series No.</label>
                                    <input type="text" class="form-control" name="series_no" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Member PIN</label>
                                    <input type="text" class="form-control" name="member_pin" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Patient Name</label>
                                <input type="text" class="form-control" name="patient_name" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Physician Name</label>
                                <input type="text" class="form-control" name="physician_name" required>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Confinement Start</label>
                                    <input type="date" class="form-control" name="confinement_start" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Confinement End</label>
                                    <input type="date" class="form-control" name="confinement_end" required>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Case Rate 1 Code</label>
                                    <input type="text" class="form-control" name="caserate1_code" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Case Rate 1 Gross</label>
                                    <input type="number" step="0.01" class="form-control" name="caserate1_gross" required>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Case Rate 2 Code</label>
                                    <input type="text" class="form-control" name="caserate2_code">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Case Rate 2 Gross</label>
                                    <input type="number" step="0.01" class="form-control" name="caserate2_gross">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Others Code</label>
                                    <input type="text" class="form-control" name="others_code">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Others Gross</label>
                                    <input type="number" step="0.01" class="form-control" name="others_gross">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Total Gross</label>
                                    <input type="number" step="0.01" class="form-control" name="total_gross" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Total W/Tax</label>
                                    <input type="number" step="0.01" class="form-control" name="total_wtax">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Total HCI</label>
                                    <input type="number" step="0.01" class="form-control" name="total_hci" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Total PF</label>
                                    <input type="number" step="0.01" class="form-control" name="total_pf" required>
                                </div>
                            </div>

                            <button type="submit" name="submit" class="btn btn-primary w-100">
                                <i class="fas fa-save me-2"></i>Save Record
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Right Side - Table -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h3 class="mb-0">ACPN Records</h3>
                        <div class="search-box">
                            <form method="GET" class="d-flex">
                                <input type="text" class="form-control me-2" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="Search records...">
                                <button class="btn btn-light" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="bg-light">
                                    <tr>
                                        <th><i class="fas fa-hashtag me-2"></i>ID</th>
                                        <th><i class="fas fa-file-alt me-2"></i>PABN No.</th>
                                        <th><i class="fas fa-list-ol me-2"></i>Series No.</th>
                                        <th><i class="fas fa-id-card me-2"></i>Member PIN</th>
                                        <th><i class="fas fa-user me-2"></i>Patient Name</th>
                                        <th><i class="fas fa-user-md me-2"></i>Physician</th>
                                        <th><i class="fas fa-calendar me-2"></i>Confinement</th>
                                        <th><i class="fas fa-money-bill me-2"></i>Total</th>
                                        <th><i class="fas fa-cog me-2"></i>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($results as $row): ?>
                                    <tr>
                                        <td class="fw-bold text-primary"><?php echo htmlspecialchars($row['id']); ?></td>
                                        <td><?php echo htmlspecialchars($row['pabn_no']); ?></td>
                                        <td><?php echo htmlspecialchars($row['series_no']); ?></td>
                                        <td><?php echo htmlspecialchars($row['member_pin']); ?></td>
                                        <td><?php echo htmlspecialchars($row['patient_name']); ?></td>
                                        <td><?php echo htmlspecialchars($row['physician_name']); ?></td>
                                        <td><?php echo date('M d, Y', strtotime($row['confinement_start'])) . ' - ' . date('M d, Y', strtotime($row['confinement_end'])); ?></td>
                                        <td>₱<?php echo number_format($row['total_gross'], 2); ?></td>
                                        <td>
                                            <button class="btn btn-warning btn-sm" onclick="editRecord(<?php echo htmlspecialchars(json_encode($row)); ?>)">
                                                <i class="fas fa-edit me-1"></i>Edit
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Modal -->
    <div class="modal fade" id="editModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-warning">
                    <h5 class="modal-title">Edit ACPN Record</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <input type="hidden" name="id" id="edit_id">
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label class="form-label">PABN No.</label>
                                <input type="text" class="form-control" name="pabn_no" id="edit_pabn_no" required>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Series No.</label>
                                <input type="text" class="form-control" name="series_no" id="edit_series_no" required>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Member PIN</label>
                                <input type="text" class="form-control" name="member_pin" id="edit_member_pin" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Patient Name</label>
                            <input type="text" class="form-control" name="patient_name" id="edit_patient_name" required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Physician Name</label>
                            <input type="text" class="form-control" name="physician_name" id="edit_physician_name" required>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Confinement Start</label>
                                <input type="date" class="form-control" name="confinement_start" id="edit_confinement_start" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Confinement End</label>
                                <input type="date" class="form-control" name="confinement_end" id="edit_confinement_end" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Case Rate 1 Code</label>
                                <input type="text" class="form-control" name="caserate1_code" id="edit_caserate1_code" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Case Rate 1 Gross</label>
                                <input type="number" step="0.01" class="form-control" name="caserate1_gross" id="edit_caserate1_gross" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Case Rate 2 Code</label>
                                <input type="text" class="form-control" name="caserate2_code" id="edit_caserate2_code">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Case Rate 2 Gross</label>
                                <input type="number" step="0.01" class="form-control" name="caserate2_gross" id="edit_caserate2_gross">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Others Code</label>
                                <input type="text" class="form-control" name="others_code" id="edit_others_code">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Others Gross</label>
                                <input type="number" step="0.01" class="form-control" name="others_gross" id="edit_others_gross">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Total Gross</label>
                                <input type="number" step="0.01" class="form-control" name="total_gross" id="edit_total_gross" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Total W/Tax</label>
                                <input type="number" step="0.01" class="form-control" name="total_wtax" id="edit_total_wtax">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Total HCI</label>
                                <input type="number" step="0.01" class="form-control" name="total_hci" id="edit_total_hci" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Total PF</label>
                                <input type="number" step="0.01" class="form-control" name="total_pf" id="edit_total_pf" required>
                            </div>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="submit" name="update" class="btn btn-warning">Update Record</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
