<?php
require_once '../database.php';

// Get all items for the dropdown
$items_sql = "SELECT itemid, generaldescription FROM items ORDER BY generaldescription";
$items_stmt = $conn->prepare($items_sql);
$items_stmt->execute();
$items = $items_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get selected filters
$report_type = isset($_GET['report_type']) ? $_GET['report_type'] : 'daily';
$selected_date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01');
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-t');
$selected_item = isset($_GET['item_id']) ? $_GET['item_id'] : '';

// Prepare SQL based on report type
if ($report_type === 'daily') {
    $sql = "SELECT
        DATE(pt.transaction_date) AS transaction_date,
        i.generaldescription AS item_name,
        p.patientname,
        SUM(pd.quantity) AS quantity_utilized
    FROM pharmatransactions pt
    JOIN pharmatransaction_details pd ON pt.transaction_id = pd.transaction_id
    JOIN patient p ON pt.patientid = p.patientid
    JOIN items i ON pd.item_id = i.itemid
    WHERE pt.transaction_date BETWEEN :date AND :date_end " . 
    ($selected_item ? "AND i.itemid = :item_id " : "") . 
    "GROUP BY DATE(pt.transaction_date), i.generaldescription, p.patientname
    ORDER BY transaction_date, item_name, p.patientname";

    $stmt = $conn->prepare($sql);
    $date_end = $selected_date . ' 23:59:59';
    $stmt->bindParam(':date', $selected_date);
    $stmt->bindParam(':date_end', $date_end);
} else {
    $sql = "SELECT
        pt.transaction_date,
        i.generaldescription AS item_name,
        p.patientname,
        pd.quantity AS quantity_utilized
    FROM pharmatransactions pt
    JOIN pharmatransaction_details pd ON pt.transaction_id = pd.transaction_id
    JOIN patient p ON pt.patientid = p.patientid
    JOIN items i ON pd.item_id = i.itemid
    WHERE pt.transaction_date BETWEEN :start_date AND CONCAT(:end_date, ' 23:59:59') " . 
    ($selected_item ? "AND i.itemid = :item_id " : "") . 
    "ORDER BY pt.transaction_date DESC, item_name, p.patientname";

    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':start_date', $start_date);
    $stmt->bindParam(':end_date', $end_date);
}

if ($selected_item) {
    $stmt->bindParam(':item_id', $selected_item);
}
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Calculate total quantity utilized
$total_quantity = array_sum(array_column($results, 'quantity_utilized'));

// Get summary statistics
$stats = [
    'unique_patients' => count(array_unique(array_column($results, 'patientname'))),
    'unique_items' => count(array_unique(array_column($results, 'item_name'))),
    'avg_per_patient' => $total_quantity / (count(array_unique(array_column($results, 'patientname'))) ?: 1)
];

// Export functionality
if (isset($_GET['export']) && $_GET['export'] === 'excel') {
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment;filename="utilization_report.xls"');
    echo "<table border='1'>";
    echo "<tr><th>Date</th><th>Item Name</th><th>Patient Name</th><th>Quantity</th></tr>";
    
    foreach($results as $row) {
        echo "<tr>";
        echo "<td>" . date('F d, Y', strtotime($row['transaction_date'])) . "</td>";
        echo "<td>" . $row['item_name'] . "</td>";
        echo "<td>" . $row['patientname'] . "</td>";
        echo "<td>" . number_format($row['quantity_utilized']) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    exit();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pharmacy Utilization Report</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background-color: #f0f5ff;
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
        }
        .container { max-width: 1200px; margin: 20px auto; }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .card-header {
            background: linear-gradient(135deg, #0d6efd, #0099ff);
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e0e0e0;
            padding: 10px 15px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
        }
        .btn-primary {
            background: linear-gradient(135deg, #0d6efd, #0099ff);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
        }
        .table th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
        }
        .report-type-selector {
            margin-bottom: 20px;
        }
        .report-type-selector .btn {
            min-width: 120px;
        }
    </style>
</head>
<body>
      <div class="container">
        <div class="card shadow-lg">
            <div class="card-header py-4">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">
                        <i class="fas fa-pills me-2"></i>Pharmacy Utilization Report
                    </h3>
                    <div>
                        <a href="../pharmacy/pharmacydashboard.php" class="btn btn-light btn-lg rounded-3 me-2">
                            <i class="fas fa-home me-2"></i>Homepage
                        </a>
                        <a href="?export=excel<?php echo isset($_GET['item_id']) ? '&item_id=' . $_GET['item_id'] : ''; ?>" class="btn btn-success btn-lg rounded-3 me-2">
                            <i class="fas fa-file-excel me-2"></i>Export Excel
                        </a>

                    </div>
                </div>
            </div>

            <div class="card-body">
                <!-- Summary Statistics -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h5><i class="fas fa-users me-2"></i>Unique Patients</h5>
                                <h3><?php echo number_format($stats['unique_patients']); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h5><i class="fas fa-pills me-2"></i>Unique Items</h5>
                                <h3><?php echo number_format($stats['unique_items']); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h5><i class="fas fa-chart-line me-2"></i>Avg Per Patient</h5>
                                <h3><?php echo number_format($stats['avg_per_patient'], 2); ?></h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Report Type Selector -->
                <div class="report-type-selector text-center">
                    <div class="btn-group" role="group">
                        <input type="radio" class="btn-check" name="report_type" id="daily" value="daily" 
                            <?php echo $report_type === 'daily' ? 'checked' : ''; ?>>
                        <label class="btn btn-outline-primary" for="daily">Daily Report</label>

                        <input type="radio" class="btn-check" name="report_type" id="monthly" value="monthly"
                            <?php echo $report_type === 'monthly' ? 'checked' : ''; ?>>
                        <label class="btn btn-outline-primary" for="monthly">Monthly Report</label>
                    </div>
                </div>

                <!-- Filter Form -->
                <form method="GET" class="row g-3 mb-4">
                    <input type="hidden" name="report_type" value="<?php echo $report_type; ?>" id="report_type_input">
                    
                    <div class="col-md-4 daily-filter <?php echo $report_type === 'monthly' ? 'd-none' : ''; ?>">
                        <label class="form-label">
                            <i class="fas fa-calendar me-2"></i>Select Date
                        </label>
                        <input type="date" class="form-control" name="date" value="<?php echo $selected_date; ?>">
                    </div>

                    <div class="col-md-4 monthly-filter <?php echo $report_type === 'daily' ? 'd-none' : ''; ?>">
                        <label class="form-label">
                            <i class="fas fa-calendar me-2"></i>Start Date
                        </label>
                        <input type="date" class="form-control" name="start_date" value="<?php echo $start_date; ?>">
                    </div>

                    <div class="col-md-4 monthly-filter <?php echo $report_type === 'daily' ? 'd-none' : ''; ?>">
                        <label class="form-label">
                            <i class="fas fa-calendar me-2"></i>End Date
                        </label>
                        <input type="date" class="form-control" name="end_date" value="<?php echo $end_date; ?>">
                    </div>

                    <div class="col-md-4">
                        <label class="form-label">
                            <i class="fas fa-capsules me-2"></i>Select Medicine/Item
                        </label>
                        <select class="form-select" name="item_id">
                            <option value="">All Items</option>
                            <?php foreach($items as $item): ?>
                                <option value="<?php echo $item['itemid']; ?>" 
                                    <?php echo ($selected_item == $item['itemid']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($item['generaldescription']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>Filter
                        </button>
                    </div>
                </form>

                <div class="table-responsive">
                    <div style="height: 500px; overflow-y: auto;">
                        <table class="table table-hover align-middle">
                            <thead class="sticky-top bg-white">
                                <tr>
                                    <th><i class="fas fa-calendar me-2"></i>Date</th>
                                    <th><i class="fas fa-pills me-2"></i>Item Name</th>
                                    <th><i class="fas fa-user me-2"></i>Patient Name</th>
                                    <th><i class="fas fa-box me-2"></i>Quantity</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($results)): ?>
                                <tr>
                                    <td colspan="4" class="text-center py-4">
                                        <i class="fas fa-info-circle me-2"></i>No records found for the selected filters.
                                    </td>
                                </tr>
                                <?php else: ?>
                                    <?php foreach($results as $row): ?>
                                    <tr>
                                        <td><?php echo date('F d, Y', strtotime($row['transaction_date'])); ?></td>
                                        <td><?php echo htmlspecialchars($row['item_name']); ?></td>
                                        <td><?php echo htmlspecialchars($row['patientname']); ?></td>
                                        <td><?php echo number_format($row['quantity_utilized']); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Handle report type change
        document.querySelectorAll('input[name="report_type"]').forEach(radio => {
            radio.addEventListener('change', function() {
                document.getElementById('report_type_input').value = this.value;
                
                // Toggle visibility of filter fields
                if (this.value === 'daily') {
                    document.querySelectorAll('.daily-filter').forEach(el => el.classList.remove('d-none'));
                    document.querySelectorAll('.monthly-filter').forEach(el => el.classList.add('d-none'));
                } else {
                    document.querySelectorAll('.daily-filter').forEach(el => el.classList.add('d-none'));
                    document.querySelectorAll('.monthly-filter').forEach(el => el.classList.remove('d-none'));
                }
            });
        });
    </script>
</body>
</html>
