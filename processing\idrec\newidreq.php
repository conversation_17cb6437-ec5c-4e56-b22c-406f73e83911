<?php
require_once '../../database.php';

// INSERT
if (isset($_POST['submit'])) {
    $sql = "INSERT INTO idrequest (phicnum, memlast, memfirst, memmid, memext, membday, 
            memgender, dsubmitted, trasmittalnum, status) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $_POST['phicnum'], $_POST['memlast'], $_POST['memfirst'], $_POST['memmid'], $_POST['memext'],
        $_POST['membday'], $_POST['memgender'], $_POST['dsubmitted'], $_POST['trasmittalnum'], 
        $_POST['status']
    ]);
}

// UPDATE
if (isset($_POST['update'])) {
    // Handle empty date fields
    $membday = !empty($_POST['membday']) ? $_POST['membday'] : null;
    $dsubmitted = !empty($_POST['dsubmitted']) ? $_POST['dsubmitted'] : null;
    
    $sql = "UPDATE idrequest SET 
            phicnum=?, memlast=?, memfirst=?, memmid=?, memext=?, membday=?,
            memgender=?, dsubmitted=?, trasmittalnum=?, status=?
            WHERE id=?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $_POST['phicnum'], $_POST['memlast'], $_POST['memfirst'], $_POST['memmid'], $_POST['memext'],
        $membday, $_POST['memgender'], $dsubmitted, $_POST['trasmittalnum'], $_POST['status'],
        $_POST['id']
    ]);
}

// SEARCH
$search = isset($_GET['search']) ? $_GET['search'] : '';
$sql = "SELECT * FROM idrequest WHERE 
        phicnum LIKE ? OR memlast LIKE ? OR memfirst LIKE ?";
$stmt = $conn->prepare($sql);
$searchTerm = "%$search%";
$stmt->execute([$searchTerm, $searchTerm, $searchTerm]);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PhilHealth ID Request Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 20px auto; }
        .table-responsive { margin-top: 20px; }
        .modal-lg { max-width: 800px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card shadow-lg rounded-4 border-0">
            <div class="card-header bg-gradient bg-primary bg-opacity-25 border-0 py-4 d-flex justify-content-between align-items-center">
                <h3 class="mb-0 text-primary">
                    <i class="fas fa-id-card me-2"></i>PhilHealth ID Request Management
                </h3>
                <div class="d-flex align-items-center">
                    <button type="button" class="btn btn-primary btn-lg rounded-3 shadow-sm" data-bs-toggle="modal" data-bs-target="#memberModal">
                        <i class="fas fa-user-plus me-2"></i>New Request
                    </button>
                    <div class="ms-2">
                        <a href="../../index.php" class="btn btn-outline-primary btn-lg rounded-3 shadow-sm">
                            <i class="fas fa-home me-2"></i>Homepage
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                <form method="GET" class="mb-4">
                    <div class="input-group input-group-lg shadow-sm rounded-3 overflow-hidden">
                        <span class="input-group-text border-0 bg-light">
                            <i class="fas fa-search text-primary"></i>
                        </span>
                        <input type="text" class="form-control border-0 bg-light py-3" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Search by PHIC number or member name...">
                        <button class="btn btn-primary px-4" type="submit">
                            <i class="fas fa-search-plus me-2"></i>Search
                        </button>
                    </div>
                </form>

                <div class="table-responsive rounded-3 border">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="bg-light">
                            <tr>
                                <th><i class="fas fa-id-card me-2"></i>PHIC Number</th>
                                <th><i class="fas fa-user me-2"></i>Member Name</th>
                                <th><i class="fas fa-calendar me-2"></i>Birth Date</th>
                                <th><i class="fas fa-venus-mars me-2"></i>Gender</th>
                                <th><i class="fas fa-calendar-check me-2"></i>Date Submitted</th>
                                <th><i class="fas fa-file-alt me-2"></i>Transmittal #</th>
                                <th><i class="fas fa-tasks me-2"></i>Status</th>
                                <th><i class="fas fa-cog me-2"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($results as $row): ?>
                            <tr>
                                <td class="fw-bold text-primary"><?php echo htmlspecialchars($row['phicnum']); ?></td>
                                <td>
                                    <?php 
                                        echo htmlspecialchars($row['memlast'] . ', ' . $row['memfirst'] . ' ' . 
                                        $row['memmid'] . ' ' . $row['memext']); 
                                    ?>
                                </td>
                                <td><?php echo htmlspecialchars($row['membday']); ?></td>
                                <td><?php echo htmlspecialchars($row['memgender']); ?></td>
                                <td><?php 
                                    if (!empty($row['dsubmitted'])) {
                                        $submittedDate = new DateTime($row['dsubmitted']);
                                        $today = new DateTime();
                                        $diff = $today->diff($submittedDate);
                                        $daysPast = $diff->days;
                                        
                                        echo '<small class="d-block">';
                                        echo '<i class="fas fa-paper-plane me-1"></i>Submitted: ';
                                        if ($daysPast >= 60) {
                                            echo '<span class="text-danger">' . htmlspecialchars($row['dsubmitted']) . '</span>';
                                            echo ' <small class="text-danger">(' . $daysPast . ' days ago)</small>';
                                        } elseif ($daysPast >= 30) {
                                            echo '<span class="text-warning">' . htmlspecialchars($row['dsubmitted']) . '</span>';
                                            echo ' <small class="text-warning">(' . $daysPast . ' days ago)</small>';
                                        } else {
                                            echo htmlspecialchars($row['dsubmitted']);
                                            echo ' <small class="text-muted">(' . $daysPast . ' days ago)</small>';
                                        }
                                        echo '</small>';
                                    }
                                ?></td>
                                <td><?php echo htmlspecialchars($row['trasmittalnum']); ?></td>
                                <td>
                                    <span class="badge rounded-pill bg-<?php 
                                        echo $row['status'] == 'Pending' ? 'warning' : 
                                            ($row['status'] == 'Submitted' ? 'info' : 
                                            ($row['status'] == 'Completed' ? 'success' : 'secondary')); 
                                    ?>">
                                        <?php echo htmlspecialchars($row['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-primary" onclick="editMember(<?php echo htmlspecialchars(json_encode($row)); ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Modal -->
    <div class="modal fade" id="memberModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="modalTitle">
                        <i class="fas fa-user-plus me-2"></i>New ID Request
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="memberForm" method="POST" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <input type="hidden" name="id" id="member_id">
                        
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label class="form-label">PHIC Number</label>
                                <input type="text" class="form-control" name="phicnum" id="phicnum" required>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Last Name</label>
                                <input type="text" class="form-control" name="memlast" id="memlast" required>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">First Name</label>
                                <input type="text" class="form-control" name="memfirst" id="memfirst" required>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Middle Name</label>
                                <input type="text" class="form-control" name="memmid" id="memmid">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Extension</label>
                                <input type="text" class="form-control" name="memext" id="memext">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Birth Date</label>
                                <input type="date" class="form-control" name="membday" id="membday">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Gender</label>
                                <select class="form-select" name="memgender" id="memgender" required>
                                    <option value="">Select Gender</option>
                                    <option value="Male">Male</option>
                                    <option value="Female">Female</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Date Submitted</label>
                                <input type="date" class="form-control" name="dsubmitted" id="dsubmitted">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Transmittal Number</label>
                                <input type="text" class="form-control" name="trasmittalnum" id="trasmittalnum">
                            </div>
                            <div class="col-md-12">
                                <label class="form-label">Status</label>
                                <select class="form-select" name="status" id="status" required>
                                    <option value="Pending">Pending</option>
                                    <option value="Submitted">Submitted</option>
                                    <option value="Completed">Completed</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="submitBtn" name="submit">Save</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editMember(data) {
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-edit me-2"></i>Edit ID Request';
            document.getElementById('member_id').value = data.id;
            
            // Fill form fields
            document.getElementById('phicnum').value = data.phicnum;
            document.getElementById('memlast').value = data.memlast;
            document.getElementById('memfirst').value = data.memfirst;
            document.getElementById('memmid').value = data.memmid;
            document.getElementById('memext').value = data.memext;
            document.getElementById('membday').value = data.membday;
            document.getElementById('memgender').value = data.memgender;
            document.getElementById('dsubmitted').value = data.dsubmitted;
            document.getElementById('trasmittalnum').value = data.trasmittalnum;
            document.getElementById('status').value = data.status;
            
            document.getElementById('submitBtn').name = 'update';
            document.getElementById('submitBtn').innerHTML = 'Update';
            
            new bootstrap.Modal(document.getElementById('memberModal')).show();
        }

        document.getElementById('memberModal').addEventListener('hidden.bs.modal', function () {
            document.getElementById('memberForm').reset();
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-user-plus me-2"></i>New ID Request';
            document.getElementById('submitBtn').name = 'submit';
            document.getElementById('submitBtn').innerHTML = 'Save';
        });
    </script>
</body>
</html>
