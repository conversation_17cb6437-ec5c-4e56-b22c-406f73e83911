<?php
require_once '../database.php';

// INSERT
if (isset($_POST['submit'])) {
    $sql = "INSERT INTO discount (plast, pfirst, pmid, pext, admidate, disdate, distype) 
            VALUES (?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $_POST['plast'], $_POST['pfirst'], $_POST['pmid'], $_POST['pext'],
        $_POST['admidate'], $_POST['disdate'], $_POST['distype']
    ]);
}

// UPDATE
if (isset($_POST['update'])) {
    // Handle empty date fields
    $admidate = !empty($_POST['admidate']) ? $_POST['admidate'] : null;
    $disdate = !empty($_POST['disdate']) ? $_POST['disdate'] : null;
    
    $sql = "UPDATE discount SET 
            plast=?, pfirst=?, pmid=?, pext=?, 
            admidate=?, disdate=?, distype=?
            WHERE id=?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $_POST['plast'], $_POST['pfirst'], $_POST['pmid'], $_POST['pext'],
        $admidate, $disdate, $_POST['distype'], $_POST['id']
    ]);
}

// SEARCH
$search = isset($_GET['search']) ? $_GET['search'] : '';
$sql = "SELECT * FROM discount WHERE 
        plast LIKE ? OR pfirst LIKE ? OR 
        pmid LIKE ? ORDER BY created_at DESC";
$stmt = $conn->prepare($sql);
$searchTerm = "%$search%";
$stmt->execute([$searchTerm, $searchTerm, $searchTerm]);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Discount Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 20px auto; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0"><i class="fas fa-percent me-2"></i>Discount Management</h3>
            </div>
            <div class="card-body">
                <!-- Add New Discount Button -->
                <div class="mb-4">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#discountModal">
                        <i class="fas fa-plus me-2"></i>Add New Discount
                    </button>
                    <a href="../index.php" class="btn btn-outline-primary ms-2">
                        <i class="fas fa-home me-2"></i>Back to Homepage
                    </a>
                </div>

                <!-- Search Form -->
                <form method="GET" class="mb-4">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Search by name...">
                        <button class="btn btn-outline-primary" type="submit">Search</button>
                    </div>
                </form>

                <!-- Discount List -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Discount Code</th>
                                <th>Last Name</th>
                                <th>First Name</th>
                                <th>Middle Name</th>
                                <th>Extension</th>
                                <th>Admission Date</th>
                                <th>Discharge Date</th>
                                <th>Discount Type</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($results as $row): ?>
                            <tr>
                                <td class="fw-bold text-primary">
                                    <?php 
                                    $year = !empty($row['disdate']) 
                                        ? date('Y', strtotime($row['disdate'])) 
                                        : date('Y');
                                    
                                    $formattedId = sprintf('%s-%04d', $year, $row['id']);
                                    echo '<span class="badge bg-light text-primary border border-primary">' . 
                                         htmlspecialchars($formattedId) . 
                                         '</span>';
                                    ?>
                                </td>
                                <td><?php echo htmlspecialchars($row['plast']); ?></td>
                                <td><?php echo htmlspecialchars($row['pfirst']); ?></td>
                                <td><?php echo htmlspecialchars($row['pmid']); ?></td>
                                <td><?php echo htmlspecialchars($row['pext']); ?></td>
                                <td><?php echo htmlspecialchars($row['admidate']); ?></td>
                                <td><?php echo htmlspecialchars($row['disdate']); ?></td>
                                <td><?php echo htmlspecialchars($row['distype']); ?></td>
                                <td>
                                    <button class="btn btn-sm btn-info" onclick="editDiscount(<?php echo htmlspecialchars(json_encode($row)); ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modern Modal -->
    <div class="modal fade" id="discountModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content shadow">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="modalTitle">
                        <i class="fas fa-percent me-2"></i>Add New Discount
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="discountForm" method="POST" class="needs-validation" novalidate>
                    <input type="hidden" name="id" id="discount_id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label><i class="fas fa-user me-2"></i>Last Name</label>
                            <input type="text" class="form-control" name="plast" id="plast" required>
                        </div>
                        <div class="mb-3">
                            <label><i class="fas fa-user me-2"></i>First Name</label>
                            <input type="text" class="form-control" name="pfirst" id="pfirst" required>
                        </div>
                        <div class="mb-3">
                            <label><i class="fas fa-user me-2"></i>Middle Name</label>
                            <input type="text" class="form-control" name="pmid" id="pmid">
                        </div>
                        <div class="mb-3">
                            <label><i class="fas fa-user-tag me-2"></i>Extension</label>
                            <input type="text" class="form-control" name="pext" id="pext">
                        </div>
                        <div class="mb-3">
                            <label><i class="fas fa-calendar-plus me-2"></i>Admission Date</label>
                            <input type="date" class="form-control" name="admidate" id="admidate" required>
                        </div>
                        <div class="mb-3">
                            <label><i class="fas fa-calendar-minus me-2"></i>Discharge Date</label>
                            <input type="date" class="form-control" name="disdate" id="disdate">
                        </div>
                        <div class="mb-3">
                            <label><i class="fas fa-tags me-2"></i>Discount Type</label>
                            <select class="form-select" name="distype" id="distype" required>
                                <option value="Senior Citizen"><i class="fas fa-users"></i> Senior Citizen</option>
                                <option value="PWD"><i class="fas fa-wheelchair"></i> PWD</option>
                                <option value="Others"><i class="fas fa-star"></i> Others</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Close
                        </button>
                        <button type="submit" class="btn btn-primary" id="submitBtn" name="submit">
                            <i class="fas fa-save me-2"></i>Save
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editDiscount(data) {
            document.getElementById('modalTitle').textContent = 'Edit Discount';
            document.getElementById('discount_id').value = data.id;
            document.getElementById('plast').value = data.plast;
            document.getElementById('pfirst').value = data.pfirst;
            document.getElementById('pmid').value = data.pmid;
            document.getElementById('pext').value = data.pext;
            document.getElementById('admidate').value = data.admidate;
            document.getElementById('disdate').value = data.disdate;
            document.getElementById('distype').value = data.distype;
            
            document.getElementById('submitBtn').name = 'update';
            document.getElementById('submitBtn').textContent = 'Update';
            
            new bootstrap.Modal(document.getElementById('discountModal')).show();
        }

        document.getElementById('discountModal').addEventListener('hidden.bs.modal', function () {
            document.getElementById('discountForm').reset();
            document.getElementById('modalTitle').textContent = 'Add New Discount';
            document.getElementById('submitBtn').name = 'submit';
            document.getElementById('submitBtn').textContent = 'Save';
        });
    </script>
</body>
</html>
