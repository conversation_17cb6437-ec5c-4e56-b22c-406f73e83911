<?php
require_once 'database.php';

// Fetch necessary data for reports
$totalTransactions = $conn->query("SELECT COUNT(*) FROM transactions")->fetchColumn();
$totalAccounts = $conn->query("SELECT COUNT(*) FROM accounts")->fetchColumn();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Reports Dashboard - BIRI District Hospital</title>
    <link rel="icon" type="image/x-icon" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00b4d8;
            --secondary-color: #0077b6;
            --accent-color: #48cae4;
        }
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
        }
        .report-card {
            transition: transform 0.3s;
        }
        .report-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0">
                <i class="fa-solid fa-chart-line me-2 text-primary"></i>
                Reports Dashboard
            </h2>
            <a href="index.php" class="btn btn-outline-primary">
                <i class="fas fa-home me-2"></i>Back to Homepage
            </a>
        </div>
        <div class="row g-4">
            <!-- Financial Reports Card -->
            <div class="col-md-6 col-lg-4">
                <div class="card report-card shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-money-bill-wave me-2 text-success"></i>
                            Financial Reports
                        </h5>
                        <p class="card-text">View detailed financial statements, balance sheets, and income statements.</p>
                        <a href="financial_reports.php" class="btn btn-primary">Generate Report</a>
                    </div>
                </div>
            </div>
            <!-- Transaction Reports Card -->
            <div class="col-md-6 col-lg-4">
                <div class="card report-card shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-receipt me-2 text-info"></i>
                            Transaction Reports
                        </h5>
                        <p class="card-text">Analysis of all transactions with detailed breakdowns.</p>
                        <div class="mb-2">
                            <span class="badge bg-info">Total Transactions: <?php echo $totalTransactions; ?></span>
                        </div>
                        <a href="transaction_reports.php" class="btn btn-primary">Generate Report</a>
                    </div>
                </div>
            </div>
            <!-- Account Reports Card -->
            <div class="col-md-6 col-lg-4">
                <div class="card report-card shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-book me-2 text-warning"></i>
                            Account Reports
                        </h5>
                        <p class="card-text">Comprehensive account statements and balance reports.</p>
                        <div class="mb-2">
                            <span class="badge bg-warning text-dark">Total Accounts: <?php echo $totalAccounts; ?></span>
                        </div>
                        <a href="account_reports.php" class="btn btn-primary">Generate Report</a>
                    </div>
                </div>
            </div>
            <!-- Department Reports Card -->
            <div class="col-md-6 col-lg-4">
                <div class="card report-card shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-building me-2 text-danger"></i>
                            Department Reports
                        </h5>
                        <p class="card-text">Department-wise financial analysis and budget reports.</p>
                        <a href="department_reports.php" class="btn btn-primary">Generate Report</a>
                    </div>
                </div>
            </div>
            <!-- Audit Reports Card -->
            <div class="col-md-6 col-lg-4">
                <div class="card report-card shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-file-alt me-2 text-secondary"></i>
                            Audit Reports
                        </h5>
                        <p class="card-text">System audit logs and transaction verification reports.</p>
                        <a href="audit_reports.php" class="btn btn-primary">Generate Report</a>
                    </div>
                </div>
            </div>
            <!-- Custom Reports Card -->
            <div class="col-md-6 col-lg-4">
                <div class="card report-card shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-tools me-2 text-primary"></i>
                            Custom Reports
                        </h5>
                        <p class="card-text">Generate customized reports based on specific parameters.</p>
                        <a href="custom_reports.php" class="btn btn-primary">Create Report</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
