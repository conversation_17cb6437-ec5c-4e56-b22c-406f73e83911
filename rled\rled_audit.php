<?php
require_once 'databasehdbo.php';

// Create audit table if not exists
$sql = "CREATE TABLE IF NOT EXISTS audit (
    audit_id INT(10) NOT NULL AUTO_INCREMENT,
    audit_date DATE NOT NULL,
    audit_description TEXT NOT NULL,
    audit_status ENUM('Pending','In Progress','Completed') NULL DEFAULT 'Pending',
    date_completed DATE NULL DEFAULT NULL,
    created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (audit_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci";

$conn->exec($sql);

// INSERT
if (isset($_POST['submit'])) {
    $sql = "INSERT INTO audit (audit_date, audit_description, audit_status, date_completed) 
            VALUES (?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $date_completed = !empty($_POST['date_completed']) ? $_POST['date_completed'] : null;
    $stmt->execute([
        $_POST['audit_date'],
        $_POST['audit_description'],
        $_POST['audit_status'],
        $date_completed
    ]);
}

// UPDATE
if (isset($_POST['update'])) {
    $sql = "UPDATE audit SET 
            audit_date = ?, 
            audit_description = ?, 
            audit_status = ?, 
            date_completed = ?
            WHERE audit_id = ?";
    $stmt = $conn->prepare($sql);
    $date_completed = !empty($_POST['date_completed']) ? $_POST['date_completed'] : null;
    $stmt->execute([
        $_POST['audit_date'],
        $_POST['audit_description'],
        $_POST['audit_status'],
        $date_completed,
        $_POST['audit_id']
    ]);
}

// SEARCH
$search = isset($_GET['search']) ? $_GET['search'] : '';
$sql = "SELECT * FROM audit WHERE 
        audit_description LIKE ? OR audit_status LIKE ?
        ORDER BY created_at DESC";
$stmt = $conn->prepare($sql);
$searchTerm = "%$search%";
$stmt->execute([$searchTerm, $searchTerm]);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audit Records</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 20px auto; }
        .table-responsive { margin-top: 20px; }
        .modal-lg { max-width: 800px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card shadow-lg rounded-4 border-0">
            <div class="card-header bg-gradient bg-info bg-opacity-25 border-0 py-4 d-flex justify-content-between align-items-center">
                <h3 class="mb-0 text-primary">
                    <i class="fas fa-clipboard-list me-2"></i>Audit Records
                </h3>
                <div class="d-flex align-items-center">
                    <button type="button" class="btn btn-primary btn-lg rounded-3 shadow-sm" data-bs-toggle="modal" data-bs-target="#auditModal">
                        <i class="fas fa-plus me-2"></i>New Audit
                    </button>
                    <div class="ms-2">
                        <a href="../../index.php" class="btn btn-outline-primary btn-lg rounded-3 shadow-sm">
                            <i class="fas fa-home me-2"></i>Homepage
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                <form method="GET" class="mb-4">
                    <div class="input-group input-group-lg shadow-sm rounded-3 overflow-hidden">
                        <span class="input-group-text border-0 bg-light">
                            <i class="fas fa-search text-primary"></i>
                        </span>
                        <input type="text" class="form-control border-0 bg-light py-3" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Search audit records...">
                        <button class="btn btn-primary px-4" type="submit">
                            <i class="fas fa-search-plus me-2"></i>Search
                        </button>
                    </div>
                </form>

                <div class="table-responsive rounded-3 border">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="bg-light">
                            <tr>
                                <th><i class="fas fa-calendar me-2"></i>Audit Date</th>
                                <th><i class="fas fa-file-alt me-2"></i>Description</th>
                                <th><i class="fas fa-chart-line me-2"></i>Status</th>
                                <th><i class="fas fa-calendar-check me-2"></i>Date Completed</th>
                                <th><i class="fas fa-cog me-2"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($results as $row): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($row['audit_date']); ?></td>
                                <td><?php echo htmlspecialchars($row['audit_description']); ?></td>
                                <td>
                                    <span class="badge rounded-pill bg-<?php echo $row['audit_status'] == 'Pending' ? 'warning' : ($row['audit_status'] == 'In Progress' ? 'info' : 'success'); ?> px-3 py-2">
                                        <?php echo htmlspecialchars($row['audit_status']); ?>
                                    </span>
                                </td>
                                <td><?php echo $row['date_completed'] ? htmlspecialchars($row['date_completed']) : '-'; ?></td>
                                <td>
                                    <button class="btn btn-info btn-sm rounded-3" onclick="editAudit(<?php echo htmlspecialchars(json_encode($row)); ?>)">
                                        <i class="fas fa-pen-to-square me-1"></i> Edit
                                    </button>
                                    <a href="rled_audit_findings.php?audit_id=<?php echo $row['audit_id']; ?>" class="btn btn-primary btn-sm rounded-3">
                                        <i class="fas fa-list-check me-1"></i> Findings
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Add/Edit -->
    <div class="modal fade" id="auditModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="modalTitle">
                        <i class="fas fa-clipboard-list me-2"></i>New Audit Record
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="auditForm" method="POST" class="needs-validation" novalidate>
                    <input type="hidden" name="audit_id" id="audit_id">
                    <div class="modal-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">Audit Date</label>
                                <input type="date" class="form-control" name="audit_date" id="audit_date" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Status</label>
                                <select class="form-select" name="audit_status" id="audit_status" required>
                                    <option value="Pending">Pending</option>
                                    <option value="In Progress">In Progress</option>
                                    <option value="Completed">Completed</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Description</label>
                                <textarea class="form-control" name="audit_description" id="audit_description" rows="3" required></textarea>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Date Completed</label>
                                <input type="date" class="form-control" name="date_completed" id="date_completed">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Close
                        </button>
                        <button type="submit" class="btn btn-primary" id="submitBtn" name="submit">
                            <i class="fas fa-save me-2"></i>Save Record
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editAudit(data) {
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-edit me-2"></i>Edit Audit Record';
            document.getElementById('audit_id').value = data.audit_id;
            document.getElementById('audit_date').value = data.audit_date;
            document.getElementById('audit_description').value = data.audit_description;
            document.getElementById('audit_status').value = data.audit_status;
            document.getElementById('date_completed').value = data.date_completed;
            
            document.getElementById('submitBtn').name = 'update';
            document.getElementById('submitBtn').innerHTML = '<i class="fas fa-save me-2"></i>Update Record';
            
            new bootstrap.Modal(document.getElementById('auditModal')).show();
        }

        document.getElementById('auditModal').addEventListener('hidden.bs.modal', function () {
            document.getElementById('auditForm').reset();
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-clipboard-list me-2"></i>New Audit Record';
            document.getElementById('submitBtn').name = 'submit';
            document.getElementById('submitBtn').innerHTML = '<i class="fas fa-save me-2"></i>Save Record';
        });
    </script>
</body>
</html>

