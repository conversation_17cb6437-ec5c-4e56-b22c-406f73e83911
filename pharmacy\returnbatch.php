<?php
require_once '../database.php'; // Include database connection file
session_start();
$department = $_SESSION['department'];
$usertype = $_SESSION['type'];
// Get employee name from database
$stmt = $conn->prepare("SELECT e.emp_name FROM employees e 
                       INNER JOIN users u ON u.emp_id = e.id 
                       WHERE u.user_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$employee = $stmt->fetch();
$_SESSION['emp_name'] = $employee['emp_name'] ?? 'Unknown Employee';
// Check if user is not logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Create returnbatch table if not exists with user_id foreign key
$sql = "CREATE TABLE IF NOT EXISTS returnbatch (
    rbid INT AUTO_INCREMENT PRIMARY KEY,
    date_returned TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id INT,
    remarks TEXT,
    FOREIG<PERSON> KEY (user_id) REFERENCES users(user_id)
)";

try {
    $conn->exec($sql);
} catch(PDOException $e) {
    die("Error creating table: " . $e->getMessage());
}


// INSERT
if (isset($_POST['submit'])) {
    $sql = "INSERT INTO returnbatch (user_id, remarks) VALUES (?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $_SESSION['user_id'], // Use session user_id instead of POST
        $_POST['remarks']
    ]);
    header("Location: " . $_SERVER['PHP_SELF']); // Redirect to refresh the page
    exit();
}

// UPDATE
if (isset($_POST['update'])) {
    $sql = "UPDATE returnbatch SET remarks=? WHERE rbid=? AND user_id=?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $_POST['remarks'],
        $_POST['rbid'],
        $_SESSION['user_id'] // Add user check for security
    ]);
    header("Location: " . $_SERVER['PHP_SELF']); // Redirect to refresh the page
    exit();
}

// SEARCH - Load all results if no search term
$search = isset($_GET['search']) ? $_GET['search'] : '';
$searchTerm = '%' . $search . '%';
$sql = "SELECT rb.*, u.username 
        FROM returnbatch rb 
        LEFT JOIN users u ON rb.user_id = u.user_id 
        WHERE rb.remarks LIKE ? OR u.username LIKE ?
        ORDER BY rb.date_returned DESC";
$stmt = $conn->prepare($sql);
$stmt->execute([$searchTerm, $searchTerm]);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Return to Supplier Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 20px auto; }
    </style>
</head>
<body>
    <!-- Add/Edit Return Modal -->
    <div class="modal fade" id="returnModal" tabindex="-1" aria-labelledby="modalTitle" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="modalTitle">Add New Return</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" id="returnForm">
                    <div class="modal-body">
                        <input type="hidden" name="rbid" id="rbid">
                        <div class="mb-3">
                            <label class="form-label">User</label>
                            <input type="text" class="form-control" value="<?php echo htmlspecialchars($_SESSION['emp_name']); ?>" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Remarks</label>
                            <textarea class="form-control" name="remarks" id="remarks" required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary" name="submit" id="submitBtn">Add Return</button>
                        <button type="submit" class="btn btn-warning" name="update" id="updateBtn" style="display: none;">Update Return</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="card shadow-lg">
            <div class="card-header bg-primary text-white py-4">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">
                        <i class="fas fa-exchange-alt me-2"></i>Return to Supplier Management
                    </h3>
                    <div>
                        <button class="btn btn-light btn-lg" data-bs-toggle="modal" data-bs-target="#returnModal">
                            <i class="fas fa-plus me-2"></i>New Return
                        </button>
                        <a href="../pharmacy/pharmacydashboard.php" class="btn btn-outline-light btn-lg ms-2">
                            <i class="fas fa-home me-2"></i>Homepage
                        </a>
                    </div>
                </div>
            </div>

            <div class="card-body">
                <!-- Search Form -->
                <form method="GET" class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text border-0 bg-light">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control border-0 bg-light" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Search returns...">
                        <button class="btn btn-primary" type="submit">Search</button>
                    </div>
                </form>

                <!-- Returns Table -->
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="bg-light">
                            <tr>
                                <th><i class="fas fa-hashtag me-2"></i>ID</th>
                                <th><i class="fas fa-calendar me-2"></i>Date Returned</th>
                                <th><i class="fas fa-user me-2"></i>User</th>
                                <th><i class="fas fa-comment me-2"></i>Remarks</th>
                                <th><i class="fas fa-cog me-2"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($results as $row): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($row['rbid']); ?></td>
                                <td><?php echo date('M d, Y', strtotime($row['date_returned'])); ?></td>
                                <td><?php echo htmlspecialchars($row['username']); ?></td>
                                <td><?php echo htmlspecialchars($row['remarks']); ?></td>
                                <td>
                                    <button class="btn btn-warning btn-sm" onclick='editReturn(<?php echo json_encode($row); ?>)'>
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <a href="returnitems.php?rbid=<?php echo $row['rbid']; ?>" class="btn btn-primary btn-sm">
                                        <i class="fas fa-plus"></i> Add Items
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const returnModal = new bootstrap.Modal(document.getElementById('returnModal'));

        window.editReturn = function(returnData) {
            document.getElementById('modalTitle').textContent = 'Edit Return';
            document.getElementById('rbid').value = returnData.rbid;
            document.getElementById('remarks').value = returnData.remarks;
            document.getElementById('submitBtn').style.display = 'none';
            document.getElementById('updateBtn').style.display = 'block';
            returnModal.show();
        };

        // Reset modal when opening for new return
        document.querySelector('[data-bs-target="#returnModal"]').addEventListener('click', function() {
            document.getElementById('modalTitle').textContent = 'Add New Return';
            document.getElementById('rbid').value = '';
            document.getElementById('remarks').value = '';
            document.getElementById('submitBtn').style.display = 'block';
            document.getElementById('updateBtn').style.display = 'none';
        });
    });
    </script>
</body>
</html>
