<?php
require_once '../../database.php';
require_once '../../fpdf/fpdf.php';

// Get delivery ID from URL
$delivery_id = isset($_GET['delivery_id']) ? (int)$_GET['delivery_id'] : 0;

// Fetch delivery details
$stmt = $conn->prepare("SELECT * FROM oxygen_deliveries WHERE id = ?");
$stmt->execute([$delivery_id]);
$delivery = $stmt->fetch();

// Fetch tanks for this delivery
$stmt = $conn->prepare("SELECT * FROM oxygen_tanks WHERE delivery_id = ?");
$stmt->execute([$delivery_id]);
$tanks = $stmt->fetchAll();

// Create PDF document
$pdf = new FPDF('P', 'mm', 'A4');
$pdf->AddPage();

// Add logos
$pdf->Image('../../images/pgns.png', 40, 10, 25);
$pdf->Image('../../images/bdh.png', 145, 10, 25);

// Header section
$pdf->SetFont('Times', '', 12);
$pdf->Cell(0, 6, 'Republic of the Philippines', 0, 1, 'C');
$pdf->Cell(0, 6, 'Province of Northern Samar', 0, 1, 'C');
$pdf->Cell(0, 6, 'Provincial Health Office', 0, 1, 'C');
$pdf->SetFont('Times', 'B', 16);
$pdf->Cell(0, 8, 'BIRI DISTRICT HOSPITAL', 0, 1, 'C');
$pdf->SetFont('Times', 'I', 11);
$pdf->Cell(0, 6, 'Biri Northern Samar', 0, 1, 'C');

// Report title
$pdf->Ln(10);
$pdf->SetFont('Times', 'B', 14);
$pdf->Cell(0, 10, 'OXYGEN DELIVERY REPORT', 0, 1, 'C');

// Delivery details
$pdf->SetFont('Times', 'B', 12);
$pdf->Cell(0, 10, 'Delivery Information:', 0, 1, 'L');
$pdf->SetFont('Times', '', 11);
$pdf->Cell(40, 6, 'Receipt No:', 0, 0);
$pdf->Cell(0, 6, $delivery['receipt_no'], 0, 1);
$pdf->Cell(40, 6, 'Date Delivered:', 0, 0);
$pdf->Cell(0, 6, date('F d, Y', strtotime($delivery['delivery_date'])), 0, 1);
$pdf->Cell(40, 6, 'Supplier:', 0, 0);
$pdf->Cell(0, 6, $delivery['supplier'], 0, 1);

// Tanks table header
$pdf->Ln(10);
$pdf->SetFont('Times', 'B', 11);
$pdf->Cell(60, 7, 'Serial Number', 1, 0, 'C');
$pdf->Cell(30, 7, 'Quantity', 1, 0, 'C');
$pdf->Cell(50, 7, 'Amount (PHP)', 1, 0, 'C');
$pdf->Cell(50, 7, 'Date Added', 1, 1, 'C');

// Tanks table content
$pdf->SetFont('Times', '', 11);
$totalAmount = 0;
foreach ($tanks as $tank) {
    $pdf->Cell(60, 7, $tank['serial_number'], 1, 0, 'L');
    $pdf->Cell(30, 7, $tank['quantity'], 1, 0, 'C');
    $pdf->Cell(50, 7, 'PHP ' . number_format($tank['amount'], 2), 1, 0, 'R');
    $pdf->Cell(50, 7, date('Y-m-d', strtotime($tank['created_at'])), 1, 1, 'C');
    $totalAmount += ($tank['amount'] * $tank['quantity']);
}

// Total amount
$pdf->SetFont('Times', 'B', 11);
$pdf->Cell(90, 7, 'Total Amount:', 1, 0, 'R');
$pdf->Cell(100, 7, 'PHP ' . number_format($totalAmount, 2), 1, 1, 'R');

// Acceptance and received by section
$pdf->Ln(10);
$pdf->SetFont('Times', 'B', 11);

// Create table for acceptance section
$pdf->Cell(190, 7, 'ACCEPTANCE AND RECEIVING DETAILS', 1, 1, 'C');

// Headers
$pdf->SetFont('Times', 'B', 11);
$pdf->Cell(95, 7, 'ACCEPTANCE', 1, 0, 'C');
$pdf->Cell(95, 7, 'RECEIVED BY', 1, 1, 'C');

// Dates
$pdf->SetFont('Times', '', 11);
$pdf->Cell(95, 7, 'Date: _________________', 1, 0, 'L');
$pdf->Cell(95, 7, 'Date Received: _________________', 1, 1, 'L');

// Delivery Status
$pdf->Cell(95, 7, 'O Complete  O Partial', 1, 0, 'L');
$pdf->Cell(95, 7, '', 1, 1, 'L');

// Signatures
$pdf->SetFont('Times', 'B', 11);
$pdf->Cell(95, 15, '', 1, 0, 'C'); // Added space for signature
$pdf->Cell(95, 15, '', 1, 1, 'C'); // Added space for signature
$pdf->Cell(95, 7, 'EDUARDO O. SABANGAN, JR', 1, 0, 'C');
$pdf->Cell(95, 7, 'LUZ M. FEROLINO, RN', 1, 1, 'C');

$pdf->SetFont('Times', '', 11);
$pdf->Cell(95, 7, 'Supply Officer', 1, 0, 'C');
$pdf->Cell(95, 7, 'Chief Nurse', 1, 1, 'C');

// Approved by section
$pdf->Ln(5);
$pdf->SetFont('Times', 'B', 11);
$pdf->Cell(190, 7, 'APPROVED BY:', 1, 1, 'C');
$pdf->Cell(190, 15, '', 1, 1, 'C'); // Space for signature
$pdf->Cell(190, 7, 'LUCILLE G. ROMINES, MD, FPCP', 1, 1, 'C');
$pdf->SetFont('Times', '', 11);
$pdf->Cell(190, 7, 'Chief of Hospital', 1, 1, 'C');

// Output PDF
$pdf->Output();
