<?php
require_once 'database.php';
// Initialize messages and session
session_start();
$successMessage = $errorMessage = "";

// Fetch all transactions with employee names
$transactions = $conn->query("SELECT t.*, e.emp_name 
    FROM transactions t
    JOIN employees e ON t.employee_id = e.id
    ORDER BY t.transaction_date DESC")->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Transaction List - BIRI District Hospital</title>
    <link rel="icon" type="image/x-icon" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #f5f7fa;
            font-family: 'Inter', sans-serif;
            color: #2d3748;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            padding: 2.5rem;
        }
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            padding: 2.5rem;
            margin-bottom: 2.5rem;
            background: white;
            transition: transform 0.2s ease;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .table {
            margin-bottom: 0;
            border-collapse: separate;
            border-spacing: 0 8px;
        }
        .table thead th {
            border: none;
            font-weight: 600;
            color: #4a5568;
            padding: 1.25rem 1rem;
            text-transform: uppercase;
            font-size: 0.875rem;
            letter-spacing: 0.05em;
        }
        .table tbody td {
            padding: 1.25rem 1rem;
            vertical-align: middle;
            border-top: 1px solid #edf2f7;
            background: white;
        }
        .table tbody tr {
            transition: all 0.2s ease;
        }
        .btn-group-action {
            display: flex;
            gap: 0.75rem;
            align-items: center;
        }
        .btn {
            border-radius: 10px;
            padding: 0.625rem 1.25rem;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #4f46e5, #4338ca);
            border: none;
        }
        .btn-warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            border: none;
            color: white;
        }
        .btn-danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            border: none;
        }
        .btn-info {
            background: linear-gradient(135deg, #0ea5e9, #0284c7);
            border: none;
            color: white;
        }
        .transaction-row {
            transition: all 0.2s ease;
            border-radius: 12px;
        }
        .transaction-row:hover {
            background-color: #f8fafc;
            transform: scale(1.01);
        }
        .fw-semibold {
            color: #1e293b;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0"><i class="fa-solid fa-list me-2"></i>Transaction List</h2>
            <a href="transactions.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>New Transaction
            </a>
        </div>
        <div class="card">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Transaction Ref</th>
                            <th>Date</th>
                            <th>Employee</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($transactions as $transaction): ?>
                            <tr class="transaction-row">
                                <td class="fw-semibold"><?php echo htmlspecialchars($transaction['transaction_ref']); ?></td>
                                <td><?php echo date('M d, Y', strtotime($transaction['transaction_date'])); ?></td>
                                <td><?php echo htmlspecialchars($transaction['emp_name']); ?></td>
                                <td>
                                    <div class="btn-group-action">
                                        <a href="view_transaction.php?id=<?php echo $transaction['transaction_id']; ?>" 
                                           class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        <a href="edit_transaction.php?id=<?php echo $transaction['transaction_id']; ?>" 
                                           class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <a href="modify_transaction.php?id=<?php echo $transaction['transaction_id']; ?>" 
                                           class="btn btn-sm btn-info">
                                            <i class="fas fa-cog"></i> Modify
                                        </a>
                                        <a href="delete_transaction.php?id=<?php echo $transaction['transaction_id']; ?>" 
                                           class="btn btn-sm btn-danger"
                                           onclick="return confirm('Are you sure you want to delete this transaction?');">
                                            <i class="fas fa-trash"></i> Delete
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
