<?php
require_once 'database.php';

// Get current year
$currentYear = date('Y');

// Get selected year or use current year as default
$selectedYear = isset($_GET['year']) ? $_GET['year'] : $currentYear;



// Fetch account summary data with transaction details
$accountSummary = $conn->query("
    SELECT 
        a.account_code,
        a.account_description,
        a.account_amount as budget,
        COALESCE(SUM(td.amount), 0) as utilized,
        (a.account_amount - COALESCE(SUM(td.amount), 0)) as remaining,
        COUNT(DISTINCT td.transaction_id) as transaction_count
    FROM accounts a
    LEFT JOIN transaction_details td ON a.account_code = td.account_code
    LEFT JOIN transactions t ON td.transaction_id = t.transaction_id AND YEAR(t.transaction_date) = $selectedYear
    WHERE a.account_year = $selectedYear
    GROUP BY a.account_code, a.account_description, a.account_amount")->fetchAll();

// Fetch department expenditure data with employee details
$departmentExpenditure = $conn->query("
    SELECT 
        d.department_name,
        COUNT(DISTINCT e.id) as employee_count,
        COUNT(DISTINCT t.transaction_id) as transaction_count,
        COALESCE(SUM(td.amount), 0) as total_spent,
        MAX(t.transaction_date) as last_transaction_date
    FROM departments d
    LEFT JOIN employees e ON d.id = e.department_id
    LEFT JOIN transactions t ON e.id = t.employee_id
    LEFT JOIN transaction_details td ON t.transaction_id = td.transaction_id
    WHERE YEAR(t.transaction_date) = $selectedYear OR t.transaction_date IS NULL
    GROUP BY d.department_name
    ORDER BY total_spent DESC")->fetchAll();

// Calculate total budget and utilization
$totalBudget = 0;
$totalUtilized = 0;
$totalTransactions = 0;
foreach ($accountSummary as $account) {
    $totalBudget += $account['budget'];
    $totalUtilized += $account['utilized'];
    $totalTransactions += $account['transaction_count'];
}
$utilizationRate = ($totalBudget > 0) ? ($totalUtilized / $totalBudget) * 100 : 0;

// Get total number of active accounts
$totalAccounts = $conn->query("SELECT COUNT(*) FROM accounts WHERE account_year = $selectedYear")->fetchColumn();

// Get department statistics
$totalDepartments = $conn->query("SELECT COUNT(*) FROM departments")->fetchColumn();
$totalEmployees = $conn->query("SELECT COUNT(*) FROM employees")->fetchColumn();


?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Reports - BIRI District Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --money-green: #2ecc71;
            --money-dark: #27ae60;
            --warning-yellow: #f1c40f;
            --danger-red: #e74c3c;
        }
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8fafb;
        }
        .progress {
            height: 20px;
            border-radius: 10px;
            background-color: #eef2f5;
        }
        .progress-bar {
            background-color: var(--money-green);
            border-radius: 10px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-title {
            color: #2c3e50;
            font-weight: 600;
        }
        .financial-metric {
            padding: 1.5rem;
            border-radius: 12px;
            background: linear-gradient(145deg, #ffffff, #f5f7fa);
        }
        .metric-title {
            color: #7f8c8d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--money-dark);
        }
        .table {
            border-collapse: separate;
            border-spacing: 0 8px;
        }
        .table thead th {
            border: none;
            color: #95a5a6;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.85rem;
        }
        .table tbody tr {
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
            border-radius: 10px;
        }
        .table tbody td {
            padding: 1rem;
            border: none;
            vertical-align: middle;
        }
        .btn-back {
            background-color: var(--money-dark);
            color: white;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s;
        }
        .btn-back:hover {
            background-color: var(--money-green);
            color: white;
            transform: translateX(-5px);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <h2 class="mb-4 d-flex align-items-center">
            <i class="fas fa-file-invoice-dollar me-3 text-primary"></i>
            <span>Account Reports <?php echo $selectedYear; ?></span>
        </h2>
        <!-- Year Selection -->
        <div id="yearSelector" class="year-selector mb-4">
            <div class="d-flex flex-wrap gap-2 justify-content-start">
                <?php
                for ($i = 0; $i < 5; $i++) {
                    $year = date('Y') - $i;
                    $activeClass = ($year == $selectedYear) ? 'active' : '';
                    echo "<a href='?year={$year}' name='year-btn-{$year}' id='yearBtn{$year}' class='btn btn-financial year-selector-btn {$activeClass}'>
                            <i class='fas fa-calendar-alt me-2'></i>
                            <span>FY {$year}</span>
                          </a>";
                }
                ?>
            </div>
        </div>
        <style>
            .year-selector-btn {
                min-width: 120px;
                padding: 0.75rem 1.25rem;
                border-radius: 10px;
                font-weight: 500;
                transition: all 0.3s ease;
                border: 2px solid var(--money-dark);
                color: var(--money-dark);
            }
            .year-selector-btn:hover {
                background-color: var(--money-dark);
                color: white;
                transform: translateY(-2px);
            }
            .year-selector-btn.active {
                background-color: var(--money-dark);
                color: white;
                box-shadow: 0 4px 12px rgba(39, 174, 96, 0.2);
            }
        </style>
        <!-- Summary Statistics -->
        <div class="row mb-4 g-3">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Total Accounts</h6>
                        <h2 class="card-title mb-0"><?php echo $totalAccounts; ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Departments</h6>
                        <h2 class="card-title mb-0"><?php echo $totalDepartments; ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Employees</h6>
                        <h2 class="card-title mb-0"><?php echo $totalEmployees; ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Transactions</h6>
                        <h2 class="card-title mb-0"><?php echo $totalTransactions; ?></h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Overview -->
        <div class="card mb-4">
            <div class="card-body">
                <h4 class="card-title">Financial Overview</h4>
                <div class="row g-4">
                    <div class="col-md-4">
                        <div class="financial-metric">
                            <h6 class="metric-title">Total Budget</h6>
                            <div class="metric-value">₱<?php echo number_format($totalBudget, 2); ?></div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="financial-metric">
                            <h6 class="metric-title">Total Utilized</h6>
                            <div class="metric-value text-warning">₱<?php echo number_format($totalUtilized, 2); ?></div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="financial-metric">
                            <h6 class="metric-title">Utilization Rate</h6>
                            <div class="progress mt-2">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: <?php echo $utilizationRate; ?>%; 
                                            background-color: <?php echo $utilizationRate > 90 ? 'var(--danger-red)' : 
                                                ($utilizationRate > 70 ? 'var(--warning-yellow)' : 'var(--money-green)'); ?>;"
                                     aria-valuenow="<?php echo $utilizationRate; ?>" 
                                     aria-valuemin="0" aria-valuemax="100">
                                    <?php echo number_format($utilizationRate, 1); ?>%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Details -->
        <div class="card mb-4">
            <div class="card-body">
                <h4 class="card-title">Account Details</h4>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Account Code</th>
                                <th>Description</th>
                                <th>Budget</th>
                                <th>Utilized</th>
                                <th>Remaining</th>
                                <th>Transactions</th>
                                <th>Progress</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($accountSummary as $account): ?>
                                <?php 
                                    $accountUtilization = ($account['budget'] > 0) 
                                        ? ($account['utilized'] / $account['budget']) * 100 
                                        : 0;
                                    $progressColor = $accountUtilization > 90 ? 'var(--danger-red)' : 
                                        ($accountUtilization > 70 ? 'var(--warning-yellow)' : 'var(--money-green)');
                                ?>
                                <tr>
                                    <td class="fw-bold"><?php echo htmlspecialchars($account['account_code']); ?></td>
                                    <td><?php echo htmlspecialchars($account['account_description']); ?></td>
                                    <td class="fw-semibold">₱<?php echo number_format($account['budget'], 2); ?></td>
                                    <td class="fw-semibold text-warning">₱<?php echo number_format($account['utilized'], 2); ?></td>
                                    <td class="fw-semibold text-success">₱<?php echo number_format($account['remaining'], 2); ?></td>
                                    <td class="text-center"><?php echo $account['transaction_count']; ?></td>
                                    <td style="width: 200px;">
                                        <div class="progress">
                                            <div class="progress-bar" role="progressbar"
                                                 style="width: <?php echo $accountUtilization; ?>%; background-color: <?php echo $progressColor; ?>;"
                                                 aria-valuenow="<?php echo $accountUtilization; ?>"
                                                 aria-valuemin="0" aria-valuemax="100">
                                                <?php echo number_format($accountUtilization, 1); ?>%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Department Analysis -->
        <div class="card mb-4">
            <div class="card-body">
                <h4 class="card-title">Department Analysis</h4>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Department</th>
                                <th>Employees</th>
                                <th>Transactions</th>
                                <th>Total Spent</th>
                                <th>Last Transaction</th>
                                <th>Budget Share</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($departmentExpenditure as $dept): ?>
                                <?php 
                                    $percentOfBudget = ($totalBudget > 0)
                                        ? ($dept['total_spent'] / $totalBudget) * 100 
                                        : 0;
                                ?>
                                <tr>
                                    <td class="fw-semibold"><?php echo htmlspecialchars($dept['department_name']); ?></td>
                                    <td><?php echo $dept['employee_count']; ?></td>
                                    <td><?php echo $dept['transaction_count']; ?></td>
                                    <td class="fw-bold text-success">₱<?php echo number_format($dept['total_spent'], 2); ?></td>
                                    <td><?php echo $dept['last_transaction_date'] ? date('M d, Y', strtotime($dept['last_transaction_date'])) : 'N/A'; ?></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="progress flex-grow-1 me-2">
                                                <div class="progress-bar" role="progressbar"
                                                     style="width: <?php echo $percentOfBudget; ?>%"
                                                     aria-valuenow="<?php echo $percentOfBudget; ?>"
                                                     aria-valuemin="0" aria-valuemax="100">
                                                </div>
                                            </div>
                                            <span class="fw-semibold"><?php echo number_format($percentOfBudget, 1); ?>%</span>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <a href="reports.php" class="btn btn-back">
                <i class="fas fa-arrow-left me-2"></i>Back to Reports
            </a>
            <a href="export_account_pdf.php?year=<?php echo $selectedYear; ?>" class="btn btn-back ms-2">
                <i class="fas fa-file-pdf me-2"></i>Export to PDF
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
