<?php
require_once '../database.php';
require_once '../fpdf/fpdf.php';

// Get parameters from URL - support both itemid and lot_no
$itemid = isset($_GET['itemid']) ? $_GET['itemid'] : '';
$lot_no = isset($_GET['lot_no']) ? $_GET['lot_no'] : '';

// Build query based on available parameters
if (!empty($itemid) && !empty($lot_no)) {
    // Filter by both itemid and lot_no
    $whereClause = "WHERE psl.itemid = ? AND psl.lot_no = ?";
    $params = [$itemid, $lot_no];
} elseif (!empty($itemid)) {
    // Filter by itemid only
    $whereClause = "WHERE psl.itemid = ?";
    $params = [$itemid];
} elseif (!empty($lot_no)) {
    // Filter by lot_no only
    $whereClause = "WHERE psl.lot_no = ?";
    $params = [$lot_no];
} else {
    // No filter - show error
    die('Error: Please provide either itemid or lot_no parameter');
}

// Get utilization details and stock ledger information
// Modified query to properly filter utilizations by lot_no
$sql = "WITH StockLedgerFiltered AS (
    SELECT DISTINCT
        psl.itemid,
        psl.lot_no,
        psl.expiry_date,
        psl.unit_cost,
        psl.selling_price,
        psl.qty_received,
        psl.cris_no,
        psl.beg_balance,
        psl.date_received,
        psl.ics_no,
        psl.supplier,
        i.generaldescription AS item_description,
        i.unitmeasure,
        pc.categorydesc
    FROM pharmacy_stock_ledger psl
    LEFT JOIN items i ON psl.itemid = i.itemid
    LEFT JOIN pharmacategory pc ON i.category = pc.categoryid
    $whereClause
),
UtilizationData AS (
    SELECT
        DATE(pt.transaction_date) as transaction_date,
        d.quantity as quantity_issued,
        slf.item_description,
        slf.unitmeasure,
        slf.categorydesc,
        slf.itemid,
        slf.lot_no,
        slf.expiry_date,
        slf.unit_cost,
        slf.selling_price,
        slf.qty_received,
        slf.cris_no,
        slf.beg_balance,
        slf.date_received,
        slf.ics_no,
        slf.supplier,
        p.patientname,
        p.patient_type,
        doc.doctorname,
        ROW_NUMBER() OVER (
            PARTITION BY slf.itemid, slf.lot_no
            ORDER BY pt.transaction_date
        ) as row_num
    FROM StockLedgerFiltered slf
    INNER JOIN pharmatransaction_details d ON d.itemid = slf.itemid
    INNER JOIN pharmatransactions pt ON d.transaction_id = pt.transaction_id
    LEFT JOIN patient p ON pt.patientid = p.patientid
    LEFT JOIN doctors doc ON d.doctorid = doc.doctorid
    WHERE pt.transaction_date IS NOT NULL
),
RunningBalance AS (
    SELECT *,
        (COALESCE(qty_received, 0) + COALESCE(beg_balance, 0)) -
        SUM(COALESCE(quantity_issued, 0)) OVER (
            PARTITION BY itemid, lot_no
            ORDER BY transaction_date, row_num
            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ) as running_balance
    FROM UtilizationData
)
SELECT * FROM RunningBalance
ORDER BY transaction_date ASC, row_num ASC";

$stmt = $conn->prepare($sql);
$stmt->execute($params);
$utilization = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Check if data exists
if (empty($utilization)) {
    header('Content-Type: text/html; charset=utf-8');
    echo '<div style="text-align: center; margin-top: 50px; font-family: Arial, sans-serif;">';
    echo '<h2 style="color: #dc3545;">No Utilization Data Found</h2>';

    if (!empty($lot_no) && !empty($itemid)) {
        echo '<p>No utilization data found for Item ID: <strong>' . htmlspecialchars($itemid) . '</strong> with Lot Number: <strong>' . htmlspecialchars($lot_no) . '</strong></p>';
    } elseif (!empty($lot_no)) {
        echo '<p>No utilization data found for Lot Number: <strong>' . htmlspecialchars($lot_no) . '</strong></p>';
        echo '<p><em>This could mean the lot number doesn\'t exist or has no recorded utilizations.</em></p>';
    } elseif (!empty($itemid)) {
        echo '<p>No utilization data found for Item ID: <strong>' . htmlspecialchars($itemid) . '</strong></p>';
    }

    echo '<div style="margin-top: 20px;">';
    echo '<p style="color: #6c757d; font-size: 14px;">Possible reasons:</p>';
    echo '<ul style="text-align: left; display: inline-block; color: #6c757d; font-size: 14px;">';
    echo '<li>The lot number may not exist in the system</li>';
    echo '<li>No medications from this lot have been dispensed yet</li>';
    echo '<li>The lot may be in stock but not yet utilized</li>';
    echo '</ul>';
    echo '</div>';

    echo '<a href="javascript:history.back()" style="text-decoration: none; background-color: #007bff; color: white; padding: 10px 20px; border-radius: 5px; margin-right: 10px;">Go Back</a>';
    echo '<a href="stock_ledger.php" style="text-decoration: none; background-color: #28a745; color: white; padding: 10px 20px; border-radius: 5px;">View Stock Ledger</a>';
    echo '</div>';
    exit;
}

// Create PDF with smaller margins to maximize space
$pdf = new FPDF('P', 'mm', 'A4');
$pdf->AddPage();
$pdf->SetMargins(8, 8, 8);

// Add header with smaller logos
$pdf->Image('../images/pgns.png', 15, 8, 20);
$pdf->Image('../images/bdh.png', 175, 8, 20);

// Add title and hospital information with smaller fonts
$pdf->SetFont('Arial', '', 9);
$pdf->Cell(0, 4, 'Republic of the Philippines', 0, 1, 'C');
$pdf->Cell(0, 4, 'Province of Northern Samar', 0, 1, 'C');
$pdf->Cell(0, 4, 'Provincial Health Office', 0, 1, 'C');
$pdf->SetFont('Arial', 'B', 12);
$pdf->Cell(0, 5, 'BIRI DISTRICT HOSPITAL', 0, 1, 'C');
$pdf->SetFont('Arial', 'I', 8);
$pdf->Cell(0, 4, 'Biri Northern Samar', 0, 1, 'C');
$pdf->SetFont('Arial', 'B', 11);
$pdf->Cell(0, 5, 'PHARMACY STOCK LEDGER', 0, 1, 'C');

// Add subtitle if filtering by lot number with smaller font
if (!empty($lot_no)) {
    $pdf->SetFont('Arial', 'B', 9);
    $pdf->SetTextColor(128, 0, 0); // Dark red color
    $pdf->Cell(0, 4, 'UTILIZATION REPORT - LOT NUMBER: ' . strtoupper($lot_no), 0, 1, 'C');
    $pdf->SetTextColor(0); // Reset to black
} elseif (!empty($itemid) && empty($lot_no)) {
    $pdf->SetFont('Arial', 'B', 9);
    $pdf->SetTextColor(0, 0, 128); // Dark blue color
    $pdf->Cell(0, 4, 'UTILIZATION REPORT - ITEM ID: ' . $itemid, 0, 1, 'C');
    $pdf->SetTextColor(0); // Reset to black
}

$pdf->Ln(3);

// Section Headers Style - more compact
$sectionHeaderStyle = function($pdf, $title) {
    $pdf->SetFillColor(28, 58, 95);
    $pdf->SetTextColor(255);
    $pdf->SetFont('Arial', 'B', 9);
    $pdf->Cell(194, 5, $title, 0, 1, 'L', true);
    $pdf->SetTextColor(0);
    $pdf->SetFont('Arial', '', 8);
    $pdf->Ln(1);
};

// Compact Item Information Section
$sectionHeaderStyle($pdf, ' Item Information');
$pdf->SetDrawColor(200, 200, 200);

// Three-column layout for more compact display
$col1Width = 65;
$col2Width = 65;
$col3Width = 64;

// First row
$pdf->Cell($col1Width, 4, 'Item: ' . substr($utilization[0]['item_description'], 0, 25) . (strlen($utilization[0]['item_description']) > 25 ? '...' : ''), 1, 0, 'L', false);
$pdf->Cell($col2Width, 4, 'ID: ' . $utilization[0]['itemid'], 1, 0, 'L', false);
$pdf->Cell($col3Width, 4, 'Category: ' . substr($utilization[0]['categorydesc'], 0, 20), 1, 1, 'L', false);

// Second row
$pdf->Cell($col1Width, 4, 'Unit: ' . $utilization[0]['unitmeasure'], 1, 0, 'L', false);
// Highlight lot number if filtering by lot_no
if (!empty($lot_no)) {
    $pdf->SetFillColor(255, 255, 0); // Yellow background
    $pdf->SetFont('Arial', 'B', 8);
    $pdf->Cell($col2Width, 4, 'Lot: ' . $utilization[0]['lot_no'], 1, 0, 'L', true);
    $pdf->SetFillColor(255, 255, 255); // Reset background
    $pdf->SetFont('Arial', '', 8);
} else {
    $pdf->Cell($col2Width, 4, 'Lot: ' . $utilization[0]['lot_no'], 1, 0, 'L', false);
}
$pdf->Cell($col3Width, 4, 'Expiry: ' . date('M d, Y', strtotime($utilization[0]['expiry_date'])), 1, 1, 'L', false);

$pdf->Ln(2);

// Compact Stock & Reference Information Section
$sectionHeaderStyle($pdf, ' Stock & Reference Information');

// First row - Financial info
$pdf->Cell($col1Width, 4, 'Cost: PHP ' . number_format($utilization[0]['unit_cost'], 2), 1, 0, 'L', false);
$pdf->Cell($col2Width, 4, 'Price: PHP ' . number_format($utilization[0]['selling_price'], 2), 1, 0, 'L', false);
$pdf->Cell($col3Width, 4, 'Qty Rcvd: ' . $utilization[0]['qty_received'], 1, 1, 'L', false);

// Second row - Reference info
$pdf->Cell($col1Width, 4, 'CRIS: ' . substr($utilization[0]['cris_no'], 0, 15), 1, 0, 'L', false);
$pdf->Cell($col2Width, 4, 'ICS: ' . substr($utilization[0]['ics_no'], 0, 15), 1, 0, 'L', false);
$pdf->Cell($col3Width, 4, 'Beg Bal: ' . $utilization[0]['beg_balance'], 1, 1, 'L', false);

// Third row - Dates and supplier
$pdf->Cell($col1Width, 4, 'Rcvd: ' . date('M d, Y', strtotime($utilization[0]['date_received'])), 1, 0, 'L', false);
$pdf->Cell(129, 4, 'Supplier: ' . substr($utilization[0]['supplier'], 0, 40) . (strlen($utilization[0]['supplier']) > 40 ? '...' : ''), 1, 1, 'L', false);

// Compact Utilization table
$pdf->Ln(2);
$pdf->SetFont('Arial', 'B', 8);
$pdf->Cell(0, 4, 'Utilization Details', 0, 1);

// Optimized table headers with smaller fonts and heights
$pdf->SetFillColor(240, 240, 240);
$pdf->SetFont('Arial', 'B', 7);
$pdf->Cell(28, 5, 'Date', 1, 0, 'C', true);
$pdf->Cell(45, 5, 'Doctor', 1, 0, 'C', true);
$pdf->Cell(50, 5, 'Patient Name', 1, 0, 'C', true);
$pdf->Cell(25, 5, 'Patient Type', 1, 0, 'C', true);
$pdf->Cell(12, 5, 'Qty', 1, 0, 'C', true);
$pdf->Cell(12, 5, 'Unit Price', 1, 0, 'C', true);
$pdf->Cell(12, 5, 'Subtotal', 1, 0, 'C', true);
$pdf->Cell(10, 5, 'Balance', 1, 1, 'C', true);

// Table content with smaller font and row height
$pdf->SetFont('Arial', '', 6);
$total_quantity = 0;
$total_amount = 0;

foreach ($utilization as $row) {
    // Truncate long names to fit
    $doctor_name = substr($row['doctorname'] ?? 'N/A', 0, 18);
    $patient_name = substr($row['patientname'] ?? 'N/A', 0, 20);
    $patient_type = substr($row['patient_type'] ?? 'N/A', 0, 10);

    $subtotal = ($row['quantity_issued'] ?? 0) * ($row['selling_price'] ?? 0);

    $pdf->Cell(28, 4, date('M d, y', strtotime($row['transaction_date'])), 1, 0, 'L');
    $pdf->Cell(45, 4, $doctor_name, 1, 0, 'L');
    $pdf->Cell(50, 4, $patient_name, 1, 0, 'L');
    $pdf->Cell(25, 4, $patient_type, 1, 0, 'L');
    $pdf->Cell(12, 4, number_format($row['quantity_issued'], 0), 1, 0, 'R');
    $pdf->Cell(12, 4, number_format($row['selling_price'] ?? 0, 1), 1, 0, 'R');
    $pdf->Cell(12, 4, number_format($subtotal, 1), 1, 0, 'R');
    $pdf->Cell(10, 4, number_format($row['running_balance'], 0), 1, 1, 'R');

    $total_quantity += $row['quantity_issued'];
    $total_amount += $subtotal;
}

// Print totals with styling - more compact
$pdf->SetFont('Arial', 'B', 7);
$pdf->SetFillColor(240, 240, 240);
$pdf->Cell(148, 5, 'TOTALS:', 1, 0, 'R', true);
$pdf->Cell(12, 5, number_format($total_quantity, 0), 1, 0, 'C', true);
$pdf->Cell(12, 5, '', 1, 0, 'C', true); // Empty cell for unit price
$pdf->Cell(12, 5, number_format($total_amount, 1), 1, 0, 'C', true);
$pdf->Cell(10, 5, '', 1, 1, 'C', true); // Empty cell for balance

// Add compact lot-specific summary if filtering by lot number
if (!empty($lot_no)) {
    $pdf->Ln(2);
    $sectionHeaderStyle($pdf, ' Lot Summary & Financial Analysis');

    // Calculate remaining balance and utilization percentage
    $initial_stock = $utilization[0]['qty_received'] + $utilization[0]['beg_balance'];
    $remaining_balance = end($utilization)['running_balance'];
    $utilization_percentage = $initial_stock > 0 ? (($total_quantity / $initial_stock) * 100) : 0;

    // Compact 4-column layout
    $pdf->SetFont('Arial', '', 7);
    $pdf->Cell(48.5, 4, 'Initial Stock: ' . number_format($initial_stock, 0), 1, 0, 'L', false);
    $pdf->Cell(48.5, 4, 'Total Used: ' . number_format($total_quantity, 0), 1, 0, 'L', false);
    $pdf->Cell(48.5, 4, 'Remaining: ' . number_format($remaining_balance, 0), 1, 0, 'L', false);
    $pdf->Cell(48.5, 4, 'Usage Rate: ' . number_format($utilization_percentage, 1) . '%', 1, 1, 'L', false);

    // Financial summary row
    $pdf->Cell(48.5, 4, 'Total Value: PHP ' . number_format($total_amount, 2), 1, 0, 'L', false);
    $pdf->Cell(48.5, 4, 'Avg Price: PHP ' . number_format($total_quantity > 0 ? $total_amount / $total_quantity : 0, 2), 1, 0, 'L', false);

    // Status indicator - more compact
    $status = '';
    $status_color = [0, 0, 0]; // Default black
    if ($remaining_balance <= 0) {
        $status = 'DEPLETED';
        $status_color = [255, 0, 0]; // Red
    } elseif ($utilization_percentage >= 80) {
        $status = 'LOW STOCK';
        $status_color = [255, 165, 0]; // Orange
    } elseif ($utilization_percentage >= 50) {
        $status = 'MODERATE USE';
        $status_color = [255, 255, 0]; // Yellow
    } else {
        $status = 'ADEQUATE STOCK';
        $status_color = [0, 128, 0]; // Green
    }

    $pdf->SetTextColor($status_color[0], $status_color[1], $status_color[2]);
    $pdf->SetFont('Arial', 'B', 7);
    $pdf->Cell(97, 4, 'Status: ' . $status, 1, 1, 'C', false);
    $pdf->SetTextColor(0, 0, 0); // Reset to black
}

// Add compact footer
$pdf->Ln(3);
$pdf->SetFont('Arial', 'I', 6);
$pdf->SetTextColor(128, 128, 128);
$pdf->Cell(0, 3, 'Generated on: ' . date('F d, Y \a\t g:i A') . ' | Total Records: ' . count($utilization) . ' | Report ID: ' . strtoupper(substr(md5($itemid . $lot_no . time()), 0, 8)), 0, 1, 'C');
$pdf->SetTextColor(0, 0, 0); // Reset to black

// Output PDF
$filename = 'item_utilization_' . $itemid;
if (!empty($lot_no)) {
    $filename .= '_lot_' . preg_replace('/[^a-zA-Z0-9]/', '_', $lot_no);
}
$filename .= '.pdf';
$pdf->Output('I', $filename);
