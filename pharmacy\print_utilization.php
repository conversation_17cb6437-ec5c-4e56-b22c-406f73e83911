<?php
require_once '../database.php';
require_once '../fpdf/fpdf.php';

// Get parameters from URL - support both itemid and lot_no
$itemid = isset($_GET['itemid']) ? $_GET['itemid'] : '';
$lot_no = isset($_GET['lot_no']) ? $_GET['lot_no'] : '';

// Build query based on available parameters
if (!empty($itemid) && !empty($lot_no)) {
    // Filter by both itemid and lot_no
    $whereClause = "WHERE psl.itemid = ? AND psl.lot_no = ?";
    $params = [$itemid, $lot_no];
} elseif (!empty($itemid)) {
    // Filter by itemid only
    $whereClause = "WHERE psl.itemid = ?";
    $params = [$itemid];
} elseif (!empty($lot_no)) {
    // Filter by lot_no only
    $whereClause = "WHERE psl.lot_no = ?";
    $params = [$lot_no];
} else {
    // No filter - show error
    die('Error: Please provide either itemid or lot_no parameter');
}

// Get utilization details and stock ledger information
// Modified query to properly filter utilizations by lot_no
$sql = "WITH StockLedgerFiltered AS (
    SELECT DISTINCT
        psl.itemid,
        psl.lot_no,
        psl.expiry_date,
        psl.unit_cost,
        psl.selling_price,
        psl.qty_received,
        psl.cris_no,
        psl.beg_balance,
        psl.date_received,
        psl.ics_no,
        psl.supplier,
        i.generaldescription AS item_description,
        i.unitmeasure,
        pc.categorydesc
    FROM pharmacy_stock_ledger psl
    LEFT JOIN items i ON psl.itemid = i.itemid
    LEFT JOIN pharmacategory pc ON i.category = pc.categoryid
    $whereClause
),
UtilizationData AS (
    SELECT
        DATE(pt.transaction_date) as transaction_date,
        d.quantity as quantity_issued,
        slf.item_description,
        slf.unitmeasure,
        slf.categorydesc,
        slf.itemid,
        slf.lot_no,
        slf.expiry_date,
        slf.unit_cost,
        slf.selling_price,
        slf.qty_received,
        slf.cris_no,
        slf.beg_balance,
        slf.date_received,
        slf.ics_no,
        slf.supplier,
        p.patientname,
        p.patient_type,
        doc.doctorname,
        ROW_NUMBER() OVER (
            PARTITION BY slf.itemid, slf.lot_no
            ORDER BY pt.transaction_date
        ) as row_num
    FROM StockLedgerFiltered slf
    INNER JOIN pharmatransaction_details d ON d.itemid = slf.itemid
    INNER JOIN pharmatransactions pt ON d.transaction_id = pt.transaction_id
    LEFT JOIN patient p ON pt.patientid = p.patientid
    LEFT JOIN doctors doc ON d.doctorid = doc.doctorid
    WHERE pt.transaction_date IS NOT NULL
),
RunningBalance AS (
    SELECT *,
        (COALESCE(qty_received, 0) + COALESCE(beg_balance, 0)) -
        SUM(COALESCE(quantity_issued, 0)) OVER (
            PARTITION BY itemid, lot_no
            ORDER BY transaction_date, row_num
            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ) as running_balance
    FROM UtilizationData
)
SELECT * FROM RunningBalance
ORDER BY transaction_date ASC, row_num ASC";

$stmt = $conn->prepare($sql);
$stmt->execute($params);
$utilization = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Check if data exists
if (empty($utilization)) {
    header('Content-Type: text/html; charset=utf-8');
    echo '<div style="text-align: center; margin-top: 50px; font-family: Arial, sans-serif;">';
    echo '<h2 style="color: #dc3545;">No Utilization Data Found</h2>';

    if (!empty($lot_no) && !empty($itemid)) {
        echo '<p>No utilization data found for Item ID: <strong>' . htmlspecialchars($itemid) . '</strong> with Lot Number: <strong>' . htmlspecialchars($lot_no) . '</strong></p>';
    } elseif (!empty($lot_no)) {
        echo '<p>No utilization data found for Lot Number: <strong>' . htmlspecialchars($lot_no) . '</strong></p>';
        echo '<p><em>This could mean the lot number doesn\'t exist or has no recorded utilizations.</em></p>';
    } elseif (!empty($itemid)) {
        echo '<p>No utilization data found for Item ID: <strong>' . htmlspecialchars($itemid) . '</strong></p>';
    }

    echo '<div style="margin-top: 20px;">';
    echo '<p style="color: #6c757d; font-size: 14px;">Possible reasons:</p>';
    echo '<ul style="text-align: left; display: inline-block; color: #6c757d; font-size: 14px;">';
    echo '<li>The lot number may not exist in the system</li>';
    echo '<li>No medications from this lot have been dispensed yet</li>';
    echo '<li>The lot may be in stock but not yet utilized</li>';
    echo '</ul>';
    echo '</div>';

    echo '<a href="javascript:history.back()" style="text-decoration: none; background-color: #007bff; color: white; padding: 10px 20px; border-radius: 5px; margin-right: 10px;">Go Back</a>';
    echo '<a href="stock_ledger.php" style="text-decoration: none; background-color: #28a745; color: white; padding: 10px 20px; border-radius: 5px;">View Stock Ledger</a>';
    echo '</div>';
    exit;
}

// Create PDF
$pdf = new FPDF('P', 'mm', 'A4');
$pdf->AddPage();
$pdf->SetMargins(10, 10, 10);

// Add header with logos
$pdf->Image('../images/pgns.png', 20, 10, 25);
$pdf->Image('../images/bdh.png', 170, 10, 25);

// Add title and hospital information
$pdf->SetFont('Arial', '', 12);
$pdf->Cell(0, 6, 'Republic of the Philippines', 0, 1, 'C');
$pdf->Cell(0, 6, 'Province of Northern Samar', 0, 1, 'C');
$pdf->Cell(0, 6, 'Provincial Health Office', 0, 1, 'C');
$pdf->SetFont('Arial', 'B', 16);
$pdf->Cell(0, 8, 'BIRI DISTRICT HOSPITAL', 0, 1, 'C');
$pdf->SetFont('Arial', 'I', 11);
$pdf->Cell(0, 6, 'Biri Northern Samar', 0, 1, 'C');
$pdf->SetFont('Arial', 'B', 14);
$pdf->Cell(0, 8, 'PHARMACY STOCK LEDGER', 0, 1, 'C');

// Add subtitle if filtering by lot number
if (!empty($lot_no)) {
    $pdf->SetFont('Arial', 'B', 12);
    $pdf->SetTextColor(128, 0, 0); // Dark red color
    $pdf->Cell(0, 6, 'UTILIZATION REPORT - LOT NUMBER: ' . strtoupper($lot_no), 0, 1, 'C');
    $pdf->SetTextColor(0); // Reset to black
} elseif (!empty($itemid) && empty($lot_no)) {
    $pdf->SetFont('Arial', 'B', 12);
    $pdf->SetTextColor(0, 0, 128); // Dark blue color
    $pdf->Cell(0, 6, 'UTILIZATION REPORT - ITEM ID: ' . $itemid, 0, 1, 'C');
    $pdf->SetTextColor(0); // Reset to black
}

$pdf->Ln(5);

// Section Headers Style
$sectionHeaderStyle = function($pdf, $title) {
    $pdf->SetFillColor(28, 58, 95);
    $pdf->SetTextColor(255);
    $pdf->SetFont('Arial', 'B', 11);
    $pdf->Cell(190, 7, $title, 0, 1, 'L', true);
    $pdf->SetTextColor(0);
    $pdf->SetFont('Arial', '', 10);
    $pdf->Ln(2);
};

// Item Information Section
$sectionHeaderStyle($pdf, ' Item Information');
$pdf->SetDrawColor(200, 200, 200);

// Two-column layout for item details
$leftWidth = 95;
$rightWidth = 95;

$pdf->Cell($leftWidth, 6, 'Item Name: ' . $utilization[0]['item_description'] . ' (Item ID: ' . $utilization[0]['itemid'] . ')', 1, 0, 'L', false);
$pdf->Cell($rightWidth, 6, 'Category: ' . $utilization[0]['categorydesc'], 1, 1, 'L', false);
$pdf->Cell($leftWidth, 6, 'Unit of Measure: ' . $utilization[0]['unitmeasure'], 1, 0, 'L', false);
// Highlight lot number if filtering by lot_no
if (!empty($lot_no)) {
    $pdf->SetFillColor(255, 255, 0); // Yellow background
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell($rightWidth, 6, 'Lot Number: ' . $utilization[0]['lot_no'], 1, 1, 'L', true);
    $pdf->SetFillColor(255, 255, 255); // Reset background
    $pdf->SetFont('Arial', '', 10);
} else {
    $pdf->Cell($rightWidth, 6, 'Lot Number: ' . $utilization[0]['lot_no'], 1, 1, 'L', false);
}

$pdf->Ln(5);

// Stock Information Section
$sectionHeaderStyle($pdf, ' Stock Information');

$pdf->Cell($leftWidth, 6, 'Expiry Date: ' . date('M d, Y', strtotime($utilization[0]['expiry_date'])), 1, 0, 'L', false);
$pdf->Cell($rightWidth, 6, 'Unit Cost: PHP ' . number_format($utilization[0]['unit_cost'], 2), 1, 1, 'L', false);
$pdf->Cell($leftWidth, 6, 'Selling Price: PHP ' . number_format($utilization[0]['selling_price'], 2), 1, 0, 'L', false);
$pdf->Cell($rightWidth, 6, 'Qty Received: ' . $utilization[0]['qty_received'], 1, 1, 'L', false);

$pdf->Ln(5);

// Reference Information Section
$sectionHeaderStyle($pdf, ' Reference Information');

$pdf->Cell($leftWidth, 6, 'CRIS No: ' . $utilization[0]['cris_no'], 1, 0, 'L', false);
$pdf->Cell($rightWidth, 6, 'ICS No: ' . $utilization[0]['ics_no'], 1, 1, 'L', false);
$pdf->Cell($leftWidth, 6, 'Date Received: ' . date('M d, Y', strtotime($utilization[0]['date_received'])), 1, 0, 'L', false);
$pdf->Cell($rightWidth, 6, 'Supplier: ' . $utilization[0]['supplier'], 1, 1, 'L', false);
$pdf->Cell(190, 6, 'Beginning Balance: ' . $utilization[0]['beg_balance'], 1, 1, 'L', false);

// Utilization table
$pdf->Ln(5);
$pdf->SetFont('Arial', 'B', 10);
$pdf->Cell(0, 5, 'Utilization Details', 0, 1);

// Table headers with improved styling
$pdf->SetFillColor(240, 240, 240);
$pdf->Cell(40, 8, 'Date', 1, 0, 'C', true);
$pdf->Cell(60, 8, 'Doctor', 1, 0, 'C', true);
$pdf->Cell(60, 8, 'Patient Name', 1, 0, 'C', true);
$pdf->Cell(15, 8, 'Qty', 1, 0, 'C', true);
$pdf->Cell(15, 8, 'Balance', 1, 1, 'C', true);

// Table content
$pdf->SetFont('Arial', '', 9);
$total_quantity = 0;

foreach ($utilization as $row) {
    $pdf->Cell(40, 6, date('M d, Y', strtotime($row['transaction_date'])), 1, 0, 'L');
    $pdf->Cell(60, 6, $row['doctorname'], 1, 0, 'L');
    $pdf->Cell(60, 6, $row['patientname'], 1, 0, 'L');
    $pdf->Cell(15, 6, number_format($row['quantity_issued'], 0), 1, 0, 'R');
    $pdf->Cell(15, 6, number_format($row['running_balance'], 0), 1, 1, 'R');
    $total_quantity += $row['quantity_issued'];
}

// Print total with styling
$pdf->SetFont('Arial', 'B', 10);
$pdf->SetFillColor(240, 240, 240);
$pdf->Cell(160, 8, 'Total Quantity Used:', 1, 0, 'R', true);
$pdf->Cell(30, 8, number_format($total_quantity, 0), 1, 1, 'C', true);

// Add lot-specific summary if filtering by lot number
if (!empty($lot_no)) {
    $pdf->Ln(3);
    $sectionHeaderStyle($pdf, ' Lot Number Summary');

    // Calculate remaining balance and utilization percentage
    $initial_stock = $utilization[0]['qty_received'] + $utilization[0]['beg_balance'];
    $remaining_balance = end($utilization)['running_balance'];
    $utilization_percentage = $initial_stock > 0 ? (($total_quantity / $initial_stock) * 100) : 0;

    $pdf->SetFont('Arial', '', 10);
    $pdf->Cell($leftWidth, 6, 'Initial Stock (Received + Beg. Balance): ' . number_format($initial_stock, 0), 1, 0, 'L', false);
    $pdf->Cell($rightWidth, 6, 'Total Utilized: ' . number_format($total_quantity, 0), 1, 1, 'L', false);
    $pdf->Cell($leftWidth, 6, 'Remaining Balance: ' . number_format($remaining_balance, 0), 1, 0, 'L', false);
    $pdf->Cell($rightWidth, 6, 'Utilization Rate: ' . number_format($utilization_percentage, 1) . '%', 1, 1, 'L', false);

    // Status indicator
    $status = '';
    $status_color = [0, 0, 0]; // Default black
    if ($remaining_balance <= 0) {
        $status = 'DEPLETED';
        $status_color = [255, 0, 0]; // Red
    } elseif ($utilization_percentage >= 80) {
        $status = 'LOW STOCK';
        $status_color = [255, 165, 0]; // Orange
    } elseif ($utilization_percentage >= 50) {
        $status = 'MODERATE USE';
        $status_color = [255, 255, 0]; // Yellow
    } else {
        $status = 'ADEQUATE STOCK';
        $status_color = [0, 128, 0]; // Green
    }

    $pdf->SetTextColor($status_color[0], $status_color[1], $status_color[2]);
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(190, 6, 'Stock Status: ' . $status, 1, 1, 'C', false);
    $pdf->SetTextColor(0, 0, 0); // Reset to black
}

$pdf->Ln(5);

// Output PDF
$filename = 'item_utilization_' . $itemid;
if (!empty($lot_no)) {
    $filename .= '_lot_' . preg_replace('/[^a-zA-Z0-9]/', '_', $lot_no);
}
$filename .= '.pdf';
$pdf->Output('I', $filename);
