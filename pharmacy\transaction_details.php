<?php
require_once '../database.php';
session_start();

// Check if transaction ID is provided in URL
if (!isset($_GET['id'])) {
    header('Location: transactions.php');
    exit();
}

// Create transaction details table if not exists (updated to use itemid)
$sql = "CREATE TABLE IF NOT EXISTS pharmatransaction_details (
    detail_id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_id INT,
    doctorid INT,
    itemid INT,
    quantity INT,
    unit_price DECIMAL(10,2),
    subtotal DECIMAL(10,2),
    original_price DECIMAL(10,2),
    discount_applied DECIMAL(10,2),
    FOREIGN KEY (transaction_id) REFERENCES pharmatransactions(transaction_id),
    FOREIGN KEY (itemid) REFERENCES items(itemid),
    FOREIGN KEY (doctorid) REFERENCES doctors(doctorid)
)";

try {
    $conn->exec($sql);
} catch(PDOException $e) {
    error_log("Error creating transaction details table: " . $e->getMessage());
}

// Drop item_no column and add itemid column if needed
try {
    // First, add itemid column if it doesn't exist
    $conn->exec("ALTER TABLE pharmatransaction_details ADD COLUMN itemid INT AFTER doctorid");
} catch (PDOException $e) {
    // Column might already exist, ignore error
}

try {
    // Drop the old item_no column if it exists
    $conn->exec("ALTER TABLE pharmatransaction_details DROP COLUMN item_no");
} catch (PDOException $e) {
    // Column might not exist, ignore error
}

// Get transaction details
$transactionId = $_GET['id'];
$sql = "SELECT t.*, p.patientname, p.patient_type 
        FROM pharmatransactions t 
        JOIN patient p ON t.patientid = p.patientid 
        WHERE t.transaction_id = ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$transactionId]);
$transaction = $stmt->fetch();

// Get all categories for filter
$categories = $conn->query("SELECT * FROM pharmacategory ORDER BY categorydesc")->fetchAll();

// Get all items with optional lot number search
$lotSearch = isset($_GET['lot_search']) ? trim($_GET['lot_search']) : '';

if (!empty($lotSearch)) {
    // Filter by lot number if search term is provided
    $itemsQuery = "SELECT i.*,psl.*, c.categorydesc, psl.expiry_date, psl.selling_price
                   FROM items i
                   JOIN pharmacategory c ON i.category = c.categoryid
                   JOIN pharmacy_stock_ledger psl ON i.itemid = psl.itemid
                   WHERE psl.lot_no LIKE ? OR  i.generaldescription LIKE ?
                   ORDER BY i.generaldescription, psl.expiry_date";
    $stmt = $conn->prepare($itemsQuery);
    $stmt->execute(['%' . $lotSearch . '%', '%' . $lotSearch . '%']);
    $items = $stmt->fetchAll();
} else {
    // Get all items if no search term
    $itemsQuery = "SELECT i.*,psl.*, c.categorydesc, psl.expiry_date, psl.selling_price
                   FROM items i
                   JOIN pharmacategory c ON i.category = c.categoryid
                   JOIN pharmacy_stock_ledger psl ON i.itemid = psl.itemid
                   ORDER BY i.generaldescription, psl.expiry_date";
    $items = $conn->query($itemsQuery)->fetchAll();
}

// Handle adding items to transaction
if (isset($_POST['add_item'])) {
    try {
        $itemId = $_POST['item_id'];
        $quantity = $_POST['quantity'];
        $doctorId = $_POST['doctor_id'];
        
        // Validate doctor ID exists
        $stmt = $conn->prepare("SELECT doctorid FROM doctors WHERE doctorid = ?");
        $stmt->execute([$doctorId]);
        if (!$stmt->fetch()) {
            throw new Exception("Invalid doctor selected");
        }
        
        // Get item details (using itemid directly)
        $stmt = $conn->prepare("SELECT selling_price FROM pharmacy_stock_ledger WHERE itemid = ? ORDER BY expiry_date ASC LIMIT 1");
        $stmt->execute([$itemId]);
        $item = $stmt->fetch();

        if (!$item) {
            throw new Exception("Invalid item selected");
        }
        
        $unitPrice = $item['selling_price'];
        $originalPrice = $quantity * $unitPrice;
        $subtotal = $originalPrice;
        $discountApplied = 0;
        
        // Apply discount if patient is Senior/PWD
        if (in_array($transaction['patient_type'], ['Senior', 'PWD'])) {
            $discountApplied = $originalPrice * 0.20; // Calculate 20% discount
            $subtotal = $originalPrice - $discountApplied; // Apply discount
        }
        
        $sql = "INSERT INTO pharmatransaction_details
                (transaction_id, doctorid, itemid, quantity, unit_price, subtotal, original_price, discount_applied)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$transactionId, $doctorId, $itemId, $quantity, $unitPrice, $subtotal, $originalPrice, $discountApplied]);
        $_SESSION['success'] = "Item added successfully";
    } catch(Exception $e) {
        $_SESSION['error'] = "Error adding item: " . $e->getMessage();
    }
}

// Get transaction details items (updated to use itemid directly)
$sql = "SELECT d.*, i.generaldescription, i.unitmeasure,
               (SELECT psl.expiry_date FROM pharmacy_stock_ledger psl WHERE psl.itemid = d.itemid ORDER BY psl.expiry_date ASC LIMIT 1) as expiry_date,
               (SELECT psl.selling_price FROM pharmacy_stock_ledger psl WHERE psl.itemid = d.itemid ORDER BY psl.expiry_date ASC LIMIT 1) as selling_price
        FROM pharmatransaction_details d
        JOIN items i ON d.itemid = i.itemid
        WHERE d.transaction_id = ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$transactionId]);
$transactionItems = $stmt->fetchAll();

// Calculate total
$total = array_sum(array_column($transactionItems, 'subtotal'));


// Handle item deletion
if (isset($_GET['delete_item'])) {
    $detailId = $_GET['id'];
    $transactionId = $_GET['transaction_id'];
    
    try {
        $sql = "DELETE FROM pharmatransaction_details WHERE detail_id = ? AND transaction_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$detailId, $transactionId]);
        
        $_SESSION['success'] = "Item deleted successfully";
        header("Location: transaction_details.php?id=" . $transactionId);
        exit();
    } catch(PDOException $e) {
        $_SESSION['error'] = "Error deleting item: " . $e->getMessage();
        header("Location: transaction_details.php?id=" . $transactionId);
        exit();
    }
}

// Check if payment has been made
$sql = "SELECT payment_type FROM payment WHERE transaction_id = ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$transactionId]);
$payment = $stmt->fetch();

if ($payment) {
    if ($payment['payment_type'] == 'Cash') {
        $paymentStatus = 'PAID';
    } else if ($payment['payment_type'] == 'Philhealth') {
        $paymentStatus = 'PHILHEALTH';
    } else {
        $paymentStatus = 'UNPAID (CREDIT)';
    }
} else {
    $paymentStatus = 'PENDING';
}


// Get doctors for dropdown
$sql = "SELECT doctorid, doctorname FROM doctors ORDER BY doctorname";
$doctors = $conn->query($sql)->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transaction Details</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
            --light-bg: #f8f9fa;
            --sidebar-bg: #ecf0f1;
            --text-primary: #2c3e50;
            --text-light: #ffffff;
            --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
            --border-radius: 8px;
        }

        body {
            margin: 0;
            padding: 0;
            background: var(--light-bg);
            color: var(--text-primary);
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
        }

        .main-container {
            min-height: 100vh;
            display: flex;
            background: var(--light-bg);
        }

        .sidebar {
            width: 300px;
            background: var(--sidebar-bg);
            padding: 1.5rem;
            box-shadow: var(--shadow-sm);
            border-right: 1px solid rgba(0,0,0,0.1);
        }

        .content-area {
            flex: 1;
            padding: 1.5rem;
            position: relative;
        }

        .transaction-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--text-light);
            padding: 1.25rem;
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow-md);
        }

        .action-buttons {
            display: flex;
            gap: 0.75rem;
            flex-wrap: wrap;
            margin-bottom: 1.5rem;
        }

        .action-buttons .btn {
            min-width: 130px;
            padding: 0.625rem 1rem;
            transition: all 0.3s ease;
        }

        .action-buttons .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-sm);
        }

        /* Lot Number Search Styling */
        #lotSearch {
            transition: all 0.3s ease;
        }

        #lotSearch:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .input-group-text.bg-primary {
            border-color: var(--primary-color);
        }

        #clearSearch {
            transition: all 0.3s ease;
        }

        #clearSearch:hover {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }

        /* Enhanced Select2 styling for filtered results */
        .select2-container--default .select2-results__option[aria-disabled=true] {
            color: #6c757d;
            font-style: italic;
        }

        .status-badge {
            font-size: 0.95rem;
            padding: 0.625rem 1.25rem;
            border-radius: var(--border-radius);
            font-weight: 500;
            box-shadow: var(--shadow-sm);
        }

        .total-section {
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
            color: var(--text-light);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            position: fixed;
            bottom: 1.5rem;
            right: 1.5rem;
            width: 320px;
            box-shadow: var(--shadow-md);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
    </style>
</head>
<body>

    <div class="main-container">
        <!-- Patient Information Sidebar -->
        <div class="sidebar rounded-4 shadow-sm">
            <!-- Patient Details Header -->
            <div class="sidebar-header border-bottom pb-3 mb-4">
                <h4 class="text-primary mb-0">
                    <i class="fas fa-hospital-user me-2"></i>Patient Information
                </h4>
            </div>

            <!-- Patient Details -->
            <div class="patient-details" style="height: 900 px;">
                <!-- Patient Info Cards -->
                <div class="info-cards-container mb-4">
                    <div class="info-card glass-effect p-3 rounded-4 mb-3">
                        <label class="text-primary small text-uppercase fw-semibold mb-2">Patient Name</label>
                        <h5 class="fw-bold text-dark mb-0"><?php echo htmlspecialchars($transaction['patientname']); ?></h5>
                    </div>

                    <div class="info-card glass-effect p-3 rounded-4 mb-3">
                        <label class="text-primary small text-uppercase fw-semibold mb-2">Patient Type</label>
                        <h5 class="fw-bold text-dark mb-0"><?php echo htmlspecialchars($transaction['patient_type']); ?></h5>
                    </div>

                    <div class="info-card glass-effect p-3 rounded-4 mb-3">
                        <label class="text-primary small text-uppercase fw-semibold mb-2">Ward</label>
                        <h5 class="fw-bold text-dark mb-0"><?php echo htmlspecialchars($transaction['ward']); ?></h5>
                    </div>

                    <div class="info-card glass-effect p-3 rounded-4 mb-3">
                        <label class="text-primary small text-uppercase fw-semibold mb-2">Transaction Date</label>
                        <h5 class="fw-bold text-dark mb-0"><?php echo date('M d, Y', strtotime($transaction['transaction_date'])); ?></h5>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="content-area">
            <div class="transaction-header" style="background: <?php echo 
                $paymentStatus === 'PAID' ? '#4CAF50' : 
                ($paymentStatus === 'PHILHEALTH' ? '#FFD700' : '#FF5252'); 
            ?>">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">
                        <i class="fas fa-file-invoice me-2"></i>
                        Transaction #<?php echo htmlspecialchars($transaction['transaction_reference']); ?>
                    </h3>
                    <div class="text-white">
                        <div class="date-time text-end">
                            <div id="currentDate" class="fs-5"></div>
                            <div id="currentTime" class="fs-4 fw-bold"></div>
                        </div>
                        <script>
                            function updateDateTime() {
                                const now = new Date();
                                const dateOptions = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
                                const timeOptions = { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: true };
                                
                                document.getElementById('currentDate').textContent = now.toLocaleDateString('en-US', dateOptions);
                                document.getElementById('currentTime').textContent = now.toLocaleTimeString('en-US', timeOptions);
                            }
                            
                            updateDateTime();
                            setInterval(updateDateTime, 1000);
                        </script>
                    </div>
                </div>
            </div>
            <!-- Lot Number Search Form -->
            <div class="card mb-3 border-primary border-opacity-25">
                <div class="card-body p-3">
                    <form method="GET" class="d-flex align-items-center gap-3">
                        <input type="hidden" name="id" value="<?php echo $transactionId; ?>">
                        <div class="flex-grow-1">
                            <label class="form-label text-primary mb-1">
                                <i class="fas fa-barcode me-2"></i>Filter Items by Lot Number
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-primary text-white">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text"
                                       name="lot_search"
                                       class="form-control border-primary"
                                       placeholder="Enter lot number to filter items..."
                                       value="<?php echo htmlspecialchars($lotSearch); ?>">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>Search
                                </button>
                                <?php if (!empty($lotSearch)): ?>
                                <a href="?id=<?php echo $transactionId; ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>Clear
                                </a>
                                <?php endif; ?>
                            </div>
                            <?php if (!empty($lotSearch)): ?>
                            <small class="text-success">
                                <i class="fas fa-check-circle me-1"></i>
                                Showing <?php echo count($items); ?> item(s) with lot number containing "<?php echo htmlspecialchars($lotSearch); ?>"
                            </small>
                            <?php else: ?>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Showing all <?php echo count($items); ?> available items
                            </small>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>

            <div class="action-buttons-container p-4 bg-white rounded-4 shadow-sm mb-4">
                <div class="d-flex flex-wrap gap-3 align-items-center">
                    <!-- Primary Actions -->
                    <div class="action-group">
                        <button type="button" class="btn btn-primary btn-lg rounded-pill shadow-sm hover-lift"
                                data-bs-toggle="modal" data-bs-target="#addItemModal">
                            <i class="fas fa-plus-circle me-2"></i>Add Item
                        </button>
                        <button type="button" class="btn btn-info btn-lg text-white rounded-pill shadow-sm hover-lift" 
                                data-bs-toggle="modal" data-bs-target="#priceCheckModal">
                            <i class="fas fa-tags me-2"></i>Check Prices
                        </button>
                    </div>
                    <!-- Payment Action -->
                    <a href="payment.php?ref=<?php echo htmlspecialchars($transaction['transaction_reference']); ?>" 
                       class="btn btn-primary btn-lg rounded-pill shadow-sm hover-lift">
                        <i class="fas fa-check-circle me-2"></i>Process <?php echo $paymentStatus; ?> Payment
                    </a>
                    <span class="status-badge d-inline-flex align-items-center gap-4 rounded-2 px-5 py-2 fs-5"
                          style="background: <?php echo 
                            $paymentStatus === 'PAID' ? '#4CAF50' : 
                            ($paymentStatus === 'PHILHEALTH' ? '#FFD700' : 
                            ($paymentStatus === 'UNPAID (CREDIT)' ? '#FFA726' : '#9E9E9E')); ?>;
                           color: <?php echo 
                            $paymentStatus === 'PAID' ? '#FFFFFF' : 
                            ($paymentStatus === 'PHILHEALTH' ? '#000000' : 
                            ($paymentStatus === 'UNPAID (CREDIT)' ? '#FFFFFF' : '#FFFFFF')); ?>;
                           border: 1px solid <?php echo 
                            $paymentStatus === 'PAID' ? '#388E3C' : 
                            ($paymentStatus === 'PHILHEALTH' ? '#FBC02D' : 
                            ($paymentStatus === 'UNPAID (CREDIT)' ? '#EF6C00' : '#616161')); ?>;">
                        <i class="fas <?php echo $paymentStatus === 'PAID' 
                            ? 'fa-check-circle' 
                            : ($paymentStatus === 'PHILHEALTH' 
                                ? 'fa-heart-pulse' 
                                : ($paymentStatus === 'UNPAID (CREDIT)' 
                                    ? 'fa-clock' 
                                    : 'fa-hourglass-half')); ?>"></i>
                        <span class="fw-bold fs-5"><?php echo $paymentStatus === 'PHILHEALTH' ? 'Philhealth' : $paymentStatus; ?></span>
                    </span>
                </div>
            </div>
        <!-- Price Check Modal -->
        <div class="modal fade" id="priceCheckModal" tabindex="-1" aria-labelledby="priceCheckModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title" id="priceCheckModalLabel">
                            <i class="fas fa-tags me-2"></i>Medicine Price List
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <div class="input-group">
                                <span class="input-group-text bg-info text-white">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="priceSearchInput" placeholder="Search medicines...">
                                <select class="form-select" id="categoryFilter" style="max-width: 200px;">
                                    <option value="">All Categories</option>
                                    <?php foreach($categories as $category): ?>
                                        <option value="<?php echo htmlspecialchars($category['categorydesc']); ?>">
                                            <?php echo htmlspecialchars($category['categorydesc']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <script>
                            document.addEventListener('DOMContentLoaded', function() {
                                const searchInput = document.getElementById('priceSearchInput');
                                const categoryFilter = document.getElementById('categoryFilter');
                                const tableRows = document.querySelectorAll('.table tbody tr');

                                function filterTable() {
                                    const searchTerm = searchInput.value.toLowerCase();
                                    const selectedCategory = categoryFilter.value.toLowerCase();

                                    tableRows.forEach(row => {
                                        const medicine = row.cells[0].textContent.toLowerCase();
                                        const category = row.cells[1].textContent.toLowerCase();
                                        
                                        const matchesSearch = medicine.includes(searchTerm);
                                        const matchesCategory = !selectedCategory || category === selectedCategory;

                                        row.style.display = (matchesSearch && matchesCategory) ? '' : 'none';
                                    });
                                }

                                searchInput.addEventListener('input', filterTable);
                                categoryFilter.addEventListener('change', filterTable);
                            });
                        </script>
                        <div class="table-responsive" style="max-height: 400px;">
                            <table class="table table-hover table-striped">
                                <thead class="sticky-top bg-white">
                                    <tr>
                                        <th>Medicine/Item</th>
                                        <th>Category</th>
                                        <th>Unit</th>
                                        <th class="text-end">Price</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($items as $item): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($item['generaldescription']); ?></td>
                                        <td><?php echo htmlspecialchars($item['categorydesc']); ?></td>
                                        <td><?php echo htmlspecialchars($item['unitmeasure']); ?></td>
                                        <td class="text-end">₱<?php echo number_format($item['selling_price'], 2); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="modal-footer bg-light">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!-- Add Item Modal -->
        <div class="modal fade" id="addItemModal" tabindex="-1" aria-labelledby="addItemModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title" id="addItemModalLabel">
                            <i class="fas fa-plus-circle me-2"></i>Add New Item
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body bg-light">
                        <form method="POST" id="addItemForm" class="needs-validation" novalidate>
                            <div class="mb-4">
                                <label class="form-label text-primary">
                                    <i class="fas fa-pills me-2"></i>Select Medicine/Item
                                </label>
                                <select class="form-select select2-modal border-primary" name="item_id" id="itemSelect" required>
                                    <option value="">Choose a medicine/item...</option>
                                    <?php foreach($items as $item): ?>
                                        <?php
                                            $expiryDate = strtotime($item['expiry_date']);
                                            $today = strtotime('today');
                                            $expiryClass = $expiryDate < $today ? 'text-danger' : 'text-success';
                                        ?>
                                        <option value="<?php echo $item['itemid']; ?>"
                                                data-lot="<?php echo htmlspecialchars($item['lot_no']); ?>"
                                                data-description="<?php echo htmlspecialchars($item['generaldescription']); ?>"
                                                data-category="<?php echo htmlspecialchars($item['categorydesc']); ?>">
                                            ID: <?php echo $item['itemid']; ?> -
                                            <?php echo htmlspecialchars($item['generaldescription']); ?> -
                                            <?php echo htmlspecialchars($item['categorydesc']); ?> -
                                            Lot: <?php echo htmlspecialchars($item['lot_no']); ?>
                                            (Expires: <span class="<?php echo $expiryClass; ?>"><?php echo date('M d, Y', $expiryDate); ?></span>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback"><i class="fas fa-exclamation-circle me-2"></i>Please select a medicine/item</div>
                            </div>
                            <div class="mb-4">
                                <label class="form-label text-primary">
                                    <i class="fas fa-cubes me-2"></i>Quantity
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-primary text-white"><i class="fas fa-hashtag"></i></span>
                                    <input type="number" class="form-control border-primary" name="quantity" min="1" required>
                                    <div class="invalid-feedback"><i class="fas fa-exclamation-circle me-2"></i>Please enter a valid quantity</div>
                                </div>
                            </div>
                            <div class="mb-4">
                                <label class="form-label text-primary">
                                    <i class="fas fa-user-md me-2"></i>Select Doctor
                                </label>
                                <select class="form-select border-primary" name="doctor_id" required>
                                    <option value="">Choose a doctor...</option>
                                    <?php foreach($doctors as $doctor): ?>
                                        <option value="<?php echo $doctor['doctorid']; ?>">
                                            <?php echo htmlspecialchars($doctor['doctorname']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback"><i class="fas fa-exclamation-circle me-2"></i>Please select a doctor</div>
                            </div>
                            <div class="text-end mt-4">
                                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </button>
                                <button type="submit" name="add_item" class="btn btn-primary ms-2">
                                    <i class="fas fa-plus-circle me-2"></i>Add to Transaction
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Items List -->
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Items</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive" style="max-height: 650px; overflow-y: auto;">
                    <table class="table table-hover align-middle mb-0">
                        <thead style="position: sticky; top: 0; background: white; z-index: 1;" class="bg-light">
                            <tr class="text-primary">
                                <th class="px-4 py-3">Item</th>
                                <th class="px-4 py-3">Doctor</th>
                                <th class="px-4 py-3">Unit</th>
                                <th class="px-4 py-3">Quantity</th>
                                <th class="px-4 py-3">Unit Price</th>
                                <th class="px-4 py-3">Original Price</th>
                                <th class="px-4 py-3">Discount</th>
                                <th class="px-4 py-3">Final Price</th>
                                <th class="px-4 py-3">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($transactionItems as $item): ?>
                            <tr class="border-bottom">
                                <td class="px-4 py-3">
                                    <div class="fw-semibold"><?php echo htmlspecialchars($item['generaldescription']); ?></div>
                                    <small class="text-muted">Expires: <?php echo date('M d, Y', strtotime($item['expiry_date'])); ?></small>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="badge bg-info text-white">
                                        <?php echo htmlspecialchars($conn->query("SELECT alias FROM doctors WHERE doctorid = " . $item['doctorid'])->fetch()['alias'] ?? 'N/A'); ?>
                                    </span>
                                </td>
                                <td class="px-4 py-3"><span class="badge bg-light text-dark"><?php echo htmlspecialchars($item['unitmeasure']); ?></span></td>
                                <td class="px-4 py-3 fw-bold"><?php echo htmlspecialchars($item['quantity']); ?></td>
                                <td class="px-4 py-3">₱<?php echo number_format($item['unit_price'], 2); ?></td>
                                <td class="px-4 py-3">₱<?php echo number_format($item['original_price'], 2); ?></td>
                                <td class="px-4 py-3 text-danger">₱<?php echo number_format($item['discount_applied'], 2); ?></td>
                                <td class="px-4 py-3 text-success fw-bold">₱<?php echo number_format($item['subtotal'], 2); ?></td>
                                <td class="px-4 py-3">
                                    <?php if ($paymentStatus !== 'PAID' && $paymentStatus !== 'UNPAID (CREDIT)' && $paymentStatus !== 'PHILHEALTH'): ?>
                                        <button type="button" 
                                                class="btn btn-danger btn-sm rounded-circle" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#deleteModal<?php echo $item['detail_id']; ?>">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    <?php else: ?>
                                        <button type="button" 
                                                class="btn btn-danger btn-sm rounded-circle" 
                                                disabled 
                                                title="Cannot delete items from paid or credit transactions">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    <?php endif; ?>

                                    <!-- Delete Modal -->
                                    <div class="modal fade" id="deleteModal<?php echo $item['detail_id']; ?>" tabindex="-1">
                                        <div class="modal-dialog modal-dialog-centered">
                                            <div class="modal-content border-0">
                                                <div class="modal-header bg-danger text-white">
                                                    <h5 class="modal-title">
                                                        <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                                                    </h5>
                                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body p-4">
                                                    <div class="text-center mb-4">
                                                        <i class="fas fa-trash-alt fa-3x text-danger mb-3"></i>
                                                        <h5>Are you sure you want to delete this item?</h5>
                                                        <p class="mb-0 text-muted"><?php echo htmlspecialchars($item['generaldescription']); ?></p>
                                                    </div>
                                                    <div class="d-flex justify-content-center gap-2">
                                                        <button type="button" class="btn btn-light px-4" data-bs-dismiss="modal">
                                                            <i class="fas fa-times me-2"></i>Cancel
                                                        </button>
                                                        <form method="GET" style="display: inline;">
                                                            <input type="hidden" name="delete_item" value="1">
                                                            <input type="hidden" name="id" value="<?php echo $item['detail_id']; ?>">
                                                            <input type="hidden" name="transaction_id" value="<?php echo $transactionId; ?>">
                                                            <button type="submit" class="btn btn-danger px-4">
                                                                <i class="fas fa-trash-alt me-2"></i>Delete
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot class="bg-light" style="position: sticky; bottom: 0; background: white;">
                            <tr class="border-top">
                                <td colspan="6" class="px-4 py-3"></td>
                                <td class="px-4 py-3 fw-bold text-danger">
                                    ₱<?php echo number_format(array_sum(array_column($transactionItems, 'discount_applied')), 2); ?>
                                </td>
                                <td class="px-4 py-3 fw-bold text-success">
                                    ₱<?php echo number_format($total, 2); ?>
                                </td>
                                <td class="px-4 py-3"></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
        <!-- Back Button -->
        <div class="mt-4">
            <a href="transactions.php?id=<?php echo $transaction['patientid']; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Transactions
            </a>
            <button type="button" class="btn btn-primary ms-2" data-bs-toggle="modal" data-bs-target="#calculateChangeModal">
                <i class="fas fa-calculator me-2"></i>Calculate Change
            </button>

            <!-- Calculate Change Modal -->
            <div class="modal fade" id="calculateChangeModal" tabindex="-1">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title"><i class="fas fa-calculator me-2"></i>Calculate Change</h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">Total Amount</label>
                                <input type="text" class="form-control" id="totalAmount" value="<?php echo number_format($total, 2); ?>" readonly>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Amount Received</label>
                                <input type="number" class="form-control" id="amountReceived" step="0.01">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Change</label>
                                <input type="text" class="form-control" id="changeAmount" readonly>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>

            <script>
                document.getElementById('amountReceived').addEventListener('input', function() {
                    const total = parseFloat('<?php echo $total; ?>');
                    const received = parseFloat(this.value) || 0;
                    const change = received - total;
                    document.getElementById('changeAmount').value = change >= 0 ? '₱' + change.toFixed(2) : 'Insufficient amount';
                });
            </script>
        </div>
    </div>
     <div class="preview-container bg-white rounded-4 shadow-lg mb-4 overflow-hidden" style="height: 1080px; margin-top: 10px;">
            <div class="preview-content p-4">
                <div class="preview-frame rounded-3 border" style="overflow: auto; background: #f8f9fa;">
                    <iframe id="receiptPreview" 
                            src="print_temp_receipt.php?id=<?php echo $transactionId; ?>&preview=true" 
                            class="w-100 border-0"
                            style="height: 900px; transform-origin: top left;">
                    </iframe>
                </div>
            </div>
            <div class="preview-footer p-3 bg-light border-top d-flex justify-content-center">
                <!-- Print Options -->
                    <div class="print-group d-flex gap-2">
                        <div class="dropdown">
                            <button class="btn btn-warning btn-lg text-white rounded-pill shadow-sm hover-lift dropdown-toggle" 
                                    type="button" data-bs-toggle="dropdown"
                                    style="min-width: 180px; transition: all 0.3s ease;">
                                <i class="fas fa-receipt me-2"></i>Temp Receipt
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end shadow-lg border-0 rounded-4 py-3"
                                style="min-width: 250px; backdrop-filter: blur(10px);">
                                <li class="px-3 mb-2">
                                    <h6 class="dropdown-header text-uppercase fw-semibold text-muted mb-2">
                                        <i class="fas fa-user-shield me-2"></i>Select Pharmacist
                                    </h6>
                                </li>
                                <?php
                                $pharmacists = [
                                    'RONNIEB B. CELIS, RPh',
                                    'MARK JOSHUA BALLESTA, RPh',
                                    'HAMILLY GRACE ARCEGA, RPh'
                                ];
                                foreach($pharmacists as $pharmacist): ?>
                                    <li>
                                        <a class="dropdown-item d-flex align-items-center gap-3 px-3 py-2 hover-bg-light rounded-3" 
                                           href="print_temp_receipt.php?id=<?php echo $transactionId; ?>&pharmacist=<?php echo urlencode($pharmacist); ?>" 
                                           target="_blank">
                                            <span class="icon-wrapper bg-warning bg-opacity-10 text-warning rounded-circle p-2">
                                                <i class="fas fa-user-md"></i>
                                            </span>
                                            <span class="fw-medium"><?php echo $pharmacist; ?></span>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>

                        <div class="dropdown">
                            <button class="btn btn-success btn-lg rounded-pill shadow-sm hover-lift dropdown-toggle" 
                                    type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-print me-2"></i>Chargeslip
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end shadow-sm border-0 rounded-3 p-2">
                                <?php foreach($pharmacists as $pharmacist): ?>
                                    <li>
                                        <a class="dropdown-item rounded-3 p-2 hover-bg" 
                                           href="phicschargeslip.php?id=<?php echo $transactionId; ?>&pharmacist=<?php echo urlencode($pharmacist); ?>" 
                                           target="_blank">
                                            <i class="fas fa-user-md me-2"></i><?php echo $pharmacist; ?>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
            </div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            $('.select2').select2({
                theme: 'bootstrap-5'
            });
        });
    </script>
      <script>
            const refreshPreview = () => {
                const iframe = document.getElementById('receiptPreview');
                iframe.src = `print_temp_receipt.php?id=<?php echo $transactionId; ?>&preview=true&t=${Date.now()}`;
            };

            document.getElementById('refreshPreview').addEventListener('click', refreshPreview);

            <?php if(isset($_SESSION['success']) || isset($_SESSION['error'])): ?>
                refreshPreview();
            <?php endif; ?>

            // Lot Number Search Functionality
            $(document).ready(function() {
                let originalOptions = [];

                // Store original options when modal is first opened
                $('#addItemModal').on('shown.bs.modal', function() {
                    if (originalOptions.length === 0) {
                        originalOptions = $('#itemSelect option').clone();
                    }
                    $('#lotSearch').focus();
                });

                // Search function
                function filterByLotNumber() {
                    const searchTerm = $('#lotSearch').val().toLowerCase().trim();
                    const itemSelect = $('#itemSelect');

                    console.log('Searching for lot number:', searchTerm);

                    // Clear current options
                    itemSelect.empty();
                    itemSelect.append('<option value="">Choose a medicine/item...</option>');

                    if (searchTerm === '') {
                        // Show all options if search is empty
                        originalOptions.each(function() {
                            if ($(this).val() !== '') {
                                itemSelect.append($(this).clone());
                            }
                        });
                    } else {
                        // Filter by lot number only
                        let foundCount = 0;
                        originalOptions.each(function() {
                            if ($(this).val() !== '') {
                                const lotNo = $(this).attr('data-lot') || '';
                                console.log('Checking lot:', lotNo);

                                if (lotNo.toLowerCase().includes(searchTerm)) {
                                    itemSelect.append($(this).clone());
                                    foundCount++;
                                }
                            }
                        });

                        console.log('Found', foundCount, 'items');

                        if (foundCount === 0) {
                            itemSelect.append('<option value="" disabled>No items found with lot number containing "' + searchTerm + '"</option>');
                        } else {
                            itemSelect.find('option:first').text(`Found ${foundCount} item(s) with lot number containing "${searchTerm}"...`);
                        }
                    }

                    // Trigger change event
                    itemSelect.trigger('change');
                }

                // Event listeners
                $('#lotSearch').on('input keyup', filterByLotNumber);

                // Clear search
                $('#clearSearch').on('click', function() {
                    $('#lotSearch').val('');
                    filterByLotNumber();
                    $('#lotSearch').focus();
                });

                // Clear search when modal closes
                $('#addItemModal').on('hidden.bs.modal', function() {
                    $('#lotSearch').val('');
                    filterByLotNumber();
                });
            });
        </script>
</body>
</html>
