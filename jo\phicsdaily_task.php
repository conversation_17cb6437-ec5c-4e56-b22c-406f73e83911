<?php
require_once '../database.php';

// Create daily_tasks table if not exists
$sql = "CREATE TABLE IF NOT EXISTS daily_tasks (
    daily_task_id INT AUTO_INCREMENT PRIMARY KEY,
    task_id INT NOT NULL,
    other_task_description  TEXT,
    times_accomplished INT DEFAULT 0,
    task_date DATE NOT NULL,
    employee_id VARCHAR(50) NOT NULL,
    FOREIGN KEY (task_id) REFERENCES tasks(task_id)
)";
$conn->exec($sql);

// Fetch employees for dropdown
$sql = "SELECT employee_id, jo_name FROM jo_profile 
        WHERE employee_id IN ('2356','2357','2379','2372','2347') 
        ORDER BY jo_name";
$stmt = $conn->query($sql);
$employees = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch tasks for dropdown
$sql = "SELECT task_id, task_description FROM tasks ORDER BY task_description";
$stmt = $conn->query($sql);
$tasks = $stmt->fetchAll(PDO::FETCH_ASSOC);

// INSERT
if (isset($_POST['submit'])) {
    $sql = "INSERT INTO daily_tasks (task_id, other_task_description, times_accomplished, task_date, employee_id) VALUES (?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $_POST['task_description'],
        $_POST['other_task_description'],
        $_POST['times_accomplished'],
        $_POST['task_date'],
        $_POST['employee_id']
    ]);
}

// UPDATE
if (isset($_POST['update'])) {
    $sql = "UPDATE daily_tasks SET task_id=?, other_task_description=?, times_accomplished=?, task_date=?, employee_id=? WHERE task_id=?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $_POST['task_description'],
        $_POST['other_task_description'],
        $_POST['times_accomplished'],
        $_POST['task_date'],
        $_POST['employee_id'],
        $_POST['task_id']
    ]);
}

// SEARCH
$search = isset($_GET['search']) ? $_GET['search'] : '';
$sql = "SELECT dt.*, jp.jo_name, t.task_description
        FROM daily_tasks dt 
        JOIN jo_profile jp ON dt.employee_id = jp.employee_id 
        JOIN tasks t ON dt.task_id = t.task_id
        WHERE t.task_description LIKE ? 
        OR jp.jo_name LIKE ?
        ORDER BY dt.task_date DESC";
$stmt = $conn->prepare($sql);
$searchTerm = "%$search%";
$stmt->execute([$searchTerm, $searchTerm]);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Tasks</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 20px auto; }
        .table-responsive { margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card shadow-lg rounded-4 border-0">
            <div class="card-header bg-gradient bg-primary bg-opacity-25 border-0 py-4 d-flex justify-content-between align-items-center">
                <h3 class="mb-0 text-primary">
                    <i class="fas fa-tasks me-2"></i>Daily Tasks
                </h3>
                <div class="d-flex align-items-center">
                    <button type="button" class="btn btn-primary btn-lg rounded-3 shadow-sm" data-bs-toggle="modal" data-bs-target="#taskModal">
                        <i class="fas fa-plus me-2"></i>New Task
                    </button>
                    <div class="ms-2">
                        <a href="../index.php" class="btn btn-outline-primary btn-lg rounded-3 shadow-sm">
                            <i class="fas fa-home me-2"></i>Homepage
                        </a>
                        <button class="btn btn-outline-success btn-lg rounded-3 shadow-sm ms-2" data-bs-toggle="modal" data-bs-target="#reportModal">
                            <i class="fas fa-file-pdf me-2"></i>Generate Report
                        </button>
                        <!-- Report Modal -->
                        <div class="modal fade" id="reportModal" tabindex="-1">
                            <div class="modal-dialog modal-dialog-centered">
                                <div class="modal-content">
                                    <div class="modal-header bg-success text-white">
                                        <h5 class="modal-title"><i class="fas fa-calendar me-2"></i>Select Period</h5>
                                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="mb-3">
                                            <label class="form-label">Month</label>
                                            <select class="form-select" id="monthSelect">
                                                <option value="1">January</option>
                                                <option value="2">February</option>
                                                <option value="3">March</option>
                                                <option value="4">April</option>
                                                <option value="5">May</option>
                                                <option value="6">June</option>
                                                <option value="7">July</option>
                                                <option value="8">August</option>
                                                <option value="9">September</option>
                                                <option value="10">October</option>
                                                <option value="11">November</option>
                                                <option value="12">December</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Year</label>
                                            <input type="number" class="form-select" id="yearInput" value="<?php echo date('Y'); ?>" min="2000" max="2099">
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                            <i class="fas fa-times me-2"></i>Cancel
                                        </button>
                                        <button type="button" class="btn btn-success" onclick="generateReport()">
                                            <i class="fas fa-file-pdf me-2"></i>Generate
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <script>
                            function generateReport() {
                                const month = document.getElementById('monthSelect').value;
                                const year = document.getElementById('yearInput').value;
                                window.location.href = `monthly_accomplishment.php?month=${month}&year=${year}`;
                            }
                        </script>
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                <form method="GET" class="mb-4">
                    <div class="input-group input-group-lg shadow-sm rounded-3 overflow-hidden">
                        <span class="input-group-text border-0 bg-light">
                            <i class="fas fa-search text-primary"></i>
                        </span>
                        <input type="text" class="form-control border-0 bg-light py-3" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Search tasks or employees...">
                        <button class="btn btn-primary px-4" type="submit">
                            <i class="fas fa-search me-2"></i>Search
                        </button>
                    </div>
                </form>

                <div class="table-responsive rounded-3 border">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="bg-light">
                            <tr>
                                <th><i class="fas fa-hashtag me-2"></i>ID</th>
                                <th><i class="fas fa-tasks me-2"></i>Task</th>
                                <th><i class="fas fa-tasks me-2"></i>Other Task Description</th>
                                <th><i class="fas fa-check-double me-2"></i>Times Accomplished</th>
                                <th><i class="fas fa-calendar me-2"></i>Date</th>
                                <th><i class="fas fa-user me-2"></i>Employee</th>
                                <th><i class="fas fa-cog me-2"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($results as $row): ?>
                            <tr>
                                <td class="fw-bold"><?php echo htmlspecialchars($row['daily_task_id']); ?></td>
                                <td><?php 
                                    foreach($tasks as $task) {
                                        if($task['task_id'] == $row['task_id']) {
                                            echo htmlspecialchars($task['task_description']);
                                            break;
                                        }
                                    }
                                ?></td>
                                <td><?php echo $row['other_task_description'] ? htmlspecialchars($row['other_task_description']) : '---'; ?></td>
                                <td><?php echo htmlspecialchars($row['times_accomplished']); ?></td>
                                <td><?php echo htmlspecialchars(date('M d, Y', strtotime($row['task_date']))); ?></td>
                                <td><?php echo htmlspecialchars($row['jo_name']); ?></td>
                                <td>
                                    <button class="btn btn-warning btn-sm rounded-3" onclick="editTask(<?php echo htmlspecialchars(json_encode($row)); ?>)">
                                        <i class="fas fa-edit me-1"></i>Edit
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Add/Edit -->
    <div class="modal fade" id="taskModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="modalTitle">
                        <i class="fas fa-plus me-2"></i>Add New Task
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="taskForm" method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="task_id" id="task_id">
                        <div class="mb-3">
                            <label class="form-label">Task</label>
                            <select class="form-select" name="task_description" id="task_description" required onchange="toggleOtherTaskDescription()">
                                <option value="">Select Task</option>
                                <?php foreach($tasks as $task): ?>
                                    <option value="<?php echo htmlspecialchars($task['task_id']); ?>">
                                        <?php echo htmlspecialchars($task['task_description']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3" id="otherTaskDescriptionDiv" style="display: none;">
                            <label class="form-label">Other Task Description</label>
                            <textarea class="form-control" name="other_task_description" id="other_task_description" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Times Accomplished</label>
                            <input type="number" class="form-control" name="times_accomplished" id="times_accomplished" required min="0">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Date</label>
                            <input type="date" class="form-control" name="task_date" id="task_date" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Employee</label>
                            <select class="form-select" name="employee_id" id="employee_id" required>
                                <option value="">Select Employee</option>
                                <?php foreach($employees as $employee): ?>
                                    <option value="<?php echo htmlspecialchars($employee['employee_id']); ?>">
                                        <?php echo htmlspecialchars($employee['jo_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Close
                        </button>
                        <button type="submit" class="btn btn-primary" id="submitBtn" name="submit">
                            <i class="fas fa-save me-2"></i>Save Task
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function toggleOtherTaskDescription() {
            const taskSelect = document.getElementById('task_description');
            const otherTaskDiv = document.getElementById('otherTaskDescriptionDiv');
            otherTaskDiv.style.display = taskSelect.value === '4' ? 'block' : 'none';
        }
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editTask(data) {
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-edit me-2"></i>Edit Task';
            document.getElementById('task_id').value = data.task_id;
            document.getElementById('task_description').value = data.task_id; // Changed to match task_id
            document.getElementById('other_task_description').value = data.other_task_description;
            document.getElementById('times_accomplished').value = data.times_accomplished;
            document.getElementById('task_date').value = data.task_date;
            document.getElementById('employee_id').value = data.employee_id;
            
            // Change submit button
            document.getElementById('submitBtn').name = 'update';
            document.getElementById('submitBtn').innerHTML = '<i class="fas fa-save me-2"></i>Update Task';
            
            // Show modal
            new bootstrap.Modal(document.getElementById('taskModal')).show();
        }

        // Reset form when modal is closed
        document.getElementById('taskModal').addEventListener('hidden.bs.modal', function () {
            document.getElementById('taskForm').reset();
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-plus me-2"></i>Add New Task';
            document.getElementById('submitBtn').name = 'submit';
            document.getElementById('submitBtn').innerHTML = '<i class="fas fa-save me-2"></i>Save Task';
        });

        // Set default date to today for new tasks
        document.getElementById('taskModal').addEventListener('show.bs.modal', function () {
            if (!document.getElementById('task_id').value) {
                document.getElementById('task_date').valueAsDate = new Date();
            }
        });
    </script>
</body>
</html>
