<?php
require_once '../../database.php';

// Create chart_tracking table if not exists
$sql = "CREATE TABLE IF NOT EXISTS chart_tracking (
    id INT AUTO_INCREMENT PRIMARY KEY,
    patient_lname VARCHAR(100) NOT NULL,
    patient_fname VARCHAR(100) NOT NULL,
    patient_mname VARCHAR(100),
    patient_extname VARCHAR(20),
    patient_bday DATE,
    patient_dadmitted DATE,
    patient_ddischarge DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

$conn->exec($sql);

// INSERT
if (isset($_POST['submit'])) {
    $sql = "INSERT INTO chart_tracking (patient_lname, patient_fname, patient_mname, patient_extname, 
            patient_bday, patient_dadmitted, patient_ddischarge) 
            VALUES (?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $_POST['patient_lname'], $_POST['patient_fname'], $_POST['patient_mname'], $_POST['patient_extname'],
        $_POST['patient_bday'], $_POST['patient_dadmitted'], $_POST['patient_ddischarge']
    ]);
}

// UPDATE
if (isset($_POST['update'])) {
    $sql = "UPDATE chart_tracking SET 
            patient_lname = ?, patient_fname = ?, patient_mname = ?, patient_extname = ?,
            patient_bday = ?, patient_dadmitted = ?, patient_ddischarge = ? 
            WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $_POST['patient_lname'], $_POST['patient_fname'], $_POST['patient_mname'], $_POST['patient_extname'],
        $_POST['patient_bday'], $_POST['patient_dadmitted'], $_POST['patient_ddischarge'], $_POST['id']
    ]);
}

// SEARCH
$search = isset($_GET['search']) ? $_GET['search'] : '';
$sql = "SELECT * FROM chart_tracking WHERE 
        patient_lname LIKE ? OR patient_fname LIKE ? OR patient_mname LIKE ?
        ORDER BY created_at DESC";
$stmt = $conn->prepare($sql);
$searchTerm = "%$search%";
$stmt->execute([$searchTerm, $searchTerm, $searchTerm]);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medical Records Tracking</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #475569;
            --success-color: #16a34a;
            --warning-color: #ca8a04;
            --info-color: #0891b2;
            --background-color: #f1f5f9;
            --card-color: #ffffff;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            min-height: 100vh;
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .nav-section {
            background: var(--card-color);
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            margin-bottom: 2rem;
            padding: 1.5rem;
        }

        .nav-section .nav-link {
            color: var(--secondary-color);
            padding: 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .nav-section .nav-link:hover,
        .nav-section .nav-link.active {
            background-color: var(--background-color);
            color: var(--primary-color);
        }

        .content-section {
            background: var(--card-color);
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            padding: 2rem;
        }

        .search-section {
            background: var(--background-color);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .records-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0 0.5rem;
        }

        .records-table th {
            background: var(--background-color);
            padding: 1rem;
            font-weight: 600;
            color: var(--secondary-color);
        }

        .records-table td {
            padding: 1rem;
            background: var(--card-color);
            border-top: 1px solid var(--background-color);
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .action-buttons .btn {
            padding: 0.5rem;
            border-radius: 6px;
            margin-right: 0.5rem;
        }

        .modal-content {
            border-radius: 12px;
            border: none;
        }

        .form-control {
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            padding: 0.75rem;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Navigation -->
        <nav class="nav-section">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3 class="m-0 text-primary">Medical Records Tracker</h3>
                <a href="../../index.php" class="btn btn-outline-primary">
                    <i class="fas fa-home me-2"></i>Home
                </a>
            </div>
            
            <div class="d-flex gap-3">
                <a class="nav-link active" data-bs-toggle="modal" data-bs-target="#chartModal">
                    <i class="fas fa-plus-circle me-2"></i>New Record
                </a>
                <a class="nav-link">
                    <i class="fas fa-inbox me-2"></i>Incoming
                </a>
                <a class="nav-link">
                    <i class="fas fa-paper-plane me-2"></i>Outgoing
                </a>
                <a class="nav-link">
                    <i class="fas fa-chart-line me-2"></i>Reports
                </a>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="content-section">
            <!-- Search -->
            <div class="search-section">
                <form method="GET" class="d-flex gap-3">
                    <div class="flex-grow-1">
                        <div class="input-group">
                            <span class="input-group-text border-0 bg-transparent">
                                <i class="fas fa-search text-secondary"></i>
                            </span>
                            <input type="text" class="form-control border-0 bg-transparent" 
                                   name="search" value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="Search by patient name...">
                        </div>
                    </div>
                    <button class="btn btn-primary px-4">Search</button>
                </form>
            </div>

            <!-- Records Table -->
            <div class="table-responsive">
                <table class="records-table">
                    <thead>
                        <tr>
                            <th>Record ID</th>
                            <th>Patient Name</th>
                            <th>Status</th>
                            <th>Current Location</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($results as $row): ?>
                        <tr>
                            <td class="fw-bold text-primary">#<?php echo $row['id']; ?></td>
                            <td>
                                <?php 
                                echo htmlspecialchars($row['patient_lname'] . ', ' . 
                                     $row['patient_fname']); 
                                ?>
                            </td>
                            <td>
                                <span class="status-badge bg-success bg-opacity-10 text-success">
                                    Active
                                </span>
                            </td>
                            <td>
                                <i class="fas fa-building me-2 text-secondary"></i>
                                Records Section
                            </td>
                            <td class="action-buttons">
                                <button class="btn btn-light" 
                                        onclick="editChart(<?php echo htmlspecialchars(json_encode($row)); ?>)">
                                    <i class="fas fa-edit text-primary"></i>
                                </button>
                                <a href="pchart_history.php?id=<?php echo $row['id']; ?>" 
                                   class="btn btn-light">
                                    <i class="fas fa-history text-info"></i>
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </main>
    </div>

    <!-- Record Modal -->
    <div class="modal fade" id="chartModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">
                        <i class="fas fa-plus-circle me-2 text-primary"></i>New Medical Record
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="chartForm">
                    <div class="modal-body">
                        <input type="hidden" id="id" name="id">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <label class="form-label">Last Name</label>
                                <input type="text" class="form-control" name="patient_lname" id="patient_lname" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">First Name</label>
                                <input type="text" class="form-control" name="patient_fname" id="patient_fname" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Middle Name</label>
                                <input type="text" class="form-control" name="patient_mname" id="patient_mname">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Extension Name</label>
                                <input type="text" class="form-control" name="patient_extname" id="patient_extname">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Date of Birth</label>
                                <input type="date" class="form-control" name="patient_bday" id="patient_bday">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Admission Date</label>
                                <input type="date" class="form-control" name="patient_dadmitted" id="patient_dadmitted">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Discharge Date</label>
                                <input type="date" class="form-control" name="patient_ddischarge" id="patient_ddischarge">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-light" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="submitBtn" name="submit">
                            <i class="fas fa-save me-2"></i>Save Record
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editChart(data) {
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-edit me-2 text-primary"></i>Edit Record';
            document.getElementById('id').value = data.id;
            document.getElementById('patient_lname').value = data.patient_lname;
            document.getElementById('patient_fname').value = data.patient_fname;
            document.getElementById('patient_mname').value = data.patient_mname;
            document.getElementById('patient_extname').value = data.patient_extname;
            document.getElementById('patient_bday').value = data.patient_bday;
            document.getElementById('patient_dadmitted').value = data.patient_dadmitted;
            document.getElementById('patient_ddischarge').value = data.patient_ddischarge;
            document.getElementById('submitBtn').name = 'update';
            document.getElementById('submitBtn').innerHTML = '<i class="fas fa-save me-2"></i>Update Record';
            
            new bootstrap.Modal(document.getElementById('chartModal')).show();
        }

        document.getElementById('chartModal').addEventListener('hidden.bs.modal', function () {
            document.getElementById('chartForm').reset();
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-plus-circle me-2 text-primary"></i>New Record';
            document.getElementById('submitBtn').name = 'submit';
            document.getElementById('submitBtn').innerHTML = '<i class="fas fa-save me-2"></i>Save Record';
        });
    </script>
</body>
</html>
