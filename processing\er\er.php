<?php
require_once '../../database.php';

// INSERT
if (isset($_POST['submit'])) {
    $sql = "INSERT INTO pendingercases (phicnum, glast, gfirst, gmid, gext, gbday, plast, pfirst, pmid, pext, pbday, dateconsulted, timeconsulted, contactnum, rstatus, Remarks) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $_POST['phicnum'], $_POST['glast'], $_POST['gfirst'], $_POST['gmid'], $_POST['gext'],
        $_POST['gbday'], $_POST['plast'], $_POST['pfirst'], $_POST['pmid'], $_POST['pext'],
        $_POST['pbday'], $_POST['dateconsulted'], $_POST['timeconsulted'], $_POST['contactnum'],
        $_POST['rstatus'], $_POST['remarks']
    ]);
}

// UPDATE
if (isset($_POST['update'])) {
    // Handle empty date fields
    $gbday = !empty($_POST['gbday']) ? $_POST['gbday'] : null;
    $pbday = !empty($_POST['pbday']) ? $_POST['pbday'] : null;
    
    $sql = "UPDATE pendingercases SET 
            phicnum=?, glast=?, gfirst=?, gmid=?, gext=?, gbday=?,
            plast=?, pfirst=?, pmid=?, pext=?, pbday=?, dateconsulted=?,
            timeconsulted=?, contactnum=?, rstatus=?, Remarks=?
            WHERE phicnum=?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $_POST['phicnum'], $_POST['glast'], $_POST['gfirst'], $_POST['gmid'], $_POST['gext'],
        $gbday, $_POST['plast'], $_POST['pfirst'], $_POST['pmid'], $_POST['pext'],
        $pbday, $_POST['dateconsulted'], $_POST['timeconsulted'], $_POST['contactnum'],
        $_POST['rstatus'], $_POST['remarks'], $_POST['original_phicnum']
    ]);
}

// SEARCH
$search = isset($_GET['search']) ? $_GET['search'] : '';
$sql = "SELECT * FROM pendingercases WHERE 
        phicnum LIKE ? OR glast LIKE ? OR gfirst LIKE ? OR 
        plast LIKE ? OR pfirst LIKE ?";
$stmt = $conn->prepare($sql);
$searchTerm = "%$search%";
$stmt->execute([$searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm]);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ER Cases Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 20px auto; }
        .table-responsive { margin-top: 20px; }
        .modal-lg { max-width: 800px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card shadow-lg rounded-4 border-0">
            <div class="card-header bg-gradient bg-info bg-opacity-25 border-0 py-4 d-flex justify-content-between align-items-center">
                <h3 class="mb-0 text-primary">
                    <i class="fas fa-hospital me-2"></i>Emergency Room Cases
                </h3>
                <div class="d-flex align-items-center">
                    <button type="button" class="btn btn-primary btn-lg rounded-3 shadow-sm" data-bs-toggle="modal" data-bs-target="#erCaseModal">
                        <i class="fas fa-user-plus me-2"></i>New Patient
                    </button>
                    <div class="ms-2">
                        <a href="../../index.php" class="btn btn-outline-primary btn-lg rounded-3 shadow-sm">
                            <i class="fas fa-home me-2"></i>Homepage
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                <form method="GET" class="mb-4">
                    <div class="input-group input-group-lg shadow-sm rounded-3 overflow-hidden">
                        <span class="input-group-text border-0 bg-light">
                            <i class="fas fa-search text-primary"></i>
                        </span>
                        <input type="text" class="form-control border-0 bg-light py-3" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Search patients by PHIC number or name...">
                        <button class="btn btn-primary px-4" type="submit">
                            <i class="fas fa-search-plus me-2"></i>Search
                        </button>
                    </div>
                </form>

                <div class="table-responsive rounded-3 border">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="bg-light">
                            <tr>
                                <th><i class="fas fa-id-card me-2"></i>PHIC Number</th>
                                <th><i class="fas fa-user-shield me-2"></i>Guardian</th>
                                <th><i class="fas fa-user-injured me-2"></i>Patient</th>
                                <th><i class="fas fa-calendar-check me-2"></i>Consultation</th>
                                <th><i class="fas fa-chart-line me-2"></i>Status</th>
                                <th><i class="fas fa-cog me-2"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($results as $row): ?>
                            <tr>
                                <td class="fw-bold text-primary"><?php echo htmlspecialchars($row['phicnum']); ?></td>
                                <td><?php echo htmlspecialchars($row['glast'] . ', ' . $row['gfirst']); ?></td>
                                <td><?php echo htmlspecialchars($row['plast'] . ', ' . $row['pfirst']); ?></td>
                                <td>
                                    <?php
                                    if (!empty($row['dateconsulted'])) {
                                        $consultDate = new DateTime($row['dateconsulted']);
                                        $today = new DateTime();
                                        $diff = $today->diff($consultDate);
                                        $daysPast = $diff->days;
                                        
                                        if ($daysPast >= 60) {
                                            echo '<i class="fas fa-exclamation-triangle text-danger me-1"></i>';
                                            echo '<span class="text-danger">' . htmlspecialchars($row['dateconsulted']) . '</span>';
                                            echo ' <small class="text-danger">(' . $daysPast . ' days ago)</small>';
                                        } elseif ($daysPast >= 30) {
                                            echo '<i class="fas fa-exclamation-circle text-warning me-1"></i>';
                                            echo '<span class="text-warning">' . htmlspecialchars($row['dateconsulted']) . '</span>';
                                            echo ' <small class="text-warning">(' . $daysPast . ' days ago)</small>';
                                        } else {
                                            echo '<i class="far fa-clock me-1"></i>';
                                            echo htmlspecialchars($row['dateconsulted']);
                                            echo ' <small class="text-muted">(' . $daysPast . ' days ago)</small>';
                                        }
                                    }
                                    ?>
                                </td>
                                <td>
                                    <span class="badge rounded-pill bg-<?php echo $row['rstatus'] == 'Pending' ? 'secondary' : ($row['rstatus'] == 'With Lacking' ? 'warning' : ($row['rstatus'] == 'Completed' ? 'success' : 'dark')); ?> px-3 py-2">
                                        <i class="fas fa-<?php echo $row['rstatus'] == 'Pending' ? 'hourglass' : ($row['rstatus'] == 'With Lacking' ? 'exclamation-triangle' : ($row['rstatus'] == 'Completed' ? 'check' : 'times')); ?> me-1"></i>
                                        <?php echo htmlspecialchars($row['rstatus'] ?? ''); ?>
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-info btn-sm rounded-3" onclick="editCase(<?php echo htmlspecialchars(json_encode($row)); ?>)">
                                        <i class="fas fa-pen-to-square me-1"></i> Edit
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Add/Edit -->
    <div class="modal fade" id="erCaseModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="modalTitle"><i class="fas fa-hospital-user me-2"></i>Add New ER Case</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="erCaseForm" method="POST" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <input type="hidden" name="original_phicnum" id="original_phicnum">
                        
                        <!-- Primary Information -->
                        <div class="card mb-4 border-primary">
                            <div class="card-header bg-primary bg-opacity-10">
                                <i class="fas fa-id-card me-2"></i>Primary Information
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label"><i class="fas fa-hashtag me-2"></i>PHIC Number</label>
                                        <input type="text" class="form-control" name="phicnum" id="phicnum" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label"><i class="fas fa-phone me-2"></i>Contact Number</label>
                                        <input type="text" class="form-control" name="contactnum" id="contactnum">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Guardian Information -->
                        <div class="card mb-4 border-info">
                            <div class="card-header bg-info bg-opacity-10">
                                <i class="fas fa-user-shield me-2"></i>Guardian Information
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <label class="form-label">Last Name</label>
                                        <input type="text" class="form-control" name="glast" id="glast" required>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">First Name</label>
                                        <input type="text" class="form-control" name="gfirst" id="gfirst" required>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Middle Name</label>
                                        <input type="text" class="form-control" name="gmid" id="gmid">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Extension</label>
                                        <input type="text" class="form-control" name="gext" id="gext">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label"><i class="fas fa-calendar me-2"></i>Birth Date</label>
                                        <input type="date" class="form-control" name="gbday" id="gbday">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Patient Information -->
                        <div class="card mb-4 border-success">
                            <div class="card-header bg-success bg-opacity-10">
                                <i class="fas fa-user-injured me-2"></i>Patient Information
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <label class="form-label">Last Name</label>
                                        <input type="text" class="form-control" name="plast" id="plast" required>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">First Name</label>
                                        <input type="text" class="form-control" name="pfirst" id="pfirst" required>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Middle Name</label>
                                        <input type="text" class="form-control" name="pmid" id="pmid">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Extension</label>
                                        <input type="text" class="form-control" name="pext" id="pext">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label"><i class="fas fa-calendar me-2"></i>Birth Date</label>
                                        <input type="date" class="form-control" name="pbday" id="pbday">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Consultation Details -->
                        <div class="card mb-4 border-warning">
                            <div class="card-header bg-warning bg-opacity-10">
                                <i class="fas fa-stethoscope me-2"></i>Consultation Details
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <label class="form-label"><i class="fas fa-calendar-day me-2"></i>Date Consulted</label>
                                        <input type="date" class="form-control" name="dateconsulted" id="dateconsulted" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label"><i class="fas fa-clock me-2"></i>Time Consulted</label>
                                        <input type="time" class="form-control" name="timeconsulted" id="timeconsulted" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label"><i class="fas fa-tasks me-2"></i>Status</label>
                                        <select class="form-select" name="rstatus" id="rstatus" required>
                                            <option value="Pending" class="text-secondary">Pending</option>
                                            <option value="With Lacking" class="text-warning">With Lacking</option>
                                            <option value="Completed" class="text-success">Completed</option>
                                            <option value="Non-PhilHealth" class="text-dark">Non-PhilHealth</option>
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label"><i class="fas fa-comment-medical me-2"></i>Remarks</label>
                                        <textarea class="form-control" name="remarks" id="remarks" rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Close
                        </button>
                        <button type="submit" class="btn btn-primary" id="submitBtn" name="submit">
                            <i class="fas fa-save me-2"></i>Save Case
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editCase(data) {
            document.getElementById('modalTitle').textContent = 'Edit ER Case';
            document.getElementById('original_phicnum').value = data.phicnum;
            
            // Fill form fields
            document.getElementById('phicnum').value = data.phicnum;
            document.getElementById('glast').value = data.glast;
            document.getElementById('gfirst').value = data.gfirst;
            document.getElementById('gmid').value = data.gmid;
            document.getElementById('gext').value = data.gext;
            document.getElementById('gbday').value = data.gbday;
            document.getElementById('plast').value = data.plast;
            document.getElementById('pfirst').value = data.pfirst;
            document.getElementById('pmid').value = data.pmid;
            document.getElementById('pext').value = data.pext;
            document.getElementById('pbday').value = data.pbday;
            document.getElementById('dateconsulted').value = data.dateconsulted;
            document.getElementById('timeconsulted').value = data.timeconsulted;
            document.getElementById('contactnum').value = data.contactnum;
            document.getElementById('rstatus').value = data.rstatus;
            document.getElementById('remarks').value = data.Remarks;
            
            // Change submit button
            document.getElementById('submitBtn').name = 'update';
            document.getElementById('submitBtn').textContent = 'Update Case';
            
            // Show modal
            new bootstrap.Modal(document.getElementById('erCaseModal')).show();
        }

        // Reset form when modal is closed
        document.getElementById('erCaseModal').addEventListener('hidden.bs.modal', function () {
            document.getElementById('erCaseForm').reset();
            document.getElementById('modalTitle').textContent = 'Add New ER Case';
            document.getElementById('submitBtn').name = 'submit';
            document.getElementById('submitBtn').textContent = 'Save Case';
        });
    </script>
</body>
</html>
