<?php
require_once '../database.php';

// Get selected month and year or default to current month
$selectedMonth = isset($_GET['month']) ? $_GET['month'] : date('Y-m');
$startDate = $selectedMonth . '-01';
$endDate = date('Y-m-t', strtotime($startDate));

// Get previous month
$prevMonth = date('Y-m', strtotime($startDate . ' -1 month'));
$prevMonthStart = $prevMonth . '-01';
$prevMonthEnd = date('Y-m-t', strtotime($prevMonthStart));

// Date logic clarification:
// - Beginning Balance: All activity BEFORE $startDate (previous months only)
// - Monthly Received: Activity BETWEEN $startDate AND $endDate (selected month only)
// - Monthly Dispensed: Activity BETWEEN $startDate AND $endDate (selected month only)
// - Monthly Returned: Activity BETWEEN $startDate AND $endDate (selected month only)

// Enhanced SQL with proper aggregation and better performance (copied from working check_duplicates.php)
$sql = "
WITH ItemLotsWithActivity AS (
    -- Get all item-lot combinations that have any activity with category info
    SELECT DISTINCT i.itemid, i.generaldescription, i.unitmeasure, i.category,
           pc.categorydesc, psl.lot_no, psl.selling_price, psl.unit_cost,
           psl.expiry_date, psl.cris_no, psl.supplier
    FROM items i
    LEFT JOIN pharmacategory pc ON i.category = pc.categoryid
    JOIN pharmacy_stock_ledger psl ON i.itemid = psl.itemid
    WHERE psl.lot_no IS NOT NULL AND psl.lot_no != ''
),
BeginningBalance AS (
    -- Calculate beginning balance per lot (everything before start date)
    -- Only for lots that actually existed before the start date
    SELECT
        psl.itemid,
        psl.lot_no,
        (COALESCE(SUM(CASE WHEN psl.date_received < ? THEN psl.qty_received ELSE 0 END), 0) -
         COALESCE(dispensed.total_dispensed, 0) -
         COALESCE(returned.total_returned, 0)) as beginning_qty
    FROM pharmacy_stock_ledger psl
    LEFT JOIN (
        SELECT psl2.itemid, psl2.lot_no, SUM(ptd.quantity) as total_dispensed
        FROM pharmatransaction_details ptd
        JOIN pharmatransactions pt ON ptd.transaction_id = pt.transaction_id
        JOIN pharmacy_stock_ledger psl2 ON ptd.itemid = psl2.itemid AND psl2.lot_no = psl2.lot_no
        WHERE pt.transaction_date < ?
        GROUP BY psl2.itemid, psl2.lot_no
    ) dispensed ON psl.itemid = dispensed.itemid AND psl.lot_no = dispensed.lot_no
    LEFT JOIN (
        SELECT itemid, lot_no, SUM(quantity_returned) as total_returned
        FROM return_to_supplier
        WHERE date_returned < ?
        GROUP BY itemid, lot_no
    ) returned ON psl.itemid = returned.itemid AND psl.lot_no = returned.lot_no
    WHERE psl.date_received < ?
    GROUP BY psl.itemid, psl.lot_no, dispensed.total_dispensed, returned.total_returned
),
MonthlyReceived AS (
    -- All deliveries/receipts for the month aggregated by itemid and lot_no
    SELECT
        ilwa.itemid,
        ilwa.lot_no,
        COALESCE(SUM(psl.qty_received), 0) as received_qty,
        COUNT(DISTINCT psl.date_received) as delivery_count,
        GROUP_CONCAT(DISTINCT DATE_FORMAT(psl.date_received, '%m/%d') ORDER BY psl.date_received SEPARATOR ', ') as delivery_dates,
        GROUP_CONCAT(DISTINCT psl.supplier ORDER BY psl.supplier SEPARATOR ', ') as suppliers
    FROM ItemLotsWithActivity ilwa
    LEFT JOIN pharmacy_stock_ledger psl ON ilwa.itemid = psl.itemid AND ilwa.lot_no = psl.lot_no
        AND psl.date_received BETWEEN ? AND ?
    GROUP BY ilwa.itemid, ilwa.lot_no
),
MonthlyDispensed AS (
    -- All dispensing/utilization for the SELECTED MONTH ONLY aggregated by itemid and lot_no
    SELECT
        ilwa.itemid,
        ilwa.lot_no,
        COALESCE(SUM(ptd.quantity), 0) as dispensed_qty,
        COUNT(DISTINCT pt.transaction_id) as transaction_count,
        COUNT(DISTINCT DATE(pt.transaction_date)) as active_days,
        MIN(DATE(pt.transaction_date)) as first_dispensed,
        MAX(DATE(pt.transaction_date)) as last_dispensed
    FROM ItemLotsWithActivity ilwa
    LEFT JOIN pharmacy_stock_ledger psl_disp ON ilwa.itemid = psl_disp.itemid AND ilwa.lot_no = psl_disp.lot_no
    LEFT JOIN pharmatransaction_details ptd ON psl_disp.itemid = ptd.itemid
    LEFT JOIN pharmatransactions pt ON ptd.transaction_id = pt.transaction_id
        AND pt.transaction_date BETWEEN ? AND ?
    GROUP BY ilwa.itemid, ilwa.lot_no
),
MonthlyReturned AS (
    -- All returns for the month aggregated by itemid and lot_no
    SELECT
        ilwa.itemid,
        ilwa.lot_no,
        COALESCE(SUM(r.quantity_returned), 0) as returned_qty,
        COUNT(DISTINCT r.return_id) as return_count,
        GROUP_CONCAT(DISTINCT r.return_reason ORDER BY r.date_returned SEPARATOR '; ') as return_reasons
    FROM ItemLotsWithActivity ilwa
    LEFT JOIN return_to_supplier r ON ilwa.itemid = r.itemid AND ilwa.lot_no = r.lot_no
        AND r.date_returned BETWEEN ? AND ?
    GROUP BY ilwa.itemid, ilwa.lot_no
)
SELECT
    ilwa.itemid,
    ilwa.generaldescription as item_name,
    ilwa.unitmeasure,
    ilwa.category,
    ilwa.categorydesc,
    ilwa.lot_no,
    ilwa.selling_price,
    ilwa.unit_cost,
    ilwa.expiry_date,
    ilwa.cris_no,
    ilwa.supplier,

    -- Beginning balance data
    COALESCE(bb.beginning_qty, 0) as beginning_qty,

    -- Monthly received data with details
    COALESCE(mr.received_qty, 0) as received_qty,
    COALESCE(mr.delivery_count, 0) as delivery_count,
    mr.delivery_dates,
    mr.suppliers,

    -- Monthly dispensed data with analytics
    COALESCE(md_disp.dispensed_qty, 0) as dispensed_qty,
    COALESCE(md_disp.transaction_count, 0) as transaction_count,
    COALESCE(md_disp.active_days, 0) as active_days,
    md_disp.first_dispensed,
    md_disp.last_dispensed,

    -- Monthly returned data
    COALESCE(mr_ret.returned_qty, 0) as returned_qty,
    COALESCE(mr_ret.return_count, 0) as return_count,
    mr_ret.return_reasons,

    -- Calculated ending balance
    (COALESCE(bb.beginning_qty, 0) +
     COALESCE(mr.received_qty, 0) -
     COALESCE(md_disp.dispensed_qty, 0) -
     COALESCE(mr_ret.returned_qty, 0)) as ending_qty

FROM ItemLotsWithActivity ilwa
LEFT JOIN BeginningBalance bb ON ilwa.itemid = bb.itemid AND ilwa.lot_no = bb.lot_no
LEFT JOIN MonthlyReceived mr ON ilwa.itemid = mr.itemid AND ilwa.lot_no = mr.lot_no
LEFT JOIN MonthlyDispensed md_disp ON ilwa.itemid = md_disp.itemid AND ilwa.lot_no = md_disp.lot_no
LEFT JOIN MonthlyReturned mr_ret ON ilwa.itemid = mr_ret.itemid AND ilwa.lot_no = mr_ret.lot_no
WHERE (bb.beginning_qty > 0 OR mr.received_qty > 0 OR md_disp.dispensed_qty > 0 OR mr_ret.returned_qty > 0)
ORDER BY ilwa.categorydesc ASC, ilwa.generaldescription ASC, ilwa.expiry_date ASC, ilwa.lot_no ASC
";

$stmt = $conn->prepare($sql);
try {
    $params = [
        $startDate,         // BeginningBalance - psl.date_received < ? (SUM CASE)
        $startDate,         // BeginningBalance - pt.transaction_date < ?
        $startDate,         // BeginningBalance - r.date_returned < ?
        $startDate,         // BeginningBalance - psl.date_received < ? (WHERE clause)
        $startDate, $endDate, // MonthlyReceived - date range
        $startDate, $endDate, // MonthlyDispensed - date range
        $startDate, $endDate  // MonthlyReturned - date range
    ];

    // Debug: Count parameters in query vs provided
    $queryParamCount = substr_count($sql, '?');
    $providedParamCount = count($params);

    if ($queryParamCount !== $providedParamCount) {
        throw new Exception("Parameter mismatch: Query expects {$queryParamCount} parameters, but {$providedParamCount} provided.");
    }

    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Remove duplicates if any exist (lot-based deduplication)
    $uniqueResults = [];
    $seenLots = [];

    foreach ($results as $row) {
        // Create a unique key based on itemid and lot_no to catch any duplicates
        $uniqueKey = $row['itemid'] . '|' . $row['lot_no'];

        if (!isset($seenLots[$uniqueKey])) {
            $uniqueResults[] = $row;
            $seenLots[$uniqueKey] = true;
        }
    }
    unset($row); // 🔥 This is crucial to prevent reference issues

    $results = $uniqueResults;

} catch (Exception $e) {
    echo "<div style='color: red; padding: 20px; border: 1px solid red; margin: 20px;'>";
    echo "<h3>Error Details:</h3>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . __FILE__ . "</p>";
    echo "<p><strong>Line:</strong> " . __LINE__ . "</p>";
    echo "</div>";
    die();
} catch (PDOException $e) {
    error_log("SQL Error: " . $e->getMessage());
    echo "<div style='color: red; padding: 20px; border: 1px solid red; margin: 20px;'>";
    echo "<h3>SQL Error Details:</h3>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . __FILE__ . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Query parameter count:</strong> " . substr_count($sql, '?') . "</p>";
    echo "<p><strong>Provided parameter count:</strong> " . count($params) . "</p>";
    echo "<p><strong>Parameters:</strong> " . htmlspecialchars(print_r($params, true)) . "</p>";
    echo "</div>";
    die();
}

// Calculate values and enhanced metrics (now per lot with accurate pricing)
foreach ($results as &$row) {
    // Ensure beginning balance is zero for new lots (lots with no previous history)
    if (is_null($row['beginning_qty'])) {
        $row['beginning_qty'] = 0;
    }

    // Calculate monetary values using selling price for more accurate valuation
    $price = $row['selling_price'] > 0 ? $row['selling_price'] : $row['unit_cost'];
    $row['beginning_value'] = $row['beginning_qty'] * $price;
    $row['received_value'] = $row['received_qty'] * $price;
    $row['dispensed_value'] = $row['dispensed_qty'] * $price;
    $row['returned_value'] = $row['returned_qty'] * $price;
    $row['ending_value'] = $row['ending_qty'] * $price;

    // Calculate utilization metrics
    $total_available = $row['beginning_qty'] + $row['received_qty'];
    $row['utilization_rate'] = $total_available > 0 ?
        round(($row['dispensed_qty'] / $total_available) * 100, 2) : 0;

    // Calculate turnover rate
    $row['turnover_rate'] = $row['received_qty'] > 0 ?
        round($row['dispensed_qty'] / $row['received_qty'], 2) : 0;

    // Days of supply calculation
    $days_in_month = date('t', strtotime($startDate));
    $daily_usage = $row['active_days'] > 0 ? $row['dispensed_qty'] / $row['active_days'] : 0;
    $row['days_supply'] = $daily_usage > 0 ? round($row['ending_qty'] / $daily_usage, 0) : 'N/A';

    // Stock status assessment
    if ($row['ending_qty'] <= 0) {
        $row['stock_status'] = 'Out of Stock';
        $row['status_class'] = 'danger';
    } elseif ($row['days_supply'] !== 'N/A' && $row['days_supply'] < 7) {
        $row['stock_status'] = 'Critical Low';
        $row['status_class'] = 'danger';
    } elseif ($row['days_supply'] !== 'N/A' && $row['days_supply'] < 14) {
        $row['stock_status'] = 'Low Stock';
        $row['status_class'] = 'warning';
    } else {
        $row['stock_status'] = 'Adequate';
        $row['status_class'] = 'success';
    }

    // Store all calculations in balances array for backward compatibility
    $row['balances'] = [
        'beginning' => ['qty' => $row['beginning_qty'], 'unit_cost' => $row['unit_cost'], 'total' => $row['beginning_value']],
        'received' => ['qty' => $row['received_qty'], 'unit_cost' => $row['unit_cost'], 'total' => $row['received_value']],
        'dispensed' => ['qty' => $row['dispensed_qty'], 'unit_cost' => $row['unit_cost'], 'total' => $row['dispensed_value']],
        'returned' => ['qty' => $row['returned_qty'], 'unit_cost' => $row['unit_cost'], 'total' => $row['returned_value']],
        'ending' => ['qty' => $row['ending_qty'], 'unit_cost' => $row['unit_cost'], 'total' => $row['ending_value']]
    ];
}
unset($row); // 🔥 This is crucial to prevent reference issues

// Group results by category for better organization (now with lot-based data)
$groupedResults = [];
$categoryTotals = [];

foreach ($results as $row) {
    $category = $row['categorydesc'] ?: 'Uncategorized';

    if (!isset($groupedResults[$category])) {
        $groupedResults[$category] = [];
        $categoryTotals[$category] = [
            'lots_count' => 0,
            'unique_items' => [],
            'total_beginning_qty' => 0,
            'total_received_qty' => 0,
            'total_dispensed_qty' => 0,
            'total_ending_qty' => 0,
            'total_beginning_value' => 0,
            'total_received_value' => 0,
            'total_dispensed_value' => 0,
            'total_ending_value' => 0,
            'total_transactions' => 0,
            'total_deliveries' => 0
        ];
    }

    $groupedResults[$category][] = $row;

    // Track unique items per category
    $categoryTotals[$category]['unique_items'][$row['itemid']] = $row['item_name'];

    // Calculate category totals (now per lot)
    $categoryTotals[$category]['lots_count']++;
    $categoryTotals[$category]['total_beginning_qty'] += $row['beginning_qty'];
    $categoryTotals[$category]['total_received_qty'] += $row['received_qty'];
    $categoryTotals[$category]['total_dispensed_qty'] += $row['dispensed_qty'];
    $categoryTotals[$category]['total_ending_qty'] += $row['ending_qty'];
    $categoryTotals[$category]['total_beginning_value'] += $row['beginning_value'];
    $categoryTotals[$category]['total_received_value'] += $row['received_value'];
    $categoryTotals[$category]['total_dispensed_value'] += $row['dispensed_value'];
    $categoryTotals[$category]['total_ending_value'] += $row['ending_value'];
    $categoryTotals[$category]['total_transactions'] += $row['transaction_count'];
    $categoryTotals[$category]['total_deliveries'] += $row['delivery_count'];
}

// Convert unique items array to count
foreach ($categoryTotals as &$totals) {
    $totals['items_count'] = count($totals['unique_items']);
    unset($totals['unique_items']); // Remove the array to save memory
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monthly Utilization Report</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Professional styling with minimal colors */
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
        }

        .report-header {
            border-bottom: 3px solid #dee2e6;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .report-title {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .report-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
        }

        /* Category styling - professional and clean */
        .category-header {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: 4px solid #007bff;
        }

        .category-header td {
            padding: 15px !important;
            vertical-align: middle;
        }

        .category-title {
            color: #2c3e50;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .category-summary {
            background: #f1f3f4;
            border: 1px solid #dee2e6;
            font-weight: 500;
        }

        /* Table improvements */
        .table-responsive {
            max-height: 75vh;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }

        .sticky-header {
            position: sticky;
            top: 0;
            z-index: 10;
            background: #343a40 !important;
        }

        .table th {
            font-weight: 600;
            font-size: 0.85rem;
            text-align: center;
            vertical-align: middle;
            border-color: #495057;
            white-space: nowrap;
        }

        .table td {
            font-size: 0.9rem;
            vertical-align: middle;
            border-color: #dee2e6;
        }

        /* Group headers for table sections */
        .group-header {
            background: #e9ecef;
            font-weight: 600;
            color: #495057;
            text-align: center;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Professional badges */
        .badge {
            font-size: 0.75rem;
            font-weight: 500;
        }

        .lot-badge {
            background: #6c757d;
            color: white;
            font-family: 'Courier New', monospace;
        }

        /* Toggle button styling */
        .category-toggle {
            border: none;
            background: #007bff;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .category-toggle:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        /* Collapsible content */
        .category-items {
            transition: all 0.3s ease;
        }

        /* Professional spacing */
        .item-row {
            border-left: 3px solid transparent;
        }

        .item-row:hover {
            background-color: #f8f9fa;
            border-left-color: #007bff;
        }

        /* Controls section */
        .controls-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                padding: 15px;
            }

            .table th, .table td {
                font-size: 0.8rem;
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="report-header">
            <h2 class="report-title">
                <i class="fas fa-chart-line me-3"></i>
                Monthly Utilization Report (MUR)
            </h2>
            <p class="report-subtitle">
                Comprehensive monthly analysis of pharmacy inventory utilization by lot number
            </p>
        </div>

        <div class="controls-section">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <form method="GET" class="d-flex align-items-center">
                        <label class="me-2 fw-semibold">Report Period:</label>
                        <input type="month" class="form-control me-3" name="month"
                               value="<?php echo $selectedMonth; ?>" onchange="this.form.submit()">
                    </form>
                </div>
                <div>
                    <a href="pharmacydashboard.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-home me-1"></i>Dashboard
                    </a>
                    <a href="mur_report.php?month=<?php echo $selectedMonth; ?>" class="btn btn-primary" target="_blank">
                        <i class="fas fa-print me-1"></i>Print Report
                    </a>
                </div>
            </div>
        </div>
                <!-- Summary Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="text-primary mb-2">
                            <i class="fas fa-boxes fa-2x"></i>
                        </div>
                        <h4 class="fw-bold text-dark"><?php echo count($results); ?></h4>
                        <small class="text-muted">Total Lots</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="text-success mb-2">
                            <i class="fas fa-truck fa-2x"></i>
                        </div>
                        <h4 class="fw-bold text-dark"><?php echo array_sum(array_column($results, 'delivery_count')); ?></h4>
                        <small class="text-muted">Total Deliveries</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="text-warning mb-2">
                            <i class="fas fa-exchange-alt fa-2x"></i>
                        </div>
                        <h4 class="fw-bold text-dark"><?php echo array_sum(array_column($results, 'transaction_count')); ?></h4>
                        <small class="text-muted">Total Transactions</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="text-danger mb-2">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                        <h4 class="fw-bold text-dark"><?php echo count(array_filter($results, function($r) { return $r['stock_status'] === 'Out of Stock' || $r['stock_status'] === 'Critical Low'; })); ?></h4>
                        <small class="text-muted">Critical Items</small>
                    </div>
                </div>
            </div>
        </div>

                <!-- Enhanced Table -->
                <div class="table-responsive">
                    <table class="table table-hover table-sm">
                        <thead class="table-dark sticky-header">
                            <tr>
                                <th rowspan="3" class="text-center align-middle">Item Description</th>
                                <th rowspan="3" class="text-center align-middle">Unit</th>
                                <th rowspan="3" class="text-center align-middle">Lot Number</th>
                                <th rowspan="3" class="text-center align-middle">Expiry Date</th>
                                <th rowspan="3" class="text-center align-middle">Unit Price</th>
                                <th rowspan="3" class="text-center align-middle">Selling Price</th>
                                <th colspan="2" class="group-header">Beginning</th>
                                <th colspan="4" class="group-header">Received (Monthly)</th>
                                <th colspan="4" class="group-header">Dispensed (Monthly)</th>
                                <th colspan="2" class="group-header">Returned</th>
                                <th colspan="2" class="group-header">Ending</th>
                                <th colspan="3" class="group-header">Indicators</th>
                            </tr>
                            <tr>
                                <th class="text-center">Qty</th>
                                <th class="text-center">Value</th>
                                <th class="text-center">Qty</th>
                                <th class="text-center">Deliveries</th>
                                <th class="text-center">Suppliers</th>
                                <th class="text-center">Value</th>
                                <th class="text-center">Qty</th>
                                <th class="text-center">Transactions</th>
                                <th class="text-center">Active Days</th>
                                <th class="text-center">Value</th>
                                <th class="text-center">Qty</th>
                                <th class="text-center">Value</th>
                                <th class="text-center">Qty</th>
                                <th class="text-center">Value</th>
                                <th class="text-center">Stock Status</th>
                                <th class="text-center">Days Supply</th>
                                <th class="text-center">Utilization %</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($groupedResults as $category => $categoryItems): ?>
                                <!-- Category Header Row -->
                                <tr class="category-header">
                                    <td colspan="21">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <span class="category-title">
                                                    <i class="fas fa-folder-open me-2"></i>
                                                    <?php echo htmlspecialchars($category); ?>
                                                </span>
                                                <span class="badge bg-primary ms-3"><?php echo $categoryTotals[$category]['items_count']; ?> items</span>
                                                <span class="badge bg-secondary ms-1"><?php echo $categoryTotals[$category]['lots_count']; ?> lots</span>
                                                <span class="badge bg-success ms-1">₱<?php echo number_format($categoryTotals[$category]['total_ending_value'], 2); ?></span>
                                            </div>
                                            <button class="category-toggle" type="button"
                                                    onclick="toggleCategory('<?php echo md5($category); ?>')">
                                                <i class="fas fa-chevron-down" id="icon-<?php echo md5($category); ?>"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Category Items -->
                                <?php foreach ($categoryItems as $row): ?>
                                <tbody class="category-items" id="category-<?php echo md5($category); ?>">
                                    <tr class="item-row">
                                        <td class="ps-4"><?php echo htmlspecialchars($row['item_name']); ?></td>
                                        <td class="text-center"><?php echo htmlspecialchars($row['unitmeasure']); ?></td>
                                        <td class="text-center">
                                            <span class="badge lot-badge">
                                                <i class="fas fa-barcode me-1"></i>
                                                <?php echo htmlspecialchars($row['lot_no']); ?>
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <?php
                                            $expiryDate = strtotime($row['expiry_date']);
                                            $today = strtotime('today');
                                            $daysToExpiry = ($expiryDate - $today) / (60 * 60 * 24);

                                            if ($daysToExpiry < 30) {
                                                $badgeClass = 'bg-danger text-white';
                                            } elseif ($daysToExpiry < 90) {
                                                $badgeClass = 'bg-warning text-dark';
                                            } else {
                                                $badgeClass = 'bg-success text-white';
                                            }
                                            ?>
                                            <span class="badge <?php echo $badgeClass; ?>">
                                                <?php echo date('M d, Y', strtotime($row['expiry_date'])); ?>
                                            </span>
                                        </td>
                                        <td class="text-center">₱<?php echo number_format($row['unit_cost'], 2); ?></td>
                                        <td class="text-center fw-bold text-primary">₱<?php echo number_format($row['selling_price'], 2); ?></td>

                                <!-- Beginning Balance -->
                                <td class="text-center"><?php echo number_format($row['beginning_qty']); ?></td>
                                <td class="text-center">₱<?php echo number_format($row['beginning_value'], 2); ?></td>

                                <!-- Received (Monthly Aggregated) -->
                                <td class="text-center fw-bold text-success"><?php echo number_format($row['received_qty']); ?></td>
                                <td class="text-center">
                                    <span class="badge bg-info"><?php echo $row['delivery_count']; ?></span>
                                    <?php if ($row['delivery_dates']): ?>
                                    <br><small class="text-muted"><?php echo $row['delivery_dates']; ?></small>
                                    <?php endif; ?>
                                </td>
                                <td class="text-center">
                                    <?php if ($row['suppliers']): ?>
                                    <small class="text-muted"><?php echo htmlspecialchars(substr($row['suppliers'], 0, 30)) . (strlen($row['suppliers']) > 30 ? '...' : ''); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td class="text-center">₱<?php echo number_format($row['received_value'], 2); ?></td>

                                <!-- Dispensed (Monthly Aggregated) -->
                                <td class="text-center fw-bold text-warning"><?php echo number_format($row['dispensed_qty']); ?></td>
                                <td class="text-center">
                                    <span class="badge bg-warning"><?php echo $row['transaction_count']; ?></span>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-secondary"><?php echo $row['active_days']; ?> days</span>
                                    <?php if ($row['first_dispensed'] && $row['last_dispensed']): ?>
                                    <br><small class="text-muted"><?php echo date('m/d', strtotime($row['first_dispensed'])); ?> - <?php echo date('m/d', strtotime($row['last_dispensed'])); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td class="text-center">₱<?php echo number_format($row['dispensed_value'], 2); ?></td>

                                <!-- Returned -->
                                <td class="text-center text-danger"><?php echo number_format($row['returned_qty']); ?></td>
                                <td class="text-center">₱<?php echo number_format($row['returned_value'], 2); ?></td>

                                <!-- Ending Balance -->
                                <td class="text-center fw-bold"><?php echo number_format($row['ending_qty']); ?></td>
                                <td class="text-center fw-bold">₱<?php echo number_format($row['ending_value'], 2); ?></td>

                                <!-- Indicators - All Centered -->
                                <td class="text-center">
                                    <span class="badge bg-<?php echo $row['status_class']; ?>"><?php echo $row['stock_status']; ?></span>
                                </td>

                                <td class="text-center">
                                    <?php if ($row['days_supply'] !== 'N/A'): ?>
                                    <span class="badge <?php echo $row['days_supply'] < 7 ? 'bg-danger' : ($row['days_supply'] < 14 ? 'bg-warning' : 'bg-success'); ?>">
                                        <?php echo $row['days_supply']; ?> days
                                    </span>
                                    <?php else: ?>
                                    <span class="text-muted">N/A</span>
                                    <?php endif; ?>
                                </td>

                                <td class="text-center">
                                    <span class="badge <?php echo $row['utilization_rate'] > 80 ? 'bg-success' : ($row['utilization_rate'] > 50 ? 'bg-warning' : 'bg-danger'); ?>">
                                        <?php echo $row['utilization_rate']; ?>%
                                    </span>
                                </td>
                            </tr>
                                </tbody>
                            <?php endforeach; ?>

                                <!-- Category Summary Row -->
                                <tr class="category-summary">
                                    <td colspan="6" class="fw-bold text-end py-2">
                                        <i class="fas fa-calculator me-2"></i>
                                        <span>Category Total:</span>
                                    </td>
                                    <td class="text-center fw-bold"><?php echo number_format($categoryTotals[$category]['total_beginning_qty']); ?></td>
                                    <td class="text-center fw-bold">₱<?php echo number_format($categoryTotals[$category]['total_beginning_value'], 2); ?></td>
                                    <td class="text-center fw-bold text-success"><?php echo number_format($categoryTotals[$category]['total_received_qty']); ?></td>
                                    <td class="text-center">
                                        <span class="badge bg-info"><?php echo $categoryTotals[$category]['total_deliveries']; ?></span>
                                    </td>
                                    <td class="text-center">-</td>
                                    <td class="text-center fw-bold">₱<?php echo number_format($categoryTotals[$category]['total_received_value'], 2); ?></td>
                                    <td class="text-center fw-bold text-warning"><?php echo number_format($categoryTotals[$category]['total_dispensed_qty']); ?></td>
                                    <td class="text-center">
                                        <span class="badge bg-warning"><?php echo $categoryTotals[$category]['total_transactions']; ?></span>
                                    </td>
                                    <td class="text-center">-</td>
                                    <td class="text-center fw-bold">₱<?php echo number_format($categoryTotals[$category]['total_dispensed_value'], 2); ?></td>
                                    <td class="text-center">-</td>
                                    <td class="text-center">-</td>
                                    <td class="text-center fw-bold"><?php echo number_format($categoryTotals[$category]['total_ending_qty']); ?></td>
                                    <td class="text-center fw-bold">₱<?php echo number_format($categoryTotals[$category]['total_ending_value'], 2); ?></td>
                                    <td colspan="3" class="text-center">
                                        <span class="badge bg-primary"><?php echo $categoryTotals[$category]['items_count']; ?> items</span>
                                        <span class="badge bg-secondary ms-1"><?php echo $categoryTotals[$category]['lots_count']; ?> lots</span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Summary Totals -->
                <div class="card-footer bg-light">
                    <div class="row">
                        <div class="col-md-12">
                            <h5 class="mb-3"><i class="fas fa-calculator me-2"></i>Monthly Summary</h5>
                            <div class="row">
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h6 class="text-info">Beginning Value</h6>
                                        <h4>₱<?php echo number_format(array_sum(array_column($results, 'beginning_value')), 2); ?></h4>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h6 class="text-success">Received Value</h6>
                                        <h4>₱<?php echo number_format(array_sum(array_column($results, 'received_value')), 2); ?></h4>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h6 class="text-warning">Dispensed Value</h6>
                                        <h4>₱<?php echo number_format(array_sum(array_column($results, 'dispensed_value')), 2); ?></h4>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h6 class="text-danger">Returned Value</h6>
                                        <h4>₱<?php echo number_format(array_sum(array_column($results, 'returned_value')), 2); ?></h4>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h6 class="text-primary">Ending Value</h6>
                                        <h4>₱<?php echo number_format(array_sum(array_column($results, 'ending_value')), 2); ?></h4>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h6 class="text-secondary">Avg Utilization</h6>
                                        <h4><?php echo number_format(array_sum(array_column($results, 'utilization_rate')) / count($results), 1); ?>%</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Professional toggle functionality
        function toggleCategory(categoryId) {
            const categoryItems = document.getElementById('category-' + categoryId);
            const icon = document.getElementById('icon-' + categoryId);

            if (categoryItems.style.display === 'none') {
                categoryItems.style.display = '';
                icon.classList.remove('fa-chevron-right');
                icon.classList.add('fa-chevron-down');
            } else {
                categoryItems.style.display = 'none';
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-right');
            }
        }

        // Expand/Collapse all functionality
        function expandAll() {
            document.querySelectorAll('.category-items').forEach(items => {
                items.style.display = '';
            });
            document.querySelectorAll('.category-toggle i').forEach(icon => {
                icon.classList.remove('fa-chevron-right');
                icon.classList.add('fa-chevron-down');
            });
        }

        function collapseAll() {
            document.querySelectorAll('.category-items').forEach(items => {
                items.style.display = 'none';
            });
            document.querySelectorAll('.category-toggle i').forEach(icon => {
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-right');
            });
        }

        // Add control buttons when page loads
        document.addEventListener('DOMContentLoaded', function() {
            const tableContainer = document.querySelector('.table-responsive');
            const controlsDiv = document.createElement('div');
            controlsDiv.className = 'mb-3 d-flex justify-content-end';
            controlsDiv.innerHTML = `
                <button class="btn btn-outline-secondary btn-sm me-2" onclick="expandAll()">
                    <i class="fas fa-expand-arrows-alt me-1"></i>Expand All
                </button>
                <button class="btn btn-outline-secondary btn-sm" onclick="collapseAll()">
                    <i class="fas fa-compress-arrows-alt me-1"></i>Collapse All
                </button>
            `;
            tableContainer.parentNode.insertBefore(controlsDiv, tableContainer);
        });
    </script>
    </div> <!-- Close main-container -->
</body>
</html>
