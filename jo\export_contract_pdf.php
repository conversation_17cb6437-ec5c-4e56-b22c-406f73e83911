<?php
require_once '../database.php';
require('../fpdf/fpdf.php');

// Get profile ID from URL
$profile_id = isset($_GET['id']) ? $_GET['id'] : null;
if (!$profile_id) {
    die('Profile ID not provided');
}

try {
    // Fetch employee profile with error handling
    $stmt = $conn->prepare("SELECT * FROM jo_profile WHERE profile_id = ?");
    $stmt->execute([$profile_id]);
    $profile = $stmt->fetch();
    
    if (!$profile) {
        die('Employee profile not found');
    }

    // Fetch contract history with error handling
    $stmt = $conn->prepare("
        SELECT * FROM jo_contract 
        WHERE employee_id = ? 
        ORDER BY appointment_from DESC");
    $stmt->execute([$profile['employee_id']]);
    $contracts = $stmt->fetchAll();

    // Set page margins
    $pageWidth = 210; // A4 width in mm
    $margins = 20; // Equal margins for left and right
    $tableWidth = $pageWidth - (2 * $margins); // Available width for content
    
    // Create PDF document with modern styling
    $pdf = new FPDF('P', 'mm', 'A4');
    $pdf->AddPage();
    $pdf->SetMargins($margins, $margins, $margins);
    
    // Add logos with better spacing
    if (file_exists('../images/pgns.png') && file_exists('../images/bdh.png')) {
        $pdf->Image('../images/pgns.png', 50, 15, 20);
        $pdf->Image('../images/bdh.png', $pageWidth - 70, 15, 20);
    }

    // Modern header section with smaller fonts
    $pdf->SetFont('Arial', '', 10);
    $pdf->Cell(0, 5, 'Republic of the Philippines', 0, 1, 'C');
    $pdf->Cell(0, 5, 'Province of Northern Samar', 0, 1, 'C');
    $pdf->Cell(0, 5, 'Provincial Health Office', 0, 1, 'C');
    $pdf->SetFont('Arial', 'B', 14);
    $pdf->Cell(0, 7, 'BIRI DISTRICT HOSPITAL', 0, 1, 'C');
    $pdf->SetFont('Arial', 'I', 9);
    $pdf->Cell(0, 5, 'Biri Northern Samar', 0, 1, 'C');

    // Professional certification title with spacing
    $pdf->Ln(8);
    $pdf->SetFont('Arial', 'B', 14);
    $pdf->Cell(0, 8, 'CERTIFICATION', 0, 1, 'C');
    
    // Clean content layout
    $pdf->Ln(3);
    $pdf->SetFont('Arial', '', 10);
    $pdf->MultiCell(0, 6, 'This is to CERTIFY that based on the records of this office, the following is the contract history of:', 0, 'J');
    
    // Structured employee information
    $pdf->Ln(2);
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(35, 6, 'Name:', 0, 0);
    $pdf->Cell(0, 6, strtoupper($profile['jo_name']), 0, 1);
    $pdf->Cell(35, 6, 'Birthday:', 0, 0);
    $pdf->Cell(0, 6, date('F d, Y', strtotime($profile['jo_birthday'])), 0, 1);
    $pdf->Cell(35, 6, 'Address:', 0, 0);
    $pdf->Cell(0, 6, $profile['jo_address'], 0, 1);

    // Modern table design
    $pdf->Ln(3);
    $pdf->SetFillColor(240, 240, 240);
    $pdf->SetFont('Arial', 'B', 9);

    // Adjusted cell widths to fit the table width
    $pdf->Cell($tableWidth * 0.26, 7, 'Contract Period', 1, 0, 'C', true);
    $pdf->Cell($tableWidth * 0.21, 7, 'Daily Rate', 1, 0, 'C', true);
    $pdf->Cell($tableWidth * 0.26, 7, 'Designation', 1, 0, 'C', true);
    $pdf->Cell($tableWidth * 0.27, 7, 'Assignment Area', 1, 1, 'C', true);

    $pdf->SetFont('Arial', '', 9);
    foreach ($contracts as $contract) {
        if ($pdf->GetY() > 220) {
            $pdf->AddPage();
            $pdf->SetFont('Arial', 'B', 9);
            $pdf->Cell($tableWidth * 0.26, 7, 'Contract Period', 1, 0, 'C', true);
            $pdf->Cell($tableWidth * 0.21, 7, 'Daily Rate', 1, 0, 'C', true);
            $pdf->Cell($tableWidth * 0.26, 7, 'Designation', 1, 0, 'C', true);
            $pdf->Cell($tableWidth * 0.27, 7, 'Assignment Area', 1, 1, 'C', true);
            $pdf->SetFont('Arial', '', 9);
        }
        
        $period = date('M d, Y', strtotime($contract['appointment_from'])) . ' - ' . 
                 date('M d, Y', strtotime($contract['appointment_to']));
        
        $pdf->Cell($tableWidth * 0.26, 6, $period, 1, 0, 'C');
        $pdf->Cell($tableWidth * 0.21, 6, number_format($contract['rate'], 2), 1, 0, 'C');
        $pdf->Cell($tableWidth * 0.26, 6, $contract['designation'], 1, 0, 'C');
        $pdf->Cell($tableWidth * 0.27, 6, $contract['assignment_area'], 1, 1, 'C');
    }
    // Calculate total service period
    $sql = "SELECT COALESCE(SUM(
            TIMESTAMPDIFF(MONTH, c.appointment_from, COALESCE(c.appointment_to, CURRENT_DATE)) + 1
        ), 0) as total_months_service 
        FROM jo_contract c 
        WHERE c.employee_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$profile['employee_id']]);
    $service = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $total_months = (int)$service['total_months_service'];
    $years = floor($total_months / 12);
    $months = $total_months % 12;
    
    $periodInWords = '';
    if ($years > 0 && $months > 0) {
        $periodInWords = $years . " year" . ($years > 1 ? "s" : "") . " and " . $months . " month" . ($months > 1 ? "s" : "");
    } elseif ($years > 0) {
        $periodInWords = $years . " year" . ($years > 1 ? "s" : "");
    } else {
        $periodInWords = $months . " month" . ($months > 1 ? "s" : "");
    }
    
    $pdf->Ln(5);
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(0, 6, 'Total Service Period: ' . $periodInWords, 0, 1, 'L');
    $pdf->Ln(3);
    // Space management
    $remainingSpace = 270 - $pdf->GetY();
    $neededSpace = 50;

    if ($remainingSpace < $neededSpace) {
        $pdf->AddPage();
    }

    // Professional footer
    $pdf->Ln(5);
    $pdf->SetFont('Arial', '', 10);
    $pdf->MultiCell(0, 6, 'This certification is being issued upon request of the above-named person for whatever legal purpose it may serve.', 0, 'J');
    
    $pdf->Ln(3);
    $pdf->Cell(0, 6, 'Issued this ' . date('jS') . ' day of ' . date('F Y') . ' at Biri District Hospital, Biri, Northern Samar.', 0, 1, 'L');

    // Clean signatory layout
    $pdf->Ln(15);
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(120, 5, '', 0, 0);
    $pdf->Cell(70, 5, 'CHRISTEL D. BRAVO', 0, 1, 'C');
    $pdf->SetFont('Arial', '', 9);
    $pdf->Cell(120, 4, '', 0, 0);
    $pdf->Cell(70, 4, 'Administrative Officer IV', 0, 1, 'C');

    // Output PDF
    $pdf->Output('Contract_History_' . $profile['jo_name'] . '.pdf', 'D');

} catch (PDOException $e) {
    die('Database Error: ' . $e->getMessage());
} catch (Exception $e) {
    die('Error: ' . $e->getMessage());
}
