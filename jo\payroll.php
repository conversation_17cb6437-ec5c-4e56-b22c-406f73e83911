<?php
require_once '../database.php';

// Create payroll table if not exists
try {
    $sql = "CREATE TABLE IF NOT EXISTS payroll (
        id INT AUTO_INCREMENT PRIMARY KEY,
        profile_id INT,
        pay_period DATE NOT NULL,
        num_days_work DECIMAL(5,2) NOT NULL,
        minutes_lates INT NOT NULL,
        amount_paid DECIMAL(10,2) NOT NULL,
        FOREIGN KEY (profile_id) REFERENCES jo_profile(profile_id)
    )";
    $conn->exec($sql);
} catch(PDOException $e) {
    error_log("Error creating table: " . $e->getMessage());
    echo "<div class='alert alert-danger'>An error occurred while setting up the database.</div>";
}

// Get all employee records for the dropdown
try {
    $stmt = $conn->query("SELECT profile_id, jo_name FROM jo_profile ORDER BY jo_name");
    $employee_records = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    error_log("Error fetching employees: " . $e->getMessage());
    $employee_records = [];
}

// Handle form submissions
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['save_payroll'])) {
        try {
            $stmt = $conn->prepare("INSERT INTO payroll (profile_id, pay_period, num_days_work, minutes_lates, amount_paid) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([
                $_POST['profile_id'],
                $_POST['pay_period'],
                $_POST['num_days_work'],
                $_POST['minutes_lates'],
                $_POST['amount_paid']
            ]);
            echo "<div class='alert alert-success'>Payroll record added successfully!</div>";
        } catch(PDOException $e) {
            error_log("Error adding payroll: " . $e->getMessage());
            echo "<div class='alert alert-danger'>Failed to add payroll record.</div>";
        }
    }
}

// Fetch payroll records
try {
    $stmt = $conn->query("SELECT p.*, jp.jo_name FROM payroll p JOIN jo_profile jp ON p.profile_id = jp.profile_id ORDER BY p.pay_period DESC");
    $payroll_records = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    error_log("Error fetching payroll records: " . $e->getMessage());
    $payroll_records = [];
}

// Get latest contract details
try {
    $stmt = $conn->prepare("
        SELECT jc.*, jp.jo_name 
        FROM jo_contract jc
        INNER JOIN jo_profile jp ON jp.employee_id = jc.employee_id
        WHERE jc.employee_id = ?
        ORDER BY jc.contract_id DESC 
        LIMIT 1
    ");
    $contract_details = $stmt->fetch(PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    error_log("Error fetching contract details: " . $e->getMessage());
    $contract_details = null;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payroll Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding: 20px;
        }
        .card {
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0">Payroll Management</h3>
            </div>
            <div class="card-body">
                <button class="btn btn-success mb-3" data-bs-toggle="modal" data-bs-target="#addPayrollModal">
                    <i class="fas fa-plus"></i> Add New Payroll
                </button>
                
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Employee Name</th>
                                <th>Pay Period</th>
                                <th>Days Worked</th>
                                <th>Minutes Late</th>
                                <th>Amount Paid</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($payroll_records as $record): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($record['jo_name']); ?></td>
                                <td><?php echo date('F d, Y', strtotime($record['pay_period'])); ?></td>
                                <td><?php echo $record['num_days_work']; ?></td>
                                <td><?php echo $record['minutes_lates']; ?></td>
                                <td>₱<?php echo number_format($record['amount_paid'], 2); ?></td>
                                <td>
                                    <button class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Payroll Modal -->
    <div class="modal fade" id="addPayrollModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Payroll Record</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">Employee</label>
                            <select name="profile_id" class="form-select" id="employeeSelect" required>
                                <option value="">Select Employee</option>
                                <?php foreach ($employee_records as $employee): ?>
                                    <option value="<?php echo $employee['profile_id']; ?>">
                                        <?php echo htmlspecialchars($employee['jo_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div id="contractInfo" style="display:none" class="mb-3 card p-3">
                            <h6>Contract Details</h6>
                            <div id="contractDetails"></div>
                        </div>
                        <script>
                            document.getElementById('employeeSelect').addEventListener('change', function() {
                                const profileId = this.value;
                                if(profileId) {
                                    fetch(`get_contract.php?profile_id=${profileId}`)
                                        .then(response => response.json())
                                        .then(data => {
                                            if(data) {
                                                document.getElementById('contractInfo').style.display = 'block';
                                                document.getElementById('contractDetails').innerHTML = `
                                                    <p><strong>Designation:</strong> ${data.designation}</p>
                                                    <p><strong>Rate:</strong> ₱${data.rate}</p>
                                                    <p><strong>Assignment Area:</strong> ${data.assignment_area}</p>
                                                    <p><strong>Contract Period:</strong> ${data.appointment_from} to ${data.appointment_to}</p>
                                                `;
                                            }
                                        });
                                } else {
                                    document.getElementById('contractInfo').style.display = 'none';
                                }
                            });
                        </script>
                        <div class="mb-3">
                            <label class="form-label">Pay Period</label>
                            <input type="date" name="pay_period" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Days Worked</label>
                            <input type="number" step="0.01" name="num_days_work" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Minutes Late</label>
                            <input type="number" name="minutes_lates" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Amount Paid</label>
                            <input type="number" step="0.01" name="amount_paid" class="form-control" required>
                        </div>
                        <button type="submit" name="save_payroll" class="btn btn-primary">Save Payroll</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>        document.addEventListener('DOMContentLoaded', function() {
            // Calculate amount based on days worked            const daysWorkedInput = document.querySelector('input[name="num_days_work"]');
            const amountPaidInput = document.querySelector('input[name="amount_paid"]');            
            daysWorkedInput.addEventListener('input', function() {                // Assuming daily rate of 500
                const dailyRate = 500;                const daysWorked = parseFloat(this.value) || 0;
                const amount = daysWorked * dailyRate;                amountPaidInput.value = amount.toFixed(2);
            });        });
    </script>
</body>
</html>







