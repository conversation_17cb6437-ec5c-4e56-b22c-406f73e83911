<?php
require_once '../database.php';

// Create medication monitoring categories table
$sql = "CREATE TABLE IF NOT EXISTS medication_monitoring_categories (
    category_id INT AUTO_INCREMENT PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL UNIQUE,
    category_description TEXT,
    risk_level ENUM('Low', 'Medium', 'High', 'Critical') DEFAULT 'Medium',
    monitoring_frequency_days INT DEFAULT 30,
    requires_lab_monitoring BOOLEAN DEFAULT FALSE,
    requires_vital_monitoring BOOLEAN DEFAULT FALSE,
    requires_symptom_monitoring BOOLEAN DEFAULT TRUE,
    age_specific_monitoring BOOLEAN DEFAULT FALSE,
    beers_criteria BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

try {
    $conn->exec($sql);
    echo "Medication monitoring categories table created successfully.<br>";
} catch(PDOException $e) {
    die("Error creating medication_monitoring_categories table: " . $e->getMessage());
}

// Create medication monitoring rules table
$sql = "CREATE TABLE IF NOT EXISTS medication_monitoring_rules (
    rule_id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT,
    medication_name VARCHAR(255),
    generic_name VARCHAR(255),
    monitoring_parameters TEXT, -- JSON format for parameters to monitor
    frequency_days INT DEFAULT 30,
    lab_tests_required TEXT, -- JSON format for required lab tests
    vital_signs_required TEXT, -- JSON format for vital signs to monitor
    symptoms_to_monitor TEXT, -- JSON format for symptoms to watch
    contraindications TEXT,
    age_restrictions TEXT, -- JSON format for age-specific rules
    dosage_considerations TEXT,
    interaction_warnings TEXT,
    therapeutic_range_min DECIMAL(10,4) NULL,
    therapeutic_range_max DECIMAL(10,4) NULL,
    toxic_level DECIMAL(10,4) NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES medication_monitoring_categories(category_id)
)";

try {
    $conn->exec($sql);
    echo "Medication monitoring rules table created successfully.<br>";
} catch(PDOException $e) {
    die("Error creating medication_monitoring_rules table: " . $e->getMessage());
}

// Create patient medication monitoring table
$sql = "CREATE TABLE IF NOT EXISTS patient_medication_monitoring (
    monitoring_id INT AUTO_INCREMENT PRIMARY KEY,
    patientid INT,
    itemid INT,
    rule_id INT,
    transaction_id INT,
    start_date DATE,
    next_monitoring_date DATE,
    monitoring_status ENUM('Active', 'Completed', 'Discontinued', 'Overdue') DEFAULT 'Active',
    priority_level ENUM('Low', 'Medium', 'High', 'Critical') DEFAULT 'Medium',
    prescribing_doctor INT,
    monitoring_notes TEXT,
    last_monitoring_date DATE NULL,
    monitoring_results TEXT, -- JSON format for monitoring results
    alerts_generated INT DEFAULT 0,
    compliance_score DECIMAL(5,2) DEFAULT 100.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (patientid) REFERENCES patient(patientid),
    FOREIGN KEY (itemid) REFERENCES items(itemid),
    FOREIGN KEY (rule_id) REFERENCES medication_monitoring_rules(rule_id),
    FOREIGN KEY (transaction_id) REFERENCES pharmatransactions(transaction_id),
    FOREIGN KEY (prescribing_doctor) REFERENCES doctors(doctorid)
)";

try {
    $conn->exec($sql);
    echo "Patient medication monitoring table created successfully.<br>";
} catch(PDOException $e) {
    die("Error creating patient_medication_monitoring table: " . $e->getMessage());
}

// Create monitoring alerts table
$sql = "CREATE TABLE IF NOT EXISTS medication_monitoring_alerts (
    alert_id INT AUTO_INCREMENT PRIMARY KEY,
    monitoring_id INT,
    patientid INT,
    alert_type ENUM('Due', 'Overdue', 'Critical', 'Lab_Required', 'Interaction', 'Age_Warning', 'Beers_Criteria') NOT NULL,
    alert_priority ENUM('Low', 'Medium', 'High', 'Critical') DEFAULT 'Medium',
    alert_message TEXT NOT NULL,
    alert_details TEXT, -- JSON format for detailed alert information
    is_acknowledged BOOLEAN DEFAULT FALSE,
    acknowledged_by INT NULL,
    acknowledged_at TIMESTAMP NULL,
    is_resolved BOOLEAN DEFAULT FALSE,
    resolved_by INT NULL,
    resolved_at TIMESTAMP NULL,
    resolution_notes TEXT,
    auto_generated BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (monitoring_id) REFERENCES patient_medication_monitoring(monitoring_id),
    FOREIGN KEY (patientid) REFERENCES patient(patientid)
)";

try {
    $conn->exec($sql);
    echo "Medication monitoring alerts table created successfully.<br>";
} catch(PDOException $e) {
    die("Error creating medication_monitoring_alerts table: " . $e->getMessage());
}

// Create monitoring history table
$sql = "CREATE TABLE IF NOT EXISTS medication_monitoring_history (
    history_id INT AUTO_INCREMENT PRIMARY KEY,
    monitoring_id INT,
    monitoring_date DATE,
    monitoring_type ENUM('Lab', 'Vital_Signs', 'Symptom_Check', 'Clinical_Assessment', 'Patient_Report') NOT NULL,
    monitoring_results TEXT, -- JSON format for results
    performed_by VARCHAR(100),
    notes TEXT,
    follow_up_required BOOLEAN DEFAULT FALSE,
    follow_up_date DATE NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (monitoring_id) REFERENCES patient_medication_monitoring(monitoring_id)
)";

try {
    $conn->exec($sql);
    echo "Medication monitoring history table created successfully.<br>";
} catch(PDOException $e) {
    die("Error creating medication_monitoring_history table: " . $e->getMessage());
}

// Insert initial monitoring categories
$categories = [
    [
        'name' => 'Cardiac Drugs',
        'description' => 'Medications affecting heart function with narrow therapeutic windows requiring close monitoring for efficacy and toxicity',
        'risk_level' => 'High',
        'frequency' => 14,
        'lab_monitoring' => true,
        'vital_monitoring' => true,
        'symptom_monitoring' => true,
        'age_specific' => true,
        'beers_criteria' => false
    ],
    [
        'name' => 'Antibiotics - Aminoglycosides',
        'description' => 'Aminoglycoside antibiotics requiring monitoring for nephrotoxicity and ototoxicity',
        'risk_level' => 'High',
        'frequency' => 7,
        'lab_monitoring' => true,
        'vital_monitoring' => false,
        'symptom_monitoring' => true,
        'age_specific' => true,
        'beers_criteria' => false
    ],
    [
        'name' => 'Antiepileptics',
        'description' => 'Anti-seizure medications requiring therapeutic drug monitoring and side effect surveillance',
        'risk_level' => 'High',
        'frequency' => 30,
        'lab_monitoring' => true,
        'vital_monitoring' => false,
        'symptom_monitoring' => true,
        'age_specific' => true,
        'beers_criteria' => false
    ],
    [
        'name' => 'Bronchodilators',
        'description' => 'Respiratory medications like theophylline with narrow therapeutic ranges',
        'risk_level' => 'High',
        'frequency' => 14,
        'lab_monitoring' => true,
        'vital_monitoring' => true,
        'symptom_monitoring' => true,
        'age_specific' => true,
        'beers_criteria' => false
    ],
    [
        'name' => 'Immunosuppressants',
        'description' => 'Medications suppressing immune system requiring monitoring for efficacy and toxicity',
        'risk_level' => 'Critical',
        'frequency' => 7,
        'lab_monitoring' => true,
        'vital_monitoring' => true,
        'symptom_monitoring' => true,
        'age_specific' => true,
        'beers_criteria' => false
    ],
    [
        'name' => 'Beers Criteria Medications',
        'description' => 'Potentially inappropriate medications for older adults requiring special monitoring',
        'risk_level' => 'High',
        'frequency' => 30,
        'lab_monitoring' => false,
        'vital_monitoring' => true,
        'symptom_monitoring' => true,
        'age_specific' => true,
        'beers_criteria' => true
    ]
];

$insert_category_sql = "INSERT IGNORE INTO medication_monitoring_categories
    (category_name, category_description, risk_level, monitoring_frequency_days,
     requires_lab_monitoring, requires_vital_monitoring, requires_symptom_monitoring,
     age_specific_monitoring, beers_criteria)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

$stmt = $conn->prepare($insert_category_sql);

foreach ($categories as $category) {
    try {
        $stmt->execute([
            $category['name'],
            $category['description'],
            $category['risk_level'],
            $category['frequency'],
            $category['lab_monitoring'],
            $category['vital_monitoring'],
            $category['symptom_monitoring'],
            $category['age_specific'],
            $category['beers_criteria']
        ]);
        echo "Inserted category: " . $category['name'] . "<br>";
    } catch(PDOException $e) {
        echo "Error inserting category " . $category['name'] . ": " . $e->getMessage() . "<br>";
    }
}

// Get category IDs for rule insertion
$category_ids = [];
$category_query = "SELECT category_id, category_name FROM medication_monitoring_categories";
$result = $conn->query($category_query);
while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
    $category_ids[$row['category_name']] = $row['category_id'];
}

// Insert specific medication monitoring rules
$medication_rules = [
    // Cardiac Drugs
    [
        'category' => 'Cardiac Drugs',
        'medication_name' => 'Digoxin',
        'generic_name' => 'Digoxin',
        'monitoring_parameters' => json_encode([
            'serum_digoxin_level' => 'Required',
            'serum_potassium' => 'Required',
            'serum_creatinine' => 'Required',
            'heart_rate' => 'Required',
            'ecg_monitoring' => 'Recommended'
        ]),
        'frequency_days' => 7,
        'lab_tests_required' => json_encode(['Digoxin Level', 'Electrolytes', 'Creatinine']),
        'vital_signs_required' => json_encode(['Heart Rate', 'Blood Pressure', 'ECG']),
        'symptoms_to_monitor' => json_encode(['Nausea', 'Vomiting', 'Visual disturbances', 'Confusion', 'Arrhythmias']),
        'contraindications' => 'Severe heart block, ventricular fibrillation',
        'age_restrictions' => json_encode(['elderly_dose_reduction' => true, 'pediatric_monitoring' => 'intensive']),
        'therapeutic_range_min' => 0.8,
        'therapeutic_range_max' => 2.0,
        'toxic_level' => 2.5
    ],
    [
        'category' => 'Cardiac Drugs',
        'medication_name' => 'Amiodarone',
        'generic_name' => 'Amiodarone',
        'monitoring_parameters' => json_encode([
            'liver_function' => 'Required',
            'thyroid_function' => 'Required',
            'pulmonary_function' => 'Required',
            'ecg_monitoring' => 'Required'
        ]),
        'frequency_days' => 30,
        'lab_tests_required' => json_encode(['Liver Function Tests', 'Thyroid Function', 'Chest X-ray']),
        'vital_signs_required' => json_encode(['Heart Rate', 'Blood Pressure', 'ECG', 'Oxygen Saturation']),
        'symptoms_to_monitor' => json_encode(['Shortness of breath', 'Cough', 'Fatigue', 'Weight changes']),
        'contraindications' => 'Severe sinus node dysfunction, severe liver disease',
        'age_restrictions' => json_encode(['elderly_monitoring' => 'intensive']),
        'therapeutic_range_min' => null,
        'therapeutic_range_max' => null,
        'toxic_level' => null
    ],
    // Aminoglycosides
    [
        'category' => 'Antibiotics - Aminoglycosides',
        'medication_name' => 'Gentamicin',
        'generic_name' => 'Gentamicin',
        'monitoring_parameters' => json_encode([
            'serum_gentamicin_level' => 'Required',
            'serum_creatinine' => 'Required',
            'hearing_assessment' => 'Required'
        ]),
        'frequency_days' => 3,
        'lab_tests_required' => json_encode(['Gentamicin Peak/Trough', 'Creatinine', 'BUN']),
        'vital_signs_required' => json_encode(['Blood Pressure', 'Urine Output']),
        'symptoms_to_monitor' => json_encode(['Hearing loss', 'Tinnitus', 'Dizziness', 'Decreased urine output']),
        'contraindications' => 'Known hypersensitivity, severe kidney disease',
        'age_restrictions' => json_encode(['elderly_dose_adjustment' => true, 'neonatal_monitoring' => 'intensive']),
        'therapeutic_range_min' => 5.0,
        'therapeutic_range_max' => 10.0,
        'toxic_level' => 12.0
    ],
    [
        'category' => 'Antibiotics - Aminoglycosides',
        'medication_name' => 'Vancomycin',
        'generic_name' => 'Vancomycin',
        'monitoring_parameters' => json_encode([
            'serum_vancomycin_level' => 'Required',
            'serum_creatinine' => 'Required',
            'hearing_assessment' => 'Required'
        ]),
        'frequency_days' => 3,
        'lab_tests_required' => json_encode(['Vancomycin Trough', 'Creatinine', 'BUN']),
        'vital_signs_required' => json_encode(['Blood Pressure', 'Temperature']),
        'symptoms_to_monitor' => json_encode(['Red man syndrome', 'Hearing loss', 'Kidney problems']),
        'contraindications' => 'Known hypersensitivity',
        'age_restrictions' => json_encode(['elderly_dose_adjustment' => true]),
        'therapeutic_range_min' => 10.0,
        'therapeutic_range_max' => 20.0,
        'toxic_level' => 25.0
    ],
    // Antiepileptics
    [
        'category' => 'Antiepileptics',
        'medication_name' => 'Phenytoin',
        'generic_name' => 'Phenytoin',
        'monitoring_parameters' => json_encode([
            'serum_phenytoin_level' => 'Required',
            'liver_function' => 'Required',
            'complete_blood_count' => 'Required'
        ]),
        'frequency_days' => 14,
        'lab_tests_required' => json_encode(['Phenytoin Level', 'Liver Function Tests', 'CBC']),
        'vital_signs_required' => json_encode(['Blood Pressure', 'Heart Rate']),
        'symptoms_to_monitor' => json_encode(['Ataxia', 'Nystagmus', 'Confusion', 'Rash', 'Gingival hyperplasia']),
        'contraindications' => 'Hypersensitivity, severe liver disease',
        'age_restrictions' => json_encode(['elderly_monitoring' => 'intensive', 'pregnancy_category' => 'D']),
        'therapeutic_range_min' => 10.0,
        'therapeutic_range_max' => 20.0,
        'toxic_level' => 25.0
    ],
    [
        'category' => 'Antiepileptics',
        'medication_name' => 'Valproic Acid',
        'generic_name' => 'Valproic Acid',
        'monitoring_parameters' => json_encode([
            'serum_valproate_level' => 'Required',
            'liver_function' => 'Required',
            'complete_blood_count' => 'Required',
            'ammonia_level' => 'If indicated'
        ]),
        'frequency_days' => 30,
        'lab_tests_required' => json_encode(['Valproate Level', 'Liver Function Tests', 'CBC', 'Ammonia']),
        'vital_signs_required' => json_encode(['Weight monitoring']),
        'symptoms_to_monitor' => json_encode(['Tremor', 'Hair loss', 'Weight gain', 'Nausea', 'Confusion']),
        'contraindications' => 'Liver disease, mitochondrial disorders',
        'age_restrictions' => json_encode(['pregnancy_category' => 'D', 'pediatric_liver_monitoring' => 'intensive']),
        'therapeutic_range_min' => 50.0,
        'therapeutic_range_max' => 100.0,
        'toxic_level' => 150.0
    ],
    // Bronchodilators
    [
        'category' => 'Bronchodilators',
        'medication_name' => 'Theophylline',
        'generic_name' => 'Theophylline',
        'monitoring_parameters' => json_encode([
            'serum_theophylline_level' => 'Required',
            'heart_rate' => 'Required',
            'seizure_monitoring' => 'Required'
        ]),
        'frequency_days' => 7,
        'lab_tests_required' => json_encode(['Theophylline Level']),
        'vital_signs_required' => json_encode(['Heart Rate', 'Blood Pressure', 'Respiratory Rate']),
        'symptoms_to_monitor' => json_encode(['Nausea', 'Vomiting', 'Headache', 'Insomnia', 'Seizures', 'Arrhythmias']),
        'contraindications' => 'Hypersensitivity, active peptic ulcer',
        'age_restrictions' => json_encode(['elderly_dose_reduction' => true, 'pediatric_monitoring' => 'frequent']),
        'therapeutic_range_min' => 10.0,
        'therapeutic_range_max' => 20.0,
        'toxic_level' => 25.0
    ],
    // Immunosuppressants
    [
        'category' => 'Immunosuppressants',
        'medication_name' => 'Cyclosporine',
        'generic_name' => 'Cyclosporine',
        'monitoring_parameters' => json_encode([
            'serum_cyclosporine_level' => 'Required',
            'serum_creatinine' => 'Required',
            'blood_pressure' => 'Required',
            'liver_function' => 'Required'
        ]),
        'frequency_days' => 7,
        'lab_tests_required' => json_encode(['Cyclosporine Level', 'Creatinine', 'Liver Function Tests', 'CBC']),
        'vital_signs_required' => json_encode(['Blood Pressure', 'Weight']),
        'symptoms_to_monitor' => json_encode(['Hypertension', 'Kidney dysfunction', 'Gum hyperplasia', 'Tremor']),
        'contraindications' => 'Hypersensitivity, uncontrolled hypertension',
        'age_restrictions' => json_encode(['elderly_monitoring' => 'intensive']),
        'therapeutic_range_min' => 100.0,
        'therapeutic_range_max' => 400.0,
        'toxic_level' => 500.0
    ]
];

// Insert medication rules
$insert_rule_sql = "INSERT IGNORE INTO medication_monitoring_rules
    (category_id, medication_name, generic_name, monitoring_parameters, frequency_days,
     lab_tests_required, vital_signs_required, symptoms_to_monitor, contraindications,
     age_restrictions, therapeutic_range_min, therapeutic_range_max, toxic_level)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

$rule_stmt = $conn->prepare($insert_rule_sql);

foreach ($medication_rules as $rule) {
    try {
        $category_id = $category_ids[$rule['category']] ?? null;
        if ($category_id) {
            $rule_stmt->execute([
                $category_id,
                $rule['medication_name'],
                $rule['generic_name'],
                $rule['monitoring_parameters'],
                $rule['frequency_days'],
                $rule['lab_tests_required'],
                $rule['vital_signs_required'],
                $rule['symptoms_to_monitor'],
                $rule['contraindications'],
                $rule['age_restrictions'],
                $rule['therapeutic_range_min'],
                $rule['therapeutic_range_max'],
                $rule['toxic_level']
            ]);
            echo "Inserted rule for: " . $rule['medication_name'] . "<br>";
        } else {
            echo "Category not found for: " . $rule['medication_name'] . "<br>";
        }
    } catch(PDOException $e) {
        echo "Error inserting rule for " . $rule['medication_name'] . ": " . $e->getMessage() . "<br>";
    }
}

echo "<br><strong>Medication monitoring system setup completed successfully!</strong><br>";
echo "<br><a href='medication_monitoring_dashboard.php' class='btn btn-primary'>Go to Monitoring Dashboard</a>";
?>
