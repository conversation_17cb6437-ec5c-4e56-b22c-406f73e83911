<?php
require_once '../database.php';
require('../fpdf/fpdf.php');

try {
    // Fetch all employees and their contracts with search functionality
    $search = isset($_GET['search']) ? $_GET['search'] : '';
    
    $sql = "SELECT 
        p.*, 
        MIN(c.appointment_from) as first_appointment,
        MAX(c.appointment_to) as last_appointment
        FROM jo_profile p
        LEFT JOIN jo_contract c ON p.employee_id = c.employee_id";
    
    if ($search) {
        $sql .= " WHERE p.employee_id LIKE ? OR p.jo_name LIKE ?";
        $searchParam = "%$search%";
        $stmt = $conn->prepare($sql . " GROUP BY p.profile_id");
        $stmt->execute([$searchParam, $searchParam]);
    } else {
        $stmt = $conn->prepare($sql . " GROUP BY p.profile_id");
        $stmt->execute();
    }
    
    $employees = $stmt->fetchAll();

    // Create PDF document
    $pdf = new FPDF('P', 'mm', 'A4');
    $pdf->AddPage();

    // Add logos
    if (file_exists('../images/pgns.png') && file_exists('../images/bdh.png')) {
        $pdf->Image('../images/pgns.png', 40, 10, 25);
        $pdf->Image('../images/bdh.png', 145, 10, 25);
    }

    // Header section
    $pdf->SetFont('Arial', '', 12);
    $pdf->Cell(0, 6, 'Republic of the Philippines', 0, 1, 'C');
    $pdf->Cell(0, 6, 'Province of Northern Samar', 0, 1, 'C');
    $pdf->Cell(0, 6, 'Provincial Health Office', 0, 1, 'C');
    $pdf->SetFont('Arial', 'B', 16);
    $pdf->Cell(0, 8, 'BIRI DISTRICT HOSPITAL', 0, 1, 'C');
    $pdf->SetFont('Arial', 'I', 11);
    $pdf->Cell(0, 6, 'Biri Northern Samar', 0, 1, 'C');

    // Report Title
    $pdf->Ln(15);
    $pdf->SetFont('Arial', 'B', 16);
    $pdf->Cell(0, 10, 'EMPLOYEE SERVICE DURATION REPORT', 0, 1, 'C');
    
    // Table Header
    $pdf->Ln(10);
    $pdf->SetFont('Arial', 'B', 11);
    $pdf->Cell(60, 8, 'Employee Name', 1, 0, 'C');
    $pdf->Cell(45, 8, 'First Contract', 1, 0, 'C');
    $pdf->Cell(45, 8, 'Latest Contract', 1, 0, 'C');
    $pdf->Cell(40, 8, 'Total Service', 1, 1, 'C');

    // Table Content
    $pdf->SetFont('Arial', '', 10);
    foreach ($employees as $employee) {
        if (empty($employee['first_appointment']) || empty($employee['last_appointment'])) {
            $serviceDuration = "NEW";
        } else {
            $start = new DateTime($employee['first_appointment']);
            $end = new DateTime($employee['last_appointment']);
            $currentDate = new DateTime();
            
            if ($end > $currentDate) {
                $end = $currentDate;
            }
            
            $interval = $start->diff($end);
            
            // Get start and end months
            $startMonth = (int)$start->format('n');
            $endMonth = (int)$end->format('n');
            
            // Calculate years normally
            $years = $interval->y;
            
            // Calculate months with January-June and July-December counting as 6 months
            $months = $interval->m;
            if (($startMonth >= 7 && $endMonth >= 7) || ($startMonth <= 6 && $endMonth <= 6)) {
                $months = 6;
            }
            
            if ($years > 0 && $months > 0) {
                $serviceDuration = "{$years}y {$months}m";
            } elseif ($years > 0) {
                $serviceDuration = "{$years}y";
            } elseif ($months > 0) {
                $serviceDuration = "{$months}m";
            } else {
                $serviceDuration = "NEW";
            }
        }

        $pdf->Cell(60, 8, $employee['jo_name'], 1, 0, 'L');
        $pdf->Cell(45, 8, $employee['first_appointment'] ? date('M d, Y', strtotime($employee['first_appointment'])) : 'N/A', 1, 0, 'C');
        $pdf->Cell(45, 8, $employee['last_appointment'] ? date('M d, Y', strtotime($employee['last_appointment'])) : 'N/A', 1, 0, 'C');
        $pdf->Cell(40, 8, $serviceDuration, 1, 1, 'C');
    }

    // Signatory
    $pdf->Ln(20);
    $pdf->SetFont('Arial', 'B', 12);
    $pdf->Cell(100, 6, '', 0, 0);
    $pdf->Cell(90, 6, 'CHRISTEL D. BRAVO', 0, 1, 'C');
    $pdf->SetFont('Arial', '', 12);
    $pdf->Cell(100, 6, '', 0, 0);
    $pdf->Cell(90, 6, 'Administrative Officer IV', 0, 1, 'C');

    // Output PDF
    $pdf->Output('Employee_Service_Duration_Report.pdf', 'D');

} catch (PDOException $e) {
    die('Database Error: ' . $e->getMessage());
} catch (Exception $e) {
    die('Error: ' . $e->getMessage());
}
