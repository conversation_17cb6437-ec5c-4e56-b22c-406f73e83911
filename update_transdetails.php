<?php
require_once 'database.php';

// Initialize messages and session
session_start();
$successMessage = $errorMessage = "";

// Get transaction ID from URL parameter
$transaction_id = isset($_GET['id']) ? $_GET['id'] : null;
$_SESSION['current_transaction_id'] = $transaction_id;

if ($transaction_id) {
    try {
        // Fetch transaction information
        $stmt = $conn->prepare("
            SELECT t.*, e.emp_name, td.*, a.account_description 
            FROM transactions t
            JOIN employees e ON t.employee_id = e.id
            JOIN transaction_details td ON t.transaction_id = td.transaction_id
            JOIN accounts a ON td.account_code = a.account_code
            WHERE t.transaction_id = ?
        ");
        $stmt->execute([$transaction_id]);
        $transaction_items = $stmt->fetchAll();
        
        // Get transaction header information from first row
        if (!empty($transaction_items)) {
            $transaction_info = [
                'transaction_ref' => $transaction_items[0]['transaction_ref'],
                'transaction_date' => $transaction_items[0]['transaction_date'],
                'emp_name' => $transaction_items[0]['emp_name']
            ];
        } else {
            $errorMessage = "No transaction details found for this ID.";
        }
    } catch(PDOException $e) {
        $errorMessage = "Error: " . $e->getMessage();
    }
}

// Handle updating individual transaction details
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_detail'])) {
    try {
        // Validate inputs
        if (empty($_POST['account_code']) || empty($_POST['transaction_description']) || !is_numeric($_POST['amount'])) {
            throw new Exception("All fields are required and amount must be numeric");
        }
        
        // Update transaction_details table
        $stmt = $conn->prepare("
            UPDATE transaction_details 
            SET account_code = ?, 
                transaction_description = ?, 
                amount = ? 
            WHERE detail_id = ? AND transaction_id = ?
        ");
        $stmt->execute([
            $_POST['account_code'],
            $_POST['transaction_description'],
            $_POST['amount'],
            $_POST['detail_id'],
            $_POST['transaction_id']
        ]);
        
        $successMessage = "Transaction detail updated successfully!";
        
        // Refresh transaction items with full information
        $stmt = $conn->prepare("
            SELECT t.*, e.emp_name, td.*, a.account_description 
            FROM transactions t
            JOIN employees e ON t.employee_id = e.id
            JOIN transaction_details td ON t.transaction_id = td.transaction_id
            JOIN accounts a ON td.account_code = a.account_code
            WHERE t.transaction_id = ?
        ");
        $stmt->execute([$_POST['transaction_id']]);
        $transaction_items = $stmt->fetchAll();
        
    } catch(Exception $e) {
        $errorMessage = "Error: " . $e->getMessage();
    }
}

// Fetch accounts for dropdown
$accounts = $conn->query("SELECT account_code, account_description FROM accounts")->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Transaction Details</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-4">
        <?php if (!empty($transaction_info)): ?>
            <!-- Transaction Info Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Transaction Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <p class="mb-1"><strong>Reference:</strong> <?php echo htmlspecialchars($transaction_info['transaction_ref']); ?></p>
                        </div>
                        <div class="col-md-4">
                            <p class="mb-1"><strong>Date:</strong> <?php echo htmlspecialchars($transaction_info['transaction_date']); ?></p>
                        </div>
                        <div class="col-md-4">
                            <p class="mb-1"><strong>Employee:</strong> <?php echo htmlspecialchars($transaction_info['emp_name']); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Messages -->
        <?php if ($successMessage): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $successMessage; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($errorMessage): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $errorMessage; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Transaction Details Table -->
        <?php if (!empty($transaction_items)): ?>
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Transaction Details</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Account</th>
                                    <th>Description</th>
                                    <th class="text-end">Amount</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $total = 0;
                                foreach ($transaction_items as $item): 
                                    $total += $item['amount'];
                                ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($item['account_description']); ?></td>
                                        <td><?php echo htmlspecialchars($item['transaction_description']); ?></td>
                                        <td class="text-end">₱<?php echo number_format($item['amount'], 2); ?></td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" onclick="editDetail(<?php echo htmlspecialchars(json_encode($item)); ?>)">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                                <tr class="table-light">
                                    <td colspan="2" class="text-end"><strong>Total:</strong></td>
                                    <td class="text-end"><strong>₱<?php echo number_format($total, 2); ?></strong></td>
                                    <td></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>No transaction details found.
            </div>
        <?php endif; ?>
        <!-- Back button -->
        <div class="mt-3">
            <a href="transactions.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Transactions
            </a>
        </div>

        <!-- Edit Modal -->
        <div class="modal fade" id="editModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit Transaction Detail</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form method="POST">
                        <div class="modal-body">
                            <input type="hidden" name="detail_id" id="edit_detail_id">
                            <input type="hidden" name="transaction_id" id="edit_transaction_id">
                            
                            <div class="mb-3">
                                <label for="account_code" class="form-label">Account</label>
                                <select class="form-select" name="account_code" id="edit_account_code" required>
                                    <?php foreach ($accounts as $account): ?>
                                        <option value="<?php echo $account['account_code']; ?>">
                                            <?php echo $account['account_code'] . ' - ' . $account['account_description']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="transaction_description" class="form-label">Description</label>
                                <textarea class="form-control" name="transaction_description" id="edit_transaction_description" required></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="amount" class="form-label">Amount</label>
                                <div class="input-group">
                                    <span class="input-group-text">₱</span>
                                    <input type="number" step="0.01" class="form-control" name="amount" id="edit_amount" required>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" name="update_detail" class="btn btn-primary">Update</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            function editDetail(detail) {
                document.getElementById('edit_detail_id').value = detail.detail_id;
                document.getElementById('edit_transaction_id').value = detail.transaction_id;
                document.getElementById('edit_account_code').value = detail.account_code;
                document.getElementById('edit_transaction_description').value = detail.transaction_description;
                document.getElementById('edit_amount').value = detail.amount;
                
                new bootstrap.Modal(document.getElementById('editModal')).show();
            }
        </script>
    </div>
</body>
</html>
