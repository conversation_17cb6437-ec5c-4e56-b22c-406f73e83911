<?php
session_start();

require_once '../../database.php';

// Create chart_history table if not exists
$sql = "CREATE TABLE IF NOT EXISTS `chart_history` (
    `chart_history_id` INT(10) NOT NULL AUTO_INCREMENT,
    `user_id` INT(10) NOT NULL,
    `location` VARCHAR(100) NOT NULL COLLATE 'utf8mb4_0900_ai_ci',
    `date` DATE NOT NULL,
    `forwarded_to` VARCHAR(100) NULL DEFAULT NULL COLLATE 'utf8mb4_0900_ai_ci',
    `date_forwarded` DATE NULL DEFAULT NULL,
    `remarks` TEXT NULL DEFAULT NULL COLLATE 'utf8mb4_0900_ai_ci',
    `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`chart_history_id`) USING BTREE
) COLLATE='utf8mb4_0900_ai_ci' ENGINE=InnoDB";

$conn->exec($sql);

// INSERT
if (isset($_POST['submit'])) {
    $sql = "INSERT INTO chart_history (user_id, location, date, forwarded_to, date_forwarded, remarks) 
            VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $_POST['user_id'],
        $_POST['location'],
        $_POST['date'],
        !empty($_POST['forwarded_to']) ? $_POST['forwarded_to'] : null,
        !empty($_POST['date_forwarded']) ? $_POST['date_forwarded'] : null,
        $_POST['remarks']
    ]);
}

// UPDATE
if (isset($_POST['update'])) {
    $sql = "UPDATE chart_history SET 
            user_id=?, location=?, date=?, forwarded_to=?, date_forwarded=?, remarks=?
            WHERE chart_history_id=?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $_POST['user_id'],
        $_POST['location'],
        $_POST['date'],
        !empty($_POST['forwarded_to']) ? $_POST['forwarded_to'] : null,
        !empty($_POST['date_forwarded']) ? $_POST['date_forwarded'] : null,
        $_POST['remarks'],
        $_POST['chart_history_id']
    ]);
}

// SEARCH
$search = isset($_GET['search']) ? $_GET['search'] : '';
$sql = "SELECT *, 
        COALESCE(location, '') as location,
        COALESCE(remarks, '') as remarks
        FROM chart_history WHERE 
        location LIKE ? OR remarks LIKE ? 
        ORDER BY created_at DESC";
$stmt = $conn->prepare($sql);
$searchTerm = "%$search%";
$stmt->execute([$searchTerm, $searchTerm]);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart History</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 20px auto; }
        .table-responsive { margin-top: 20px; }
        .modal-lg { max-width: 800px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card shadow-lg rounded-4 border-0">
            <div class="card-header bg-gradient bg-info bg-opacity-25 border-0 py-4 d-flex justify-content-between align-items-center">
                <h3 class="mb-0 text-primary">
                    <i class="fas fa-history me-2"></i>Chart History
                </h3>
                <div class="d-flex align-items-center">
                    <button type="button" class="btn btn-primary btn-lg rounded-3 shadow-sm" data-bs-toggle="modal" data-bs-target="#historyModal">
                        <i class="fas fa-plus me-2"></i>Add History
                    </button>
                    <div class="ms-2">
                        <a href="../../index.php" class="btn btn-outline-primary btn-lg rounded-3 shadow-sm">
                            <i class="fas fa-home me-2"></i>Homepage
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                <form method="GET" class="mb-4">
                    <div class="input-group input-group-lg shadow-sm rounded-3 overflow-hidden">
                        <span class="input-group-text border-0 bg-light">
                            <i class="fas fa-search text-primary"></i>
                        </span>
                        <input type="text" class="form-control border-0 bg-light py-3" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Search by location or remarks...">
                        <button class="btn btn-primary px-4" type="submit">
                            <i class="fas fa-search-plus me-2"></i>Search
                        </button>
                    </div>
                </form>

                <div class="table-responsive rounded-3 border">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="bg-light">
                            <tr>
                                <th><i class="fas fa-user me-2"></i>User ID</th>
                                <th><i class="fas fa-map-marker-alt me-2"></i>Location</th>
                                <th><i class="fas fa-calendar me-2"></i>Date</th>
                                <th><i class="fas fa-forward me-2"></i>Forwarded To</th>
                                <th><i class="fas fa-keyboard me-2"></i>Date Encoded</th>
                                <th><i class="fas fa-file-invoice me-2"></i>Date Claims Submitted</th>
                                <th><i class="fas fa-comment me-2"></i>Remarks</th>
                                <th><i class="fas fa-cog me-2"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($results as $row): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($row['user_id']); ?></td>
                                <td><?php echo htmlspecialchars($row['location']); ?></td>
                                <td><?php echo htmlspecialchars($row['date']); ?></td>
                                <td><?php echo htmlspecialchars($row['date_forwarded'] ?? ''); ?></td>
                                <td><?php echo htmlspecialchars($row['date_encoded'] ?? ''); ?></td>
                                <td><?php echo htmlspecialchars($row['date_claims_submitted'] ?? ''); ?></td>
                                <td><?php echo htmlspecialchars($row['remarks'] ?? ''); ?></td>
                                <td>
                                    <button class="btn btn-info btn-sm rounded-3" onclick="editHistory(<?php echo htmlspecialchars(json_encode($row)); ?>)">
                                        <i class="fas fa-pen-to-square me-1"></i> Edit
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Add/Edit -->
    <div class="modal fade" id="historyModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content border-0 shadow-lg">
                <div class="modal-header bg-gradient-primary border-0 rounded-top">
                    <h5 class="modal-title fs-4 fw-bold text-dark" id="modalTitle">
                        <i class="fas fa-plus me-2 text-dark"></i>Add History
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="historyForm" method="POST" class="needs-validation" novalidate>
                    <input type="hidden" name="chart_history_id" id="chart_history_id">
                    <div class="modal-body p-4">
                        <div class="row g-4">
                            <input type="hidden" name="user_id" id="user_id" value="<?php echo htmlspecialchars($_SESSION['user_id']); ?>">
                            <div class="col-12 col-md-6">
                                <label class="form-label fw-semibold">Date</label>
                                <input type="date" class="form-control form-control-lg shadow-sm" name="date" id="date" 
                                    value="<?php echo date('Y-m-d'); ?>" readonly required>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label fw-semibold">Location</label>
                                <select class="form-select form-select-lg shadow-sm" name="location" id="location" required>
                                    <option value="">Select Location</option>
                                    <?php
                                    $dept_sql = "SELECT department_name FROM departments";
                                    $dept_stmt = $conn->query($dept_sql);
                                    while ($dept = $dept_stmt->fetch(PDO::FETCH_ASSOC)) {
                                        echo '<option value="' . htmlspecialchars($dept['department_name']) . '">' . 
                                             htmlspecialchars($dept['department_name']) . '</option>';
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label fw-semibold">Remarks</label>
                                <textarea class="form-control form-control-lg shadow-sm" name="remarks" id="remarks" rows="4" 
                                    style="resize: none;"></textarea>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="forwardedToSwitch">
                                    <label class="form-check-label fw-semibold" for="forwardedToSwitch">Forwarded To</label>
                                </div>
                                <select class="form-select form-select-lg shadow-sm" name="forwarded_to" id="forwarded_to" style="display: none;">
                                    <option value="">Select Location</option>
                                    <?php
                                    $dept_sql = "SELECT department_name FROM departments";
                                    $dept_stmt = $conn->query($dept_sql);
                                    while ($dept = $dept_stmt->fetch(PDO::FETCH_ASSOC)) {
                                        echo '<option value="' . htmlspecialchars($dept['department_name']) . '">' . 
                                             htmlspecialchars($dept['department_name']) . '</option>';
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="dateEncodedSwitch">
                                    <label class="form-check-label fw-semibold" for="dateEncodedSwitch">Date Encoded</label>
                                </div>
                                <input type="date" class="form-control form-control-lg shadow-sm" name="date_encoded" id="date_encoded" style="display: none;">
                            </div>
                            <div class="col-12 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="dateClaimsSwitch">
                                    <label class="form-check-label fw-semibold" for="dateClaimsSwitch">Date Claims Submitted</label>
                                </div>
                                <input type="date" class="form-control form-control-lg shadow-sm" name="date_claims_submitted" id="date_claims_submitted" style="display: none;">
                            </div>
                        </div>
                        <script>
                            document.getElementById('forwardedToSwitch').addEventListener('change', function() {
                                document.getElementById('forwarded_to').style.display = this.checked ? 'block' : 'none';
                            });
                            document.getElementById('dateEncodedSwitch').addEventListener('change', function() {
                                document.getElementById('date_encoded').style.display = this.checked ? 'block' : 'none';
                            });
                            document.getElementById('dateClaimsSwitch').addEventListener('change', function() {
                                document.getElementById('date_claims_submitted').style.display = this.checked ? 'block' : 'none';
                            });
                        </script>
                    </div>
                    <div class="modal-footer border-0 p-4">
                        <button type="button" class="btn btn-light btn-lg px-4 me-2" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Close
                        </button>
                        <button type="submit" class="btn btn-primary btn-lg px-4" id="submitBtn" name="submit">
                            <i class="fas fa-save me-2"></i>Save History
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editHistory(data) {
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-edit me-2"></i>Edit History';
            document.getElementById('chart_history_id').value = data.chart_history_id;
            
            // Fill form fields
            document.getElementById('user_id').value = data.user_id;
            document.getElementById('location').value = data.location;
            document.getElementById('date').value = data.date;
            document.getElementById('forwarded_to').value = data.forwarded_to;
            document.getElementById('date_encoded').value = data.date_encoded;
            document.getElementById('date_claims_submitted').value = data.date_claims_submitted;
            document.getElementById('remarks').value = data.remarks;
            
            // Change submit button
            document.getElementById('submitBtn').name = 'update';
            document.getElementById('submitBtn').innerHTML = '<i class="fas fa-save me-2"></i>Update History';
            
            // Show modal
            new bootstrap.Modal(document.getElementById('historyModal')).show();
        }

        // Reset form when modal is closed
        document.getElementById('historyModal').addEventListener('hidden.bs.modal', function () {
            document.getElementById('historyForm').reset();
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-plus me-2"></i>Add History';
            document.getElementById('submitBtn').name = 'submit';
            document.getElementById('submitBtn').innerHTML = '<i class="fas fa-save me-2"></i>Save History';
        });
    </script>
</body>
</html>
