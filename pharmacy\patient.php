<?php
require_once '../database.php';

// Create table if not exists
$sql = "CREATE TABLE IF NOT EXISTS patient (
    patientid INT AUTO_INCREMENT PRIMARY KEY,
    patientname VARCHAR(255) NOT NULL,
    birthdate DATE,
    patient_type ENUM('Regular', 'Senior', 'PWD') NOT NULL DEFAULT 'Regular',
    id_number VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

try {
    $conn->exec($sql);
} catch(PDOException $e) {
    die("Error creating table: " . $e->getMessage());
}
// INSERT
if (isset($_POST['submit'])) {
    try {
        $sql = "INSERT INTO patient (patientname, birthdate, patient_type, id_number) VALUES (?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $birthdate = !empty($_POST['birthdate']) ? $_POST['birthdate'] : null;
        $stmt->execute([
            $_POST['patientname'],
            $birthdate,
            $_POST['patient_type'],
            $_POST['id_number']
        ]);
    } catch(PDOException $e) {
        die("Error inserting data: " . $e->getMessage());
    }
}

// UPDATE
if (isset($_POST['update'])) {
    try {
        $sql = "UPDATE patient SET 
                patientname = ?, 
                birthdate = ?,
                patient_type = ?,
                id_number = ? 
                WHERE patientid = ?";
        $stmt = $conn->prepare($sql);
        $birthdate = !empty($_POST['birthdate']) ? $_POST['birthdate'] : null;
        $stmt->execute([
            $_POST['patientname'],
            $birthdate,
            $_POST['patient_type'],
            $_POST['id_number'],
            $_POST['patientid']
        ]);
    } catch(PDOException $e) {
        die("Error updating data: " . $e->getMessage());
    }
}

// SEARCH
$search = isset($_GET['search']) ? $_GET['search'] : '';
$sql = "SELECT * FROM patient WHERE 
        patientname LIKE ? OR 
        patient_type LIKE ? OR
        id_number LIKE ?
        ORDER BY created_at DESC";
$stmt = $conn->prepare($sql);
$searchTerm = "%$search%";
$stmt->execute([$searchTerm, $searchTerm, $searchTerm]);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 20px auto; }
        .special-info { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card shadow-lg rounded-4 border-0">
            <div class="card-header bg-gradient bg-primary bg-opacity-25 border-0 py-4 d-flex justify-content-between align-items-center">
                <h3 class="mb-0 text-primary">
                    <i class="fas fa-hospital-user me-2"></i>Patient Management
                </h3>
                <div class="d-flex align-items-center">
                    <button type="button" class="btn btn-primary btn-lg rounded-3 shadow-sm" data-bs-toggle="modal" data-bs-target="#patientModal">
                        <i class="fas fa-plus me-2"></i>New Patient
                    </button>
                    <div class="ms-2">
                        <a href="../pharmacy/pharmacydashboard.php" class="btn btn-outline-primary btn-lg rounded-3 shadow-sm">
                            <i class="fas fa-home me-2"></i>Homepage
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                <!-- Search Form -->
                <form method="GET" class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text border-0 bg-light">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control border-0 bg-light" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Search patients...">
                        <button class="btn btn-primary" type="submit">Search</button>
                    </div>
                </form>
                <!-- Patients Table -->
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="bg-light">
                            <tr>
                                <th><i class="fas fa-hashtag me-2"></i>ID</th>
                                <th><i class="fas fa-user me-2"></i>Patient Name</th>
                                <th><i class="fas fa-id-card me-2"></i>Patient Type</th>
                                <th><i class="fas fa-birthday-cake me-2"></i>Age</th>
                                <th><i class="fas fa-info-circle me-2"></i>ID Number</th>
                                <th><i class="fas fa-cog me-2"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($results as $row): ?>
                            <?php 
                                $age = '';
                                if (!empty($row['birthdate'])) {
                                    $birthDate = new DateTime($row['birthdate']);
                                    $today = new DateTime();
                                    $age = $birthDate->diff($today)->y;
                                }
                            ?>
                            <tr>
                                <td class="fw-bold text-primary"><?php echo htmlspecialchars($row['patientid']); ?></td>
                                <td><?php echo htmlspecialchars($row['patientname']); ?></td>
                                <td>
                                    <span class="badge bg-<?php echo $row['patient_type'] == 'Regular' ? 'secondary' : 'primary'; ?>">
                                        <?php echo htmlspecialchars($row['patient_type']); ?>
                                    </span>
                                </td>
                                <td><?php echo $age ? $age . ' years' : '-'; ?></td>
                                <td><?php echo htmlspecialchars($row['id_number'] ?: '-'); ?></td>
                                <td>
                                    <button class="btn btn-warning btn-sm" onclick='editPatient(<?php echo json_encode($row); ?>)'>
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <a href="transactions.php?id=<?php echo $row['patientid']; ?>" class="btn btn-primary btn-sm" title="Go to Transaction Details">
                                        <i class="fas fa-receipt"></i>
                                    </a>
                                    <a href="patienthistory.php?id=<?php echo $row['patientid']; ?>" class="btn btn-info btn-sm">
                                        <i class="fas fa-history"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Patient Modal -->
    <div class="modal fade" id="patientModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary bg-opacity-25">
                    <h5 class="modal-title" id="modalTitle">Add New Patient</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="patientForm" method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="patientid" id="patientid">
                        <div class="mb-3">
                            <label class="form-label">Patient Name</label>
                            <input type="text" class="form-control" name="patientname" id="patientname" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Patient Type</label>
                            <select class="form-select" name="patient_type" id="patient_type" onchange="toggleSpecialInfo()">
                                <option value="Regular">Regular</option>
                                <option value="Senior">Senior</option>
                                <option value="PWD">PWD</option>
                            </select>
                        </div>
                        <div class="special-info">
                            <div class="mb-3">
                                <label class="form-label">Birthdate</label>
                                <input type="date" class="form-control" name="birthdate" id="birthdate">
                            </div>
                            <div class="mb-3">
                                <label class="form-label" id="id_label">ID Number</label>
                                <input type="text" class="form-control" name="id_number" id="id_number">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary" name="submit" id="submitBtn">Save Patient</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleSpecialInfo() {
            const patientType = document.getElementById('patient_type').value;
            const specialInfo = document.querySelector('.special-info');
            const idLabel = document.getElementById('id_label');
            
            if (patientType === 'Regular') {
                specialInfo.style.display = 'none';
            } else {
                specialInfo.style.display = 'block';
                idLabel.textContent = patientType === 'Senior' ? 'Senior Citizen ID Number' : 'PWD ID Number';
            }
        }

        function editPatient(patient) {
            document.getElementById('modalTitle').textContent = 'Edit Patient';
            document.getElementById('patientid').value = patient.patientid;
            document.getElementById('patientname').value = patient.patientname;
            document.getElementById('patient_type').value = patient.patient_type;
            document.getElementById('birthdate').value = patient.birthdate || '';
            document.getElementById('id_number').value = patient.id_number || '';
            
            document.getElementById('submitBtn').name = 'update';
            document.getElementById('submitBtn').textContent = 'Update Patient';
            
            toggleSpecialInfo();
            new bootstrap.Modal(document.getElementById('patientModal')).show();
        }

        // Reset form when modal is closed
        document.getElementById('patientModal').addEventListener('hidden.bs.modal', function () {
            document.getElementById('patientForm').reset();
            document.getElementById('modalTitle').textContent = 'Add New Patient';
            document.getElementById('submitBtn').name = 'submit';
            document.getElementById('submitBtn').textContent = 'Save Patient';
            document.getElementById('patientid').value = '';
            document.querySelector('.special-info').style.display = 'none';
        });
    </script>
</body>
</html>
