<?php
require_once '../database.php';
// Initialize session
session_start();

// Check if ID is provided in URL
if (!isset($_GET['id'])) {
    header('Location: patient.php');
    exit();
}

// Store patient ID in session
$_SESSION['patient_id'] = $_GET['id'];

// Create table if not exists and create trigger if not exists (if not exists)
$sql = "CREATE TABLE IF NOT EXISTS pharmatransactions (
    transaction_id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_reference VARCHAR(20),
    patientid INT,
    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ward VARCHAR(100),
    OR_Number VARCHAR(50),
    FOREIGN KEY (patientid) REFERENCES patient(patientid)
)";

try {
    $conn->exec($sql);
    
    // Create trigger if not exists
    $triggerSql = "
    CREATE TRIGGER IF NOT EXISTS generate_transaction_reference 
    BEFORE INSERT ON pharmatransactions 
    FOR EACH ROW 
    BEGIN
        DECLARE last_id INT;
        SELECT COALESCE(MAX(transaction_id), 0) INTO last_id FROM pharmatransactions;
        SET NEW.transaction_reference = CONCAT(
            YEAR(NEW.transaction_date),
            '-',
            LPAD(last_id + 1, 6, '0')
        );
    END;";
    
    $conn->exec($triggerSql);
} catch(PDOException $e) {
    // Log error instead of dying
    error_log("Error in table/trigger creation: " . $e->getMessage());
}

// INSERT Transaction
if (isset($_POST['submit'])) {
    try {
        $sql = "INSERT INTO pharmatransactions (patientid, ward, OR_Number) VALUES (?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $_POST['patientid'],
            $_POST['ward'],
            $_POST['or_number']
        ]);
        $_SESSION['success'] = "Transaction added successfully";
    } catch(PDOException $e) {
        $_SESSION['error'] = "Error adding transaction: " . $e->getMessage();
    }
}

// UPDATE Transaction
if (isset($_POST['update'])) {
    try {
        $sql = "UPDATE pharmatransactions SET patientid=?, ward=?, OR_Number=? WHERE transaction_id=?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $_POST['patientid'],
            $_POST['ward'],
            $_POST['or_number'],
            $_POST['transaction_id']
        ]);
        $_SESSION['success'] = "Transaction updated successfully";
    } catch(PDOException $e) {
        $_SESSION['error'] = "Error updating transaction: " . $e->getMessage();
    }
}

// Get transactions for specific patient
$sql = "SELECT t.*, p.patientname 
        FROM pharmatransactions t 
        LEFT JOIN patient p ON t.patientid = p.patientid 
        WHERE t.patientid = ?
        ORDER BY t.transaction_date DESC";
$stmt = $conn->prepare($sql);
$stmt->execute([$_SESSION['patient_id']]);
$transactions = $stmt->fetchAll();

// Get patient details
$sql = "SELECT patientid, patientname, birthdate, patient_type, id_number 
        FROM patient 
        WHERE patientid = ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$_SESSION['patient_id']]);
$patientDetails = $stmt->fetch();


?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Transactions</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-4">
        <!-- Patient Profile Card -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-primary bg-gradient text-white">
                <h4 class="mb-0"><i class="fas fa-user-circle me-2"></i>Patient Profile</h4>
            </div>
            <div class="card-body p-4">
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="info-group bg-light rounded-3 p-3">
                            <div class="mb-3">
                                <i class="fas fa-id-card text-primary me-2"></i>
                                <strong>Patient ID:</strong>
                                <span class="ms-2"><?php echo htmlspecialchars($patientDetails['patientid'] ?? 'N/A'); ?></span>
                            </div>
                            <div class="mb-3">
                                <i class="fas fa-user text-primary me-2"></i>
                                <strong>Patient Name:</strong>
                                <span class="ms-2"><?php echo htmlspecialchars($patientDetails['patientname'] ?? 'N/A'); ?></span>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newTransactionModal">
                                <i class="fas fa-plus-circle me-2"></i>New Transaction
                            </button>
                            <a href="patient.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Patients
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-group bg-light rounded-3 p-3">
                            <div class="mb-3">
                                <i class="fas fa-birthday-cake text-primary me-2"></i>
                                <strong>Birthdate:</strong>
                                <span class="ms-2"><?php echo date('F d, Y', strtotime($patientDetails['birthdate'] ?? 'now')); ?></span>
                            </div>
                            <div class="mb-3">
                                <i class="fas fa-tag text-primary me-2"></i>
                                <strong>Patient Type:</strong>
                                <span class="badge bg-primary ms-2"><?php echo htmlspecialchars($patientDetails['patient_type'] ?? 'N/A'); ?></span>
                            </div>
                            <div class="mb-3">
                                <i class="fas fa-fingerprint text-primary me-2"></i>
                                <strong>ID Number:</strong>
                                <span class="ms-2"><?php echo htmlspecialchars($patientDetails['id_number'] ?? 'N/A'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transactions List -->
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h4 class="mb-0"><i class="fas fa-history me-2"></i>Transaction History</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Transaction Ref</th>
                                <th>Date</th>
                                <th>Ward</th>
                                <th>OR Number</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($transactions as $transaction): ?>
                            <?php 
                                $isToday = date('Y-m-d') === date('Y-m-d', strtotime($transaction['transaction_date']));
                                $rowClass = $isToday ? 'table-warning' : '';
                            ?>
                            <tr class="<?php echo $rowClass; ?>">
                                <td><?php echo htmlspecialchars($transaction['transaction_reference']); ?></td>
                                <td>
                                    <?php echo date('M d, Y', strtotime($transaction['transaction_date'])); ?>
                                    <?php if($isToday): ?>
                                        <span class="badge bg-warning text-dark">Today</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($transaction['ward']); ?></td>
                                <td><?php echo htmlspecialchars($transaction['OR_Number']); ?></td>
                                <td>
                                    <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editModal<?php echo $transaction['transaction_id']; ?>">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#viewModal<?php echo $transaction['transaction_id']; ?>">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <a href="transaction_details.php?id=<?php echo $transaction['transaction_id']; ?>" class="btn btn-sm btn-success">
                                        <i class="fas fa-plus"></i> Add Details
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- New Transaction Modal -->
    <div class="modal fade" id="newTransactionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus-circle me-2"></i>New Transaction</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="patientid" value="<?php echo $_SESSION['patient_id']; ?>">
                        <div class="mb-3">
                            <label class="form-label">Transaction Date</label>
                            <input type="date" class="form-control" name="transaction_date" value="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Ward</label>
                            <select class="form-select" name="ward" required>
                                <option value="">Select Ward</option>
                                <option value="Out-Patient">Out-Patient</option>
                                <option value="Emergency">Emergency</option>
                                <option value="Pediatrics">Pediatrics</option>
                                <option value="Medical Male">Medical Male</option>
                                <option value="Medical Female">Medical Female</option>
                                <option value="OBGyne">OBGyne</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">OR Number</label>
                            <input type="text" class="form-control" name="or_number" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Cancel
                        </button>
                        <button type="submit" name="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Transaction
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Transaction Modals -->
    <?php foreach($transactions as $transaction): ?>
    <div class="modal fade" id="editModal<?php echo $transaction['transaction_id']; ?>" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Transaction</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="transaction_id" value="<?php echo $transaction['transaction_id']; ?>">
                        <input type="hidden" name="patientid" value="<?php echo $transaction['patientid']; ?>">
                        <div class="mb-3">
                            <label class="form-label">Transaction Date</label>
                            <input type="date" class="form-control" name="transaction_date" value="<?php echo date('Y-m-d', strtotime($transaction['transaction_date'])); ?>" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Ward</label>
                            <select class="form-select" name="ward" required>
                                <option value="">Select Ward</option>
                                <option value="Out-Patient" <?php echo ($transaction['ward'] == 'Out-Patient') ? 'selected' : ''; ?>>Out-Patient</option>
                                <option value="Emergency" <?php echo ($transaction['ward'] == 'Emergency') ? 'selected' : ''; ?>>Emergency</option>
                                <option value="Pediatrics" <?php echo ($transaction['ward'] == 'Pediatrics') ? 'selected' : ''; ?>>Pediatrics</option>
                                <option value="Medical Male" <?php echo ($transaction['ward'] == 'Medical Male') ? 'selected' : ''; ?>>Medical Male</option>
                                <option value="Medical Female" <?php echo ($transaction['ward'] == 'Medical Female') ? 'selected' : ''; ?>>Medical Female</option>
                                <option value="OBGyne" <?php echo ($transaction['ward'] == 'OBGyne') ? 'selected' : ''; ?>>OBGyne</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">OR Number</label>
                            <input type="text" class="form-control" name="or_number" value="<?php echo htmlspecialchars($transaction['OR_Number']); ?>" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Cancel
                        </button>
                        <button type="submit" name="update" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Transaction
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endforeach; ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>


