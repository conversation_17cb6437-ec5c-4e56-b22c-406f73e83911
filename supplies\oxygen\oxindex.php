<?php
require_once '../../database.php';

// Create tables if they don't exist
$conn->exec("CREATE TABLE IF NOT EXISTS oxygen_deliveries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    receipt_no VARCHAR(50) NOT NULL,
    delivery_date DATE NOT NULL,
    supplier VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)");

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['createDelivery'])) {
            $receiptNo = trim($_POST['receiptNo']);
            $deliveryDate = $_POST['deliveryDate'];
            $supplier = trim($_POST['supplier']);
            
            // Validate inputs
            if (empty($receiptNo) || empty($deliveryDate) || empty($supplier)) {
                throw new Exception("All fields are required");
            }
            
            $stmt = $conn->prepare("INSERT INTO oxygen_deliveries (receipt_no, delivery_date, supplier) VALUES (?, ?, ?)");
            $stmt->execute([$receiptNo, $deliveryDate, $supplier]);
        }
    } catch (Exception $e) {
        error_log($e->getMessage());
    }
}

// Fetch all deliveries
$deliveries = $conn->query("SELECT * FROM oxygen_deliveries ORDER BY delivery_date DESC")->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Oxygen Supply Management System for BIRI District Hospital">
    <title>Oxygen Supply Management - BIRI District Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- Create Delivery Card -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0"><i class="fas fa-truck me-2"></i>Create New Delivery</h3>
            </div>
            <div class="card-body">
                <form id="createDeliveryForm" method="POST">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">Delivery Receipt No.</label>
                            <input type="text" class="form-control" name="receiptNo" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Date Delivered</label>
                            <input type="date" class="form-control" name="deliveryDate" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Supplier</label>
                            <input type="text" class="form-control" name="supplier" required>
                        </div>
                        <div class="col-12">
                            <button type="submit" name="createDelivery" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Create Delivery
                            </button>
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#reportModal">
                                <i class="fas fa-file-alt me-2"></i>Generate Monthly Report
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Report Modal -->
                            <div class="modal fade" id="reportModal" tabindex="-1" aria-labelledby="reportModalLabel" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header bg-success text-white">
                                            <h5 class="modal-title" id="reportModalLabel">
                                                <i class="fas fa-calendar-alt me-2"></i>Select Report Period
                                            </h5>
                                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <form action="generate_monthly_report.php" method="GET" target="_blank">
                                            <div class="modal-body">
                                                <div class="mb-3">
                                                    <label class="form-label">Month</label>
                                                    <select class="form-select" name="month" required>
                                                        <?php
                                                        for ($m=1; $m<=12; $m++) {
                                                            $month = date('F', mktime(0,0,0,$m,1));
                                                            echo "<option value='$m'".($m == date('n') ? ' selected' : '').">$month</option>";
                                                        }
                                                        ?>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">Year</label>
                                                    <select class="form-select" name="year" required>
                                                        <?php
                                                        $currentYear = date('Y');
                                                        for ($y = $currentYear; $y >= $currentYear - 5; $y--) {
                                                            echo "<option value='$y'".($y == $currentYear ? ' selected' : '').">$y</option>";
                                                        }
                                                        ?>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                                    <i class="fas fa-times me-2"></i>Cancel
                                                </button>
                                                <button type="submit" class="btn btn-success">
                                                    <i class="fas fa-file-download me-2"></i>Generate Report
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            
        <!-- Deliveries List -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h3 class="mb-0"><i class="fas fa-list me-2"></i>Deliveries List</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Receipt No.</th>
                                <th>Date Delivered</th>
                                <th>Supplier</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($deliveries as $delivery): ?>
                            <tr>
                                <td><?= htmlspecialchars($delivery['receipt_no']) ?></td>
                                <td><?= htmlspecialchars($delivery['delivery_date']) ?></td>
                                <td><?= htmlspecialchars($delivery['supplier']) ?></td>
                                <td>
                                    <a href="manage_tanks.php?delivery_id=<?= $delivery['id'] ?>" 
                                       class="btn btn-sm btn-primary">
                                        <i class="fas fa-wind me-1"></i>Manage Tanks
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
