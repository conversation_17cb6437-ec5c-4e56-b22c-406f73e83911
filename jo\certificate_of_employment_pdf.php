<?php
require_once '../database.php';
require('../fpdf/fpdf.php');

// Get profile ID from URL
$profile_id = isset($_GET['id']) ? $_GET['id'] : null;
if (!$profile_id) {
    die('Profile ID not provided');
}

try {
    // Fetch employee profile with error handling
    $stmt = $conn->prepare("SELECT * FROM jo_profile WHERE profile_id = ?");
    $stmt->execute([$profile_id]);
    $profile = $stmt->fetch();
    
    if (!$profile) {
        die('Employee profile not found');
    }

    // Get gender pronouns based on profile
    $pronoun_subject = $profile['jo_gender'] == 'Male' ? 'he' : 'she';
    $pronoun_subject_caps = $profile['jo_gender'] == 'Male' ? 'He' : 'She';

    // First get the latest contract to get the current designation
    $stmt = $conn->prepare("
        SELECT designation 
        FROM jo_contract 
        WHERE employee_id = ? 
        ORDER BY appointment_to DESC 
        LIMIT 1");
    $stmt->execute([$profile['employee_id']]);
    $latestContract = $stmt->fetch();

    // Then fetch first and last contract dates
    $stmt = $conn->prepare("
        SELECT 
            MIN(appointment_from) as first_appointment,
            (SELECT appointment_to 
             FROM jo_contract 
             WHERE employee_id = ? 
             ORDER BY appointment_to DESC 
             LIMIT 1) as last_appointment
        FROM jo_contract 
        WHERE employee_id = ?
        GROUP BY employee_id");
    $stmt->execute([$profile['employee_id'], $profile['employee_id']]);
    $contract = $stmt->fetch();

    // Merge the designation with contract dates
    $contract['designation'] = $latestContract['designation'];

    // Check if contract is current
    $isCurrentContract = false;

    if (!empty($contract['first_appointment']) && !empty($contract['last_appointment'])) {
        $from = new DateTime($contract['first_appointment']);
        $to = new DateTime($contract['last_appointment']);
        $now = new DateTime();
        $endOfYear = new DateTime('December 31, ' . $now->format('Y'));

        $isCurrentContract = ($now <= $to && $to <= $endOfYear);
    }

    function numberToWords($number) {
        $ones = array(
            0 => '', 1 => 'ONE', 2 => 'TWO', 3 => 'THREE', 4 => 'FOUR', 
            5 => 'FIVE', 6 => 'SIX', 7 => 'SEVEN', 8 => 'EIGHT', 9 => 'NINE'
        );
        $tens = array(
            10 => 'TEN', 11 => 'ELEVEN', 12 => 'TWELVE', 13 => 'THIRTEEN', 
            14 => 'FOURTEEN', 15 => 'FIFTEEN', 16 => 'SIXTEEN', 
            17 => 'SEVENTEEN', 18 => 'EIGHTEEN', 19 => 'NINETEEN',
            20 => 'TWENTY', 30 => 'THIRTY', 40 => 'FORTY', 50 => 'FIFTY', 
            60 => 'SIXTY', 70 => 'SEVENTY', 80 => 'EIGHTY', 90 => 'NINETY'
        );
        
        if ($number == 0) {
            return 'ZERO';
        }
        
        $words = '';
        
        // Handle thousands
        if ($number >= 1000) {
            $words .= $ones[floor($number/1000)] . ' THOUSAND ';
            $number %= 1000;
        }
        
        // Handle hundreds
        if ($number >= 100) {
            $words .= $ones[floor($number/100)] . ' HUNDRED ';
            $number %= 100;
        }
        
        // Handle tens and ones
        if ($number >= 20) {
            $words .= $tens[floor($number/10)*10];
            if ($number % 10 > 0) {
                $words .= ' ' . $ones[$number % 10];
            }
        } elseif ($number >= 10) {
            $words .= $tens[$number];
        } elseif ($number > 0) {
            $words .= $ones[$number];
        }
        
        return trim($words);
    }
    // Create PDF document with standard margins (1 inch = 25.4 mm)
    $pdf = new FPDF('P', 'mm', 'A4');
    $pdf->SetMargins(25.4, 25.4, 25.4);
    $pdf->AddPage();

    // Add logos
    if (file_exists('../images/pgns.png') && file_exists('../images/bdh.png')) {
        $pdf->Image('../images/pgns.png', 35, 20, 30);
        $pdf->Image('../images/bdh.png', 140, 20, 30);
    }

    // Header section with proper spacing
    $pdf->SetFont('Times', '', 12);
    $pdf->Cell(0, 6, 'Republic of the Philippines', 0, 1, 'C');
    $pdf->Cell(0, 6, 'Province of Northern Samar', 0, 1, 'C');
    $pdf->Cell(0, 6, 'Provincial Health Office', 0, 1, 'C');
    $pdf->SetFont('Times', 'B', 16);
    $pdf->Cell(0, 8, 'BIRI DISTRICT HOSPITAL', 0, 1, 'C');
    $pdf->SetFont('Times', 'I', 11);
    $pdf->Cell(0, 6, 'Biri Northern Samar', 0, 1, 'C');

    // Certification Title with proper spacing
    $pdf->Ln(20);
    $pdf->SetFont('Times', 'B', 16);
    $pdf->Cell(0, 10, 'CERTIFICATE OF EMPLOYMENT', 0, 1, 'C');
    
    // Certification Content with legal document formatting
    $pdf->Ln(15);
    $pdf->SetFont('Times', 'B', 12);
    $pdf->MultiCell(0, 8, 'TO WHOM IT MAY CONCERN:', 0, 'L');
    
    $pdf->Ln(2);
    $pdf->SetFont('Times', '', 12);
    if ($isCurrentContract) {
        $pdf->MultiCell(0, 8, "\tThis is to CERTIFY that " . strtoupper($profile['jo_name']) . " is presently employed at BIRI DISTRICT HOSPITAL in the capacity of " . strtoupper($contract['designation']) . " under Job Order status. Based on the official records of this office, " . $pronoun_subject . " has been rendering services since " . date('F d, Y', strtotime($contract['first_appointment'])) . " up to the present.", 0, 'J');
    } else {
        $pdf->MultiCell(0, 8, "\tThis is to CERTIFY that " . strtoupper($profile['jo_name']) . " was formerly employed at BIRI DISTRICT HOSPITAL in the capacity of " . strtoupper($contract['designation']) . " under Job Order status. Based on the official records of this office, " . $pronoun_subject . " rendered services from " . date('F d, Y', strtotime($contract['first_appointment'])) . " to " . date('F d, Y', strtotime($contract['last_appointment'])) . ".", 0, 'J');
    }

    $pdf->Ln(8);
    $pdf->MultiCell(0, 8, "\tThis CERTIFICATION is issued upon request of " . strtoupper($profile['jo_name']) . " for whatever legal purpose it may serve.", 0, 'J');
    
    $pdf->Ln(15);
    $pdf->SetFont('Times', 'I', 12);
    $pdf->MultiCell(0, 8, "\tIssued this " . date('jS') . " day of " . date('F, Y') . " at Biri District Hospital, Biri, Northern Samar, Philippines.", 0, 'J');
    $pdf->SetFont('Times', '', 12);
    // Signatory with proper alignment
    $pdf->Ln(25);
    $pdf->SetFont('Times', 'B', 12);
    $pdf->Cell(0, 6, 'CHRISTEL D. BRAVO', 0, 1, 'R');
    $pdf->SetFont('Times', '', 12);
    $pdf->Cell(0, 6, 'Administrative Officer IV', 0, 1, 'R');

    // Output PDF
    $pdf->Output('Certificate_of_Employment_' . $profile['jo_name'] . '.pdf', 'D');

} catch (PDOException $e) {
    die('Database Error: ' . $e->getMessage());
} catch (Exception $e) {
    die('Error: ' . $e->getMessage());
}
