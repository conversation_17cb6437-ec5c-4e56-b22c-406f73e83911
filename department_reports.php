<?php
require_once 'database.php';

// Get current year
$currentYear = date('Y');

// Fetch department statistics
$departmentStats = $conn->query("
    SELECT 
        d.id,
        d.department_name,
        COUNT(DISTINCT e.id) as employee_count,
        COUNT(DISTINCT t.transaction_id) as transaction_count,
        COALESCE(SUM(td.amount), 0) as total_spent,
        MAX(t.transaction_date) as last_transaction_date
    FROM departments d
    LEFT JOIN employees e ON d.id = e.department_id
    LEFT JOIN transactions t ON e.id = t.employee_id
    LEFT JOIN transaction_details td ON t.transaction_id = td.transaction_id
    WHERE YEAR(t.transaction_date) = $currentYear
    GROUP BY d.id, d.department_name
    ORDER BY total_spent DESC")->fetchAll();

// Calculate department totals
$totalEmployees = 0;
$totalTransactions = 0;
$totalSpent = 0;
foreach ($departmentStats as $dept) {
    $totalEmployees += $dept['employee_count'];
    $totalTransactions += $dept['transaction_count'];
    $totalSpent += $dept['total_spent'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Department Reports - BIRI District Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
        }
        .department-metric {
            padding: 1.5rem;
            border-radius: 10px;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.03);
        }
        .metric-title {
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
        }
        .metric-value {
            font-size: 1.8rem;
            font-weight: 600;
            color: #2c3e50;
        }
        .table th {
            font-weight: 600;
            color: #6c757d;
        }
        .progress {
            height: 8px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0">
                <i class="fas fa-building me-2 text-primary"></i>
                Department Reports
            </h2>
            <a href="reports.php" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Reports
            </a>
        </div>

        <!-- Department Overview -->
        <div class="row g-4 mb-4">
            <div class="col-md-4">
                <div class="department-metric">
                    <h6 class="metric-title">Total Departments</h6>
                    <div class="metric-value"><?php echo count($departmentStats); ?></div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="department-metric">
                    <h6 class="metric-title">Total Employees</h6>
                    <div class="metric-value"><?php echo $totalEmployees; ?></div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="department-metric">
                    <h6 class="metric-title">Total Transactions</h6>
                    <div class="metric-value"><?php echo $totalTransactions; ?></div>
                </div>
            </div>
        </div>

        <!-- Department Analysis -->
        <div class="card">
            <div class="card-body">
                <h4 class="card-title mb-4">Department Analysis</h4>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Department</th>
                                <th>Employees</th>
                                <th>Transactions</th>
                                <th>Total Spent</th>
                                <th>Last Transaction</th>
                                <th>Spending Share</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($departmentStats as $dept): ?>
                                <?php 
                                    $spendingShare = ($totalSpent > 0) 
                                        ? ($dept['total_spent'] / $totalSpent) * 100 
                                        : 0;
                                ?>
                                <tr>
                                    <td class="fw-semibold"><?php echo htmlspecialchars($dept['department_name']); ?></td>
                                    <td><?php echo $dept['employee_count']; ?></td>
                                    <td><?php echo $dept['transaction_count']; ?></td>
                                    <td class="fw-semibold text-success">₱<?php echo number_format($dept['total_spent'], 2); ?></td>
                                    <td><?php echo $dept['last_transaction_date'] ? date('M d, Y', strtotime($dept['last_transaction_date'])) : 'N/A'; ?></td>
                                    <td style="width: 200px;">
                                        <div class="d-flex align-items-center">
                                            <div class="progress flex-grow-1 me-2">
                                                <div class="progress-bar bg-primary" role="progressbar"
                                                     style="width: <?php echo $spendingShare; ?>%"
                                                     aria-valuenow="<?php echo $spendingShare; ?>"
                                                     aria-valuemin="0" aria-valuemax="100">
                                                </div>
                                            </div>
                                            <span class="fw-semibold"><?php echo number_format($spendingShare, 1); ?>%</span>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
