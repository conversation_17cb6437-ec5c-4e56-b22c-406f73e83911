<?php
require_once '../database.php';

// Get monitoring statistics
$stats_sql = "SELECT 
    COUNT(CASE WHEN monitoring_status = 'Active' THEN 1 END) as active_monitoring,
    COUNT(CASE WHEN monitoring_status = 'Overdue' THEN 1 END) as overdue_monitoring,
    COUNT(CASE WHEN priority_level = 'Critical' THEN 1 END) as critical_patients,
    COUNT(CASE WHEN next_monitoring_date <= CURDATE() THEN 1 END) as due_today,
    COUNT(CASE WHEN next_monitoring_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as due_this_week
FROM patient_medication_monitoring";

$stats = $conn->query($stats_sql)->fetch(PDO::FETCH_ASSOC);

// Get alert statistics
$alert_stats_sql = "SELECT 
    COUNT(CASE WHEN is_acknowledged = 0 THEN 1 END) as unacknowledged_alerts,
    COUNT(CASE WHEN alert_priority = 'Critical' AND is_acknowledged = 0 THEN 1 END) as critical_alerts,
    COUNT(CASE WHEN alert_type = 'Beers_Criteria' AND is_acknowledged = 0 THEN 1 END) as beers_alerts
FROM medication_monitoring_alerts 
WHERE is_resolved = 0";

$alert_stats = $conn->query($alert_stats_sql)->fetch(PDO::FETCH_ASSOC);

// Get recent alerts
$recent_alerts_sql = "SELECT 
    mma.alert_id,
    mma.alert_type,
    mma.alert_priority,
    mma.alert_message,
    mma.created_at,
    p.patientname,
    p.patient_type,
    i.generaldescription as medication_name
FROM medication_monitoring_alerts mma
JOIN patient p ON mma.patientid = p.patientid
JOIN patient_medication_monitoring pmm ON mma.monitoring_id = pmm.monitoring_id
JOIN items i ON pmm.itemid = i.itemid
WHERE mma.is_acknowledged = 0
ORDER BY mma.alert_priority DESC, mma.created_at DESC
LIMIT 10";

$recent_alerts = $conn->query($recent_alerts_sql)->fetchAll(PDO::FETCH_ASSOC);

// Get patients requiring monitoring
$monitoring_due_sql = "SELECT 
    pmm.monitoring_id,
    pmm.next_monitoring_date,
    pmm.priority_level,
    pmm.monitoring_status,
    p.patientname,
    p.patient_type,
    p.birthdate,
    i.generaldescription as medication_name,
    mmc.category_name,
    mmr.frequency_days,
    d.doctorname as prescribing_doctor,
    DATEDIFF(pmm.next_monitoring_date, CURDATE()) as days_until_due
FROM patient_medication_monitoring pmm
JOIN patient p ON pmm.patientid = p.patientid
JOIN items i ON pmm.itemid = i.itemid
JOIN medication_monitoring_rules mmr ON pmm.rule_id = mmr.rule_id
JOIN medication_monitoring_categories mmc ON mmr.category_id = mmc.category_id
LEFT JOIN doctors d ON pmm.prescribing_doctor = d.doctorid
WHERE pmm.monitoring_status IN ('Active', 'Overdue')
AND pmm.next_monitoring_date <= DATE_ADD(CURDATE(), INTERVAL 14 DAY)
ORDER BY pmm.priority_level DESC, pmm.next_monitoring_date ASC
LIMIT 20";

$monitoring_due = $conn->query($monitoring_due_sql)->fetchAll(PDO::FETCH_ASSOC);

// Get category statistics
$category_stats_sql = "SELECT 
    mmc.category_name,
    mmc.risk_level,
    COUNT(pmm.monitoring_id) as active_patients,
    COUNT(CASE WHEN pmm.monitoring_status = 'Overdue' THEN 1 END) as overdue_patients,
    AVG(pmm.compliance_score) as avg_compliance
FROM medication_monitoring_categories mmc
LEFT JOIN medication_monitoring_rules mmr ON mmc.category_id = mmr.category_id
LEFT JOIN patient_medication_monitoring pmm ON mmr.rule_id = pmm.rule_id AND pmm.monitoring_status = 'Active'
GROUP BY mmc.category_id, mmc.category_name, mmc.risk_level
ORDER BY active_patients DESC";

$category_stats = $conn->query($category_stats_sql)->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medication Monitoring Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --critical-color: #8e44ad;
            --light-bg: #ecf0f1;
            --white: #ffffff;
            --text-dark: #2c3e50;
            --text-muted: #7f8c8d;
            --border-color: #bdc3c7;
            --shadow-light: 0 2px 10px rgba(44, 62, 80, 0.1);
            --shadow-medium: 0 4px 20px rgba(44, 62, 80, 0.15);
        }

        body {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-dark);
        }

        .dashboard-card {
            border: none;
            border-radius: 12px;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
            background: var(--white);
            border-left: 4px solid var(--accent-color);
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .stat-card {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-medium);
        }

        .stat-card.critical {
            background: linear-gradient(135deg, var(--critical-color) 0%, #9b59b6 100%);
        }

        .stat-card.danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
        }

        .stat-card.warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #d68910 100%);
        }

        .stat-card.success {
            background: linear-gradient(135deg, var(--success-color) 0%, #229954 100%);
        }

        .stat-value {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 0.85rem;
            opacity: 0.9;
            font-weight: 500;
        }

        .alert-item {
            border-left: 4px solid;
            padding: 12px 16px;
            margin-bottom: 8px;
            border-radius: 0 8px 8px 0;
            background: white;
            box-shadow: var(--shadow-light);
        }

        .alert-critical { border-left-color: var(--critical-color); }
        .alert-high { border-left-color: var(--danger-color); }
        .alert-medium { border-left-color: var(--warning-color); }
        .alert-low { border-left-color: var(--accent-color); }

        .monitoring-item {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            background: white;
            transition: all 0.3s ease;
        }

        .monitoring-item:hover {
            box-shadow: var(--shadow-light);
            transform: translateY(-1px);
        }

        .priority-critical { border-left: 4px solid var(--critical-color); }
        .priority-high { border-left: 4px solid var(--danger-color); }
        .priority-medium { border-left: 4px solid var(--warning-color); }
        .priority-low { border-left: 4px solid var(--accent-color); }

        .badge {
            font-weight: 600;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 0.8rem;
        }

        .table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: var(--shadow-light);
        }

        .table thead th {
            background: var(--primary-color);
            color: white;
            border: none;
            font-weight: 600;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 16px 12px;
        }

        .btn-action {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-action:hover {
            transform: translateY(-1px);
        }

        .nav-tabs .nav-link {
            border: none;
            border-radius: 8px 8px 0 0;
            color: var(--text-muted);
            font-weight: 500;
            padding: 12px 20px;
        }

        .nav-tabs .nav-link.active {
            background: var(--accent-color);
            color: white;
        }

        .tab-content {
            background: white;
            border-radius: 0 8px 8px 8px;
            padding: 20px;
            box-shadow: var(--shadow-light);
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="dashboard-card card">
                    <div class="card-body py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-0">
                                    <i class="fas fa-heartbeat me-3 text-danger"></i>
                                    Medication Monitoring Dashboard
                                </h3>
                                <p class="text-muted mb-0 small">High-risk medication monitoring and patient safety alerts</p>
                            </div>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addMonitoringModal">
                                    <i class="fas fa-plus me-1"></i>Add Monitoring
                                </button>
                                <a href="medication_monitoring_reports.php" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-chart-bar me-1"></i>Reports
                                </a>
                                <a href="pharmacydashboard.php" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-home me-1"></i>Back
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-2 col-sm-4 col-6">
                <div class="stat-card text-center">
                    <div class="stat-value"><?php echo $stats['active_monitoring'] ?? 0; ?></div>
                    <div class="stat-label">Active Monitoring</div>
                </div>
            </div>
            <div class="col-md-2 col-sm-4 col-6">
                <div class="stat-card danger text-center">
                    <div class="stat-value"><?php echo $stats['overdue_monitoring'] ?? 0; ?></div>
                    <div class="stat-label">Overdue</div>
                </div>
            </div>
            <div class="col-md-2 col-sm-4 col-6">
                <div class="stat-card critical text-center">
                    <div class="stat-value"><?php echo $stats['critical_patients'] ?? 0; ?></div>
                    <div class="stat-label">Critical Priority</div>
                </div>
            </div>
            <div class="col-md-2 col-sm-4 col-6">
                <div class="stat-card warning text-center">
                    <div class="stat-value"><?php echo $stats['due_today'] ?? 0; ?></div>
                    <div class="stat-label">Due Today</div>
                </div>
            </div>
            <div class="col-md-2 col-sm-4 col-6">
                <div class="stat-card success text-center">
                    <div class="stat-value"><?php echo $stats['due_this_week'] ?? 0; ?></div>
                    <div class="stat-label">Due This Week</div>
                </div>
            </div>
            <div class="col-md-2 col-sm-4 col-6">
                <div class="stat-card danger text-center">
                    <div class="stat-value"><?php echo $alert_stats['unacknowledged_alerts'] ?? 0; ?></div>
                    <div class="stat-label">Unread Alerts</div>
                </div>
            </div>
        </div>

        <!-- Main Content Tabs -->
        <div class="row">
            <div class="col-12">
                <ul class="nav nav-tabs mb-0" id="monitoringTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="alerts-tab" data-bs-toggle="tab" data-bs-target="#alerts" type="button" role="tab">
                            <i class="fas fa-exclamation-triangle me-2"></i>Active Alerts
                            <?php if ($alert_stats['unacknowledged_alerts'] > 0): ?>
                                <span class="badge bg-danger ms-2"><?php echo $alert_stats['unacknowledged_alerts']; ?></span>
                            <?php endif; ?>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="monitoring-tab" data-bs-toggle="tab" data-bs-target="#monitoring" type="button" role="tab">
                            <i class="fas fa-calendar-check me-2"></i>Due Monitoring
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="categories-tab" data-bs-toggle="tab" data-bs-target="#categories" type="button" role="tab">
                            <i class="fas fa-tags me-2"></i>Categories
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="beers-tab" data-bs-toggle="tab" data-bs-target="#beers" type="button" role="tab">
                            <i class="fas fa-user-clock me-2"></i>Beers Criteria
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="monitoringTabContent">
                    <!-- Active Alerts Tab -->
                    <div class="tab-pane fade show active" id="alerts" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0">
                                <i class="fas fa-bell text-danger me-2"></i>Active Safety Alerts
                            </h5>
                            <button class="btn btn-outline-primary btn-sm" onclick="acknowledgeAllAlerts()">
                                <i class="fas fa-check-double me-1"></i>Acknowledge All
                            </button>
                        </div>

                        <?php if (empty($recent_alerts)): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                No active alerts. All monitoring is up to date.
                            </div>
                        <?php else: ?>
                            <?php foreach ($recent_alerts as $alert): ?>
                                <div class="alert-item alert-<?php echo strtolower($alert['alert_priority']); ?>">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <div class="d-flex align-items-center mb-2">
                                                <span class="badge bg-<?php echo $alert['alert_priority'] === 'Critical' ? 'danger' : ($alert['alert_priority'] === 'High' ? 'warning' : 'info'); ?> me-2">
                                                    <?php echo $alert['alert_priority']; ?>
                                                </span>
                                                <span class="badge bg-secondary me-2">
                                                    <?php echo str_replace('_', ' ', $alert['alert_type']); ?>
                                                </span>
                                                <small class="text-muted">
                                                    <?php echo date('M d, Y H:i', strtotime($alert['created_at'])); ?>
                                                </small>
                                            </div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($alert['patientname']); ?></h6>
                                            <p class="mb-1 text-muted small">
                                                <strong>Medication:</strong> <?php echo htmlspecialchars($alert['medication_name']); ?>
                                            </p>
                                            <p class="mb-0"><?php echo htmlspecialchars($alert['alert_message']); ?></p>
                                        </div>
                                        <div class="ms-3">
                                            <button class="btn btn-outline-success btn-sm" onclick="acknowledgeAlert(<?php echo $alert['alert_id']; ?>)">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-outline-primary btn-sm" onclick="viewPatientMonitoring(<?php echo $alert['alert_id']; ?>)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>

                    <!-- Due Monitoring Tab -->
                    <div class="tab-pane fade" id="monitoring" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0">
                                <i class="fas fa-calendar-alt text-warning me-2"></i>Patients Requiring Monitoring
                            </h5>
                            <div class="btn-group" role="group">
                                <button class="btn btn-outline-primary btn-sm" onclick="filterMonitoring('all')">All</button>
                                <button class="btn btn-outline-danger btn-sm" onclick="filterMonitoring('overdue')">Overdue</button>
                                <button class="btn btn-outline-warning btn-sm" onclick="filterMonitoring('today')">Due Today</button>
                                <button class="btn btn-outline-info btn-sm" onclick="filterMonitoring('week')">This Week</button>
                            </div>
                        </div>

                        <?php if (empty($monitoring_due)): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                No patients require monitoring in the next 14 days.
                            </div>
                        <?php else: ?>
                            <?php foreach ($monitoring_due as $monitoring): ?>
                                <?php
                                $age = '';
                                if (!empty($monitoring['birthdate'])) {
                                    $birthDate = new DateTime($monitoring['birthdate']);
                                    $today = new DateTime();
                                    $age = $birthDate->diff($today)->y;
                                }

                                $status_class = '';
                                if ($monitoring['days_until_due'] < 0) {
                                    $status_class = 'danger';
                                } elseif ($monitoring['days_until_due'] == 0) {
                                    $status_class = 'warning';
                                } else {
                                    $status_class = 'info';
                                }
                                ?>
                                <div class="monitoring-item priority-<?php echo strtolower($monitoring['priority_level']); ?>"
                                     data-status="<?php echo $monitoring['days_until_due'] < 0 ? 'overdue' : ($monitoring['days_until_due'] == 0 ? 'today' : 'upcoming'); ?>">
                                    <div class="row align-items-center">
                                        <div class="col-md-4">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($monitoring['patientname']); ?></h6>
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars($monitoring['patient_type']); ?>
                                                <?php if ($age): ?> • <?php echo $age; ?> years old<?php endif; ?>
                                            </small>
                                        </div>
                                        <div class="col-md-3">
                                            <strong><?php echo htmlspecialchars($monitoring['medication_name']); ?></strong>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($monitoring['category_name']); ?></small>
                                        </div>
                                        <div class="col-md-2 text-center">
                                            <span class="badge bg-<?php echo $status_class; ?>">
                                                <?php if ($monitoring['days_until_due'] < 0): ?>
                                                    <?php echo abs($monitoring['days_until_due']); ?> days overdue
                                                <?php elseif ($monitoring['days_until_due'] == 0): ?>
                                                    Due today
                                                <?php else: ?>
                                                    <?php echo $monitoring['days_until_due']; ?> days
                                                <?php endif; ?>
                                            </span>
                                        </div>
                                        <div class="col-md-2 text-center">
                                            <span class="badge bg-<?php echo $monitoring['priority_level'] === 'Critical' ? 'danger' : ($monitoring['priority_level'] === 'High' ? 'warning' : 'info'); ?>">
                                                <?php echo $monitoring['priority_level']; ?>
                                            </span>
                                        </div>
                                        <div class="col-md-1 text-end">
                                            <div class="btn-group-vertical" role="group">
                                                <button class="btn btn-primary btn-sm" onclick="recordMonitoring(<?php echo $monitoring['monitoring_id']; ?>)">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                                <button class="btn btn-outline-secondary btn-sm" onclick="viewHistory(<?php echo $monitoring['monitoring_id']; ?>)">
                                                    <i class="fas fa-history"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>

                    <!-- Categories Tab -->
                    <div class="tab-pane fade" id="categories" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0">
                                <i class="fas fa-layer-group text-info me-2"></i>Monitoring Categories
                            </h5>
                        </div>

                        <div class="row">
                            <?php foreach ($category_stats as $category): ?>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="dashboard-card card h-100">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="card-title mb-0"><?php echo htmlspecialchars($category['category_name']); ?></h6>
                                                <span class="badge bg-<?php echo $category['risk_level'] === 'Critical' ? 'danger' : ($category['risk_level'] === 'High' ? 'warning' : 'info'); ?>">
                                                    <?php echo $category['risk_level']; ?>
                                                </span>
                                            </div>
                                            <div class="row text-center">
                                                <div class="col-4">
                                                    <div class="stat-value text-primary" style="font-size: 1.5rem;"><?php echo $category['active_patients'] ?? 0; ?></div>
                                                    <div class="stat-label small">Active</div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="stat-value text-danger" style="font-size: 1.5rem;"><?php echo $category['overdue_patients'] ?? 0; ?></div>
                                                    <div class="stat-label small">Overdue</div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="stat-value text-success" style="font-size: 1.5rem;"><?php echo number_format($category['avg_compliance'] ?? 0, 1); ?>%</div>
                                                    <div class="stat-label small">Compliance</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Beers Criteria Tab -->
                    <div class="tab-pane fade" id="beers" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0">
                                <i class="fas fa-exclamation-circle text-warning me-2"></i>Beers Criteria Monitoring
                            </h5>
                            <div class="alert alert-warning mb-0 py-2 px-3">
                                <small><i class="fas fa-info-circle me-1"></i>Special monitoring for potentially inappropriate medications in older adults</small>
                            </div>
                        </div>

                        <?php
                        // Get Beers Criteria patients
                        $beers_sql = "SELECT
                            pmm.monitoring_id,
                            pmm.next_monitoring_date,
                            pmm.priority_level,
                            p.patientname,
                            p.birthdate,
                            i.generaldescription as medication_name,
                            DATEDIFF(CURDATE(), p.birthdate) / 365.25 as age,
                            DATEDIFF(pmm.next_monitoring_date, CURDATE()) as days_until_due
                        FROM patient_medication_monitoring pmm
                        JOIN patient p ON pmm.patientid = p.patientid
                        JOIN items i ON pmm.itemid = i.itemid
                        JOIN medication_monitoring_rules mmr ON pmm.rule_id = mmr.rule_id
                        JOIN medication_monitoring_categories mmc ON mmr.category_id = mmc.category_id
                        WHERE mmc.beers_criteria = 1
                        AND pmm.monitoring_status = 'Active'
                        ORDER BY p.birthdate ASC";

                        $beers_patients = $conn->query($beers_sql)->fetchAll(PDO::FETCH_ASSOC);
                        ?>

                        <?php if (empty($beers_patients)): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                No patients currently on Beers Criteria medications requiring monitoring.
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Patient</th>
                                            <th>Age</th>
                                            <th>Medication</th>
                                            <th>Next Monitoring</th>
                                            <th>Priority</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($beers_patients as $patient): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($patient['patientname']); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $patient['age'] >= 75 ? 'danger' : 'warning'; ?>">
                                                        <?php echo number_format($patient['age'], 0); ?> years
                                                    </span>
                                                </td>
                                                <td><?php echo htmlspecialchars($patient['medication_name']); ?></td>
                                                <td>
                                                    <?php
                                                    $status_class = $patient['days_until_due'] < 0 ? 'danger' : ($patient['days_until_due'] == 0 ? 'warning' : 'info');
                                                    ?>
                                                    <span class="badge bg-<?php echo $status_class; ?>">
                                                        <?php echo date('M d, Y', strtotime($patient['next_monitoring_date'])); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php echo $patient['priority_level'] === 'Critical' ? 'danger' : 'warning'; ?>">
                                                        <?php echo $patient['priority_level']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <button class="btn btn-primary btn-sm" onclick="recordMonitoring(<?php echo $patient['monitoring_id']; ?>)">
                                                        <i class="fas fa-plus me-1"></i>Record
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Filter monitoring items
        function filterMonitoring(filter) {
            const items = document.querySelectorAll('.monitoring-item');
            items.forEach(item => {
                const status = item.dataset.status;
                switch(filter) {
                    case 'all':
                        item.style.display = '';
                        break;
                    case 'overdue':
                        item.style.display = status === 'overdue' ? '' : 'none';
                        break;
                    case 'today':
                        item.style.display = status === 'today' ? '' : 'none';
                        break;
                    case 'week':
                        item.style.display = status !== 'overdue' ? '' : 'none';
                        break;
                }
            });
        }

        // Acknowledge alert
        function acknowledgeAlert(alertId) {
            if (confirm('Mark this alert as acknowledged?')) {
                fetch('medication_monitoring_actions.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'acknowledge_alert',
                        alert_id: alertId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while acknowledging the alert.');
                });
            }
        }

        // Acknowledge all alerts
        function acknowledgeAllAlerts() {
            if (confirm('Mark all alerts as acknowledged?')) {
                fetch('medication_monitoring_actions.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'acknowledge_all_alerts'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while acknowledging alerts.');
                });
            }
        }

        // Record monitoring
        function recordMonitoring(monitoringId) {
            window.location.href = `medication_monitoring_record.php?id=${monitoringId}`;
        }

        // View monitoring history
        function viewHistory(monitoringId) {
            window.location.href = `medication_monitoring_history.php?id=${monitoringId}`;
        }

        // View patient monitoring details
        function viewPatientMonitoring(alertId) {
            window.location.href = `medication_monitoring_patient.php?alert_id=${alertId}`;
        }

        // Auto-refresh every 5 minutes
        setInterval(() => {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
